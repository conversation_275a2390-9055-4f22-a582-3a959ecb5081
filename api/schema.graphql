### This file was generated by Nexus Schema
### Do not make changes to this file directly


type Address {
  CreatedByUser: UsersPermissionsUser
  UpdatedByUser: UsersPermissionsUser
  address: JSON
  createdAt: DateTime
  documentId: ID!
  publishedAt: DateTime
  updatedAt: DateTime
}

type AddressEntity {
  attributes: Address
  id: ID
}

type AddressEntityResponse {
  data: Address
}

type AddressEntityResponseCollection {
  nodes: [Address!]!
  pageInfo: Pagination!
}

input AddressFiltersInput {
  CreatedByUser: UsersPermissionsUserFiltersInput
  UpdatedByUser: UsersPermissionsUserFiltersInput
  address: JSONFilterInput
  and: [AddressFiltersInput]
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  not: AddressFiltersInput
  or: [AddressFiltersInput]
  publishedAt: DateTimeFilterInput
  updatedAt: DateTimeFilterInput
}

input AddressInput {
  CreatedByUser: ID
  UpdatedByUser: ID
  address: JSON
  publishedAt: DateTime
}

type AddressRelationResponseCollection {
  nodes: [Address!]!
}

input BooleanFilterInput {
  and: [<PERSON><PERSON>an]
  between: [<PERSON>olean]
  contains: Boolean
  containsi: Boolean
  endsWith: Boolean
  eq: Boolean
  eqi: Boolean
  gt: Boolean
  gte: Boolean
  in: [Boolean]
  lt: Boolean
  lte: Boolean
  ne: Boolean
  nei: Boolean
  not: BooleanFilterInput
  notContains: Boolean
  notContainsi: Boolean
  notIn: [Boolean]
  notNull: Boolean
  null: Boolean
  or: [Boolean]
  startsWith: Boolean
}

type CallSession {
  Patient: Patient
  attempts: Int
  callSid: String
  callerNumber: String
  createdAt: DateTime
  documentId: ID!
  language: ENUM_CALLSESSION_LANGUAGE
  publishedAt: DateTime
  twilioBody: JSON
  updatedAt: DateTime
}

type CallSessionEntity {
  attributes: CallSession
  id: ID
}

type CallSessionEntityResponse {
  data: CallSession
}

type CallSessionEntityResponseCollection {
  nodes: [CallSession!]!
  pageInfo: Pagination!
}

input CallSessionFiltersInput {
  Patient: PatientFiltersInput
  and: [CallSessionFiltersInput]
  attempts: IntFilterInput
  callSid: StringFilterInput
  callerNumber: StringFilterInput
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  language: StringFilterInput
  not: CallSessionFiltersInput
  or: [CallSessionFiltersInput]
  publishedAt: DateTimeFilterInput
  twilioBody: JSONFilterInput
  updatedAt: DateTimeFilterInput
}

input CallSessionInput {
  Patient: ID
  attempts: Int
  callSid: String
  callerNumber: String
  language: ENUM_CALLSESSION_LANGUAGE
  publishedAt: DateTime
  twilioBody: JSON
}

type CallSessionRelationResponseCollection {
  nodes: [CallSession!]!
}

type Client {
  CreatedByUser: UsersPermissionsUser
  Groups(filters: GroupFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [Group]!
  Groups_connection(filters: GroupFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): GroupRelationResponseCollection
  Sites(filters: SiteFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [Site]!
  Sites_connection(filters: SiteFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): SiteRelationResponseCollection
  UpdatedByUser: UsersPermissionsUser
  contact: String!
  createdAt: DateTime
  documentId: ID!
  name: String!
  phone: String
  publishedAt: DateTime
  updatedAt: DateTime
}

type ClientEntity {
  attributes: Client
  id: ID
}

type ClientEntityResponse {
  data: Client
}

type ClientEntityResponseCollection {
  nodes: [Client!]!
  pageInfo: Pagination!
}

input ClientFiltersInput {
  CreatedByUser: UsersPermissionsUserFiltersInput
  Groups: GroupFiltersInput
  Sites: SiteFiltersInput
  UpdatedByUser: UsersPermissionsUserFiltersInput
  and: [ClientFiltersInput]
  contact: StringFilterInput
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  name: StringFilterInput
  not: ClientFiltersInput
  or: [ClientFiltersInput]
  phone: StringFilterInput
  publishedAt: DateTimeFilterInput
  updatedAt: DateTimeFilterInput
}

input ClientInput {
  CreatedByUser: ID
  Groups: [ID]
  Sites: [ID]
  UpdatedByUser: ID
  contact: String
  name: String
  phone: String
  publishedAt: DateTime
}

type ClientRelationResponseCollection {
  nodes: [Client!]!
}

type Continent {
  CreatedByUser: UsersPermissionsUser
  Regions(filters: RegionFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [Region]!
  Regions_connection(filters: RegionFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): RegionRelationResponseCollection
  Status: Status
  UpdatedByUser: UsersPermissionsUser
  createdAt: DateTime
  documentId: ID!
  name: String!
  publishedAt: DateTime
  updatedAt: DateTime
}

type ContinentEntity {
  attributes: Continent
  id: ID
}

type ContinentEntityResponse {
  data: Continent
}

type ContinentEntityResponseCollection {
  nodes: [Continent!]!
  pageInfo: Pagination!
}

input ContinentFiltersInput {
  CreatedByUser: UsersPermissionsUserFiltersInput
  Regions: RegionFiltersInput
  Status: StatusFiltersInput
  UpdatedByUser: UsersPermissionsUserFiltersInput
  and: [ContinentFiltersInput]
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  name: StringFilterInput
  not: ContinentFiltersInput
  or: [ContinentFiltersInput]
  publishedAt: DateTimeFilterInput
  updatedAt: DateTimeFilterInput
}

input ContinentInput {
  CreatedByUser: ID
  Regions: [ID]
  Status: ID
  UpdatedByUser: ID
  name: String
  publishedAt: DateTime
}

type ContinentRelationResponseCollection {
  nodes: [Continent!]!
}

type Country {
  CreatedByUser: UsersPermissionsUser
  Region: Region
  States(filters: StateFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [State]!
  States_connection(filters: StateFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): StateRelationResponseCollection
  Status: Status
  UpdatedByUser: UsersPermissionsUser
  code: String!
  codeThree: String!
  createdAt: DateTime
  documentId: ID!
  name: String!
  publishedAt: DateTime
  updatedAt: DateTime
}

type CountryEntity {
  attributes: Country
  id: ID
}

type CountryEntityResponse {
  data: Country
}

type CountryEntityResponseCollection {
  nodes: [Country!]!
  pageInfo: Pagination!
}

input CountryFiltersInput {
  CreatedByUser: UsersPermissionsUserFiltersInput
  Region: RegionFiltersInput
  States: StateFiltersInput
  Status: StatusFiltersInput
  UpdatedByUser: UsersPermissionsUserFiltersInput
  and: [CountryFiltersInput]
  code: StringFilterInput
  codeThree: StringFilterInput
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  name: StringFilterInput
  not: CountryFiltersInput
  or: [CountryFiltersInput]
  publishedAt: DateTimeFilterInput
  updatedAt: DateTimeFilterInput
}

input CountryInput {
  CreatedByUser: ID
  Region: ID
  States: [ID]
  Status: ID
  UpdatedByUser: ID
  code: String
  codeThree: String
  name: String
  publishedAt: DateTime
}

type CountryRelationResponseCollection {
  nodes: [Country!]!
}

input CustomViewSettingsInput {
  name: String!
  settings: JSON!
}

"""
A date string, such as 2007-12-03, compliant with the `full-date` format outlined in section 5.6 of the RFC 3339 profile of the ISO 8601 standard for representation of dates and times using the Gregorian calendar.
"""
scalar Date

input DateFilterInput {
  and: [Date]
  between: [Date]
  contains: Date
  containsi: Date
  endsWith: Date
  eq: Date
  eqi: Date
  gt: Date
  gte: Date
  in: [Date]
  lt: Date
  lte: Date
  ne: Date
  nei: Date
  not: DateFilterInput
  notContains: Date
  notContainsi: Date
  notIn: [Date]
  notNull: Boolean
  null: Boolean
  or: [Date]
  startsWith: Date
}

"""
A date-time string at UTC, such as 2007-12-03T10:15:30Z, compliant with the `date-time` format outlined in section 5.6 of the RFC 3339 profile of the ISO 8601 standard for representation of dates and times using the Gregorian calendar.
"""
scalar DateTime

input DateTimeFilterInput {
  and: [DateTime]
  between: [DateTime]
  contains: DateTime
  containsi: DateTime
  endsWith: DateTime
  eq: DateTime
  eqi: DateTime
  gt: DateTime
  gte: DateTime
  in: [DateTime]
  lt: DateTime
  lte: DateTime
  ne: DateTime
  nei: DateTime
  not: DateTimeFilterInput
  notContains: DateTime
  notContainsi: DateTime
  notIn: [DateTime]
  notNull: Boolean
  null: Boolean
  or: [DateTime]
  startsWith: DateTime
}

type DeleteMutationResponse {
  documentId: ID!
}

enum ENUM_CALLSESSION_LANGUAGE {
  en
  es
}

type Error {
  code: String!
  message: String
}

input FileInfoInput {
  alternativeText: String
  caption: String
  name: String
}

input FloatFilterInput {
  and: [Float]
  between: [Float]
  contains: Float
  containsi: Float
  endsWith: Float
  eq: Float
  eqi: Float
  gt: Float
  gte: Float
  in: [Float]
  lt: Float
  lte: Float
  ne: Float
  nei: Float
  not: FloatFilterInput
  notContains: Float
  notContainsi: Float
  notIn: [Float]
  notNull: Boolean
  null: Boolean
  or: [Float]
  startsWith: Float
}

union GenericMorph = Address | CallSession | Client | Continent | Country | Group | I18NLocale | MasterData | MasterDataCategory | Patient | Region | ReviewWorkflowsWorkflow | ReviewWorkflowsWorkflowStage | Site | State | Status | UploadFile | UsersPermissionsPermission | UsersPermissionsRole | UsersPermissionsUser | ViewSetting

type Group {
  Client: Client
  CreatedByUser: UsersPermissionsUser
  Patients(filters: PatientFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [Patient]!
  Patients_connection(filters: PatientFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): PatientRelationResponseCollection
  UpdatedByUser: UsersPermissionsUser
  createdAt: DateTime
  documentId: ID!
  name: String!
  publishedAt: DateTime
  updatedAt: DateTime
}

type GroupEntity {
  attributes: Group
  id: ID
}

type GroupEntityResponse {
  data: Group
}

type GroupEntityResponseCollection {
  nodes: [Group!]!
  pageInfo: Pagination!
}

input GroupFiltersInput {
  Client: ClientFiltersInput
  CreatedByUser: UsersPermissionsUserFiltersInput
  Patients: PatientFiltersInput
  UpdatedByUser: UsersPermissionsUserFiltersInput
  and: [GroupFiltersInput]
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  name: StringFilterInput
  not: GroupFiltersInput
  or: [GroupFiltersInput]
  publishedAt: DateTimeFilterInput
  updatedAt: DateTimeFilterInput
}

input GroupInput {
  Client: ID
  CreatedByUser: ID
  Patients: [ID]
  UpdatedByUser: ID
  name: String
  publishedAt: DateTime
}

type GroupRelationResponseCollection {
  nodes: [Group!]!
}

type I18NLocale {
  code: String
  createdAt: DateTime
  documentId: ID!
  name: String
  publishedAt: DateTime
  updatedAt: DateTime
}

"""A string used to identify an i18n locale"""
scalar I18NLocaleCode

type I18NLocaleEntity {
  attributes: I18NLocale
  id: ID
}

type I18NLocaleEntityResponse {
  data: I18NLocale
}

type I18NLocaleEntityResponseCollection {
  nodes: [I18NLocale!]!
  pageInfo: Pagination!
}

input I18NLocaleFiltersInput {
  and: [I18NLocaleFiltersInput]
  code: StringFilterInput
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  name: StringFilterInput
  not: I18NLocaleFiltersInput
  or: [I18NLocaleFiltersInput]
  publishedAt: DateTimeFilterInput
  updatedAt: DateTimeFilterInput
}

input I18NLocaleInput {
  code: String
  name: String
  publishedAt: DateTime
}

type I18NLocaleRelationResponseCollection {
  nodes: [I18NLocale!]!
}

input IDFilterInput {
  and: [ID]
  between: [ID]
  contains: ID
  containsi: ID
  endsWith: ID
  eq: ID
  eqi: ID
  gt: ID
  gte: ID
  in: [ID]
  lt: ID
  lte: ID
  ne: ID
  nei: ID
  not: IDFilterInput
  notContains: ID
  notContainsi: ID
  notIn: [ID]
  notNull: Boolean
  null: Boolean
  or: [ID]
  startsWith: ID
}

input IntFilterInput {
  and: [Int]
  between: [Int]
  contains: Int
  containsi: Int
  endsWith: Int
  eq: Int
  eqi: Int
  gt: Int
  gte: Int
  in: [Int]
  lt: Int
  lte: Int
  ne: Int
  nei: Int
  not: IntFilterInput
  notContains: Int
  notContainsi: Int
  notIn: [Int]
  notNull: Boolean
  null: Boolean
  or: [Int]
  startsWith: Int
}

"""
The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf).
"""
scalar JSON @specifiedBy(url: "http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf")

input JSONFilterInput {
  and: [JSON]
  between: [JSON]
  contains: JSON
  containsi: JSON
  endsWith: JSON
  eq: JSON
  eqi: JSON
  gt: JSON
  gte: JSON
  in: [JSON]
  lt: JSON
  lte: JSON
  ne: JSON
  nei: JSON
  not: JSONFilterInput
  notContains: JSON
  notContainsi: JSON
  notIn: [JSON]
  notNull: Boolean
  null: Boolean
  or: [JSON]
  startsWith: JSON
}

"""
The `BigInt` scalar type represents non-fractional signed whole numeric values.
"""
scalar Long

input LongFilterInput {
  and: [Long]
  between: [Long]
  contains: Long
  containsi: Long
  endsWith: Long
  eq: Long
  eqi: Long
  gt: Long
  gte: Long
  in: [Long]
  lt: Long
  lte: Long
  ne: Long
  nei: Long
  not: LongFilterInput
  notContains: Long
  notContainsi: Long
  notIn: [Long]
  notNull: Boolean
  null: Boolean
  or: [Long]
  startsWith: Long
}

type MasterData {
  Category: MasterDataCategory
  CreatedByUser: UsersPermissionsUser
  UpdatedByUser: UsersPermissionsUser
  createdAt: DateTime
  documentId: ID!
  publishedAt: DateTime
  updatedAt: DateTime
  value: String!
}

type MasterDataCategory {
  CreatedByUser: UsersPermissionsUser
  MasterData(filters: MasterDataFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [MasterData]!
  MasterData_connection(filters: MasterDataFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): MasterDataRelationResponseCollection
  UpdatedByUser: UsersPermissionsUser
  createdAt: DateTime
  documentId: ID!
  name: String!
  publishedAt: DateTime
  updatedAt: DateTime
}

type MasterDataCategoryEntity {
  attributes: MasterDataCategory
  id: ID
}

type MasterDataCategoryEntityResponse {
  data: MasterDataCategory
}

type MasterDataCategoryEntityResponseCollection {
  nodes: [MasterDataCategory!]!
  pageInfo: Pagination!
}

input MasterDataCategoryFiltersInput {
  CreatedByUser: UsersPermissionsUserFiltersInput
  MasterData: MasterDataFiltersInput
  UpdatedByUser: UsersPermissionsUserFiltersInput
  and: [MasterDataCategoryFiltersInput]
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  name: StringFilterInput
  not: MasterDataCategoryFiltersInput
  or: [MasterDataCategoryFiltersInput]
  publishedAt: DateTimeFilterInput
  updatedAt: DateTimeFilterInput
}

input MasterDataCategoryInput {
  CreatedByUser: ID
  MasterData: [ID]
  UpdatedByUser: ID
  name: String
  publishedAt: DateTime
}

type MasterDataCategoryRelationResponseCollection {
  nodes: [MasterDataCategory!]!
}

type MasterDataEntity {
  attributes: MasterData
  id: ID
}

type MasterDataEntityResponse {
  data: MasterData
}

type MasterDataEntityResponseCollection {
  nodes: [MasterData!]!
  pageInfo: Pagination!
}

input MasterDataFiltersInput {
  Category: MasterDataCategoryFiltersInput
  CreatedByUser: UsersPermissionsUserFiltersInput
  UpdatedByUser: UsersPermissionsUserFiltersInput
  and: [MasterDataFiltersInput]
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  not: MasterDataFiltersInput
  or: [MasterDataFiltersInput]
  publishedAt: DateTimeFilterInput
  updatedAt: DateTimeFilterInput
  value: StringFilterInput
}

input MasterDataInput {
  Category: ID
  CreatedByUser: ID
  UpdatedByUser: ID
  publishedAt: DateTime
  value: String
}

type MasterDataRelationResponseCollection {
  nodes: [MasterData!]!
}

type Mutation {
  """Change user password. Confirm with the current password."""
  changePassword(currentPassword: String!, password: String!, passwordConfirmation: String!): UsersPermissionsLoginPayload
  createAddress(data: AddressInput!, status: PublicationStatus = PUBLISHED): Address
  createCallSession(data: CallSessionInput!, status: PublicationStatus = PUBLISHED): CallSession
  createClient(data: ClientInput!, status: PublicationStatus = PUBLISHED): Client
  createContinent(data: ContinentInput!, status: PublicationStatus = PUBLISHED): Continent
  createCountry(data: CountryInput!, status: PublicationStatus = PUBLISHED): Country
  createGroup(data: GroupInput!, status: PublicationStatus = PUBLISHED): Group
  createMasterData(data: MasterDataInput!, status: PublicationStatus = PUBLISHED): MasterData
  createMasterDataCategory(data: MasterDataCategoryInput!, status: PublicationStatus = PUBLISHED): MasterDataCategory
  createPatient(data: PatientInput!, status: PublicationStatus = PUBLISHED): Patient
  createRegion(data: RegionInput!, status: PublicationStatus = PUBLISHED): Region
  createReviewWorkflowsWorkflow(data: ReviewWorkflowsWorkflowInput!, status: PublicationStatus = PUBLISHED): ReviewWorkflowsWorkflow
  createReviewWorkflowsWorkflowStage(data: ReviewWorkflowsWorkflowStageInput!, status: PublicationStatus = PUBLISHED): ReviewWorkflowsWorkflowStage
  createSite(data: SiteInput!, status: PublicationStatus = PUBLISHED): Site
  createState(data: StateInput!, status: PublicationStatus = PUBLISHED): State
  createStatus(data: StatusInput!, status: PublicationStatus = PUBLISHED): Status

  """Create a new role"""
  createUsersPermissionsRole(data: UsersPermissionsRoleInput!): UsersPermissionsCreateRolePayload

  """Create a new user"""
  createUsersPermissionsUser(data: UsersPermissionsUserInput!): UsersPermissionsUserEntityResponse!
  createViewSetting(data: CustomViewSettingsInput!, documentId: ID, status: PublicationStatus = PUBLISHED): ViewSetting
  deleteAddress(documentId: ID!): DeleteMutationResponse
  deleteCallSession(documentId: ID!): DeleteMutationResponse
  deleteClient(documentId: ID!): DeleteMutationResponse
  deleteContinent(documentId: ID!): DeleteMutationResponse
  deleteCountry(documentId: ID!): DeleteMutationResponse
  deleteGroup(documentId: ID!): DeleteMutationResponse
  deleteMasterData(documentId: ID!): DeleteMutationResponse
  deleteMasterDataCategory(documentId: ID!): DeleteMutationResponse
  deletePatient(documentId: ID!): DeleteMutationResponse
  deleteRegion(documentId: ID!): DeleteMutationResponse
  deleteReviewWorkflowsWorkflow(documentId: ID!): DeleteMutationResponse
  deleteReviewWorkflowsWorkflowStage(documentId: ID!): DeleteMutationResponse
  deleteSite(documentId: ID!): DeleteMutationResponse
  deleteState(documentId: ID!): DeleteMutationResponse
  deleteStatus(documentId: ID!): DeleteMutationResponse
  deleteUploadFile(id: ID!): UploadFile

  """Delete an existing role"""
  deleteUsersPermissionsRole(id: ID!): UsersPermissionsDeleteRolePayload

  """Delete an existing user"""
  deleteUsersPermissionsUser(id: ID!): UsersPermissionsUserEntityResponse!
  deleteViewSetting(documentId: ID, name: String!): DeleteMutationResponse

  """Confirm an email users email address"""
  emailConfirmation(confirmation: String!): UsersPermissionsLoginPayload

  """Request a reset password token"""
  forgotPassword(email: String!): UsersPermissionsPasswordPayload
  login(input: UsersPermissionsLoginInput!): UsersPermissionsLoginPayload!

  """Register a user"""
  register(input: UsersPermissionsRegisterInput!): UsersPermissionsLoginPayload!

  """
  Reset user password. Confirm with a code (resetToken from forgotPassword)
  """
  resetPassword(code: String!, password: String!, passwordConfirmation: String!): UsersPermissionsLoginPayload
  updateAddress(data: AddressInput!, documentId: ID!, status: PublicationStatus = PUBLISHED): Address
  updateCallSession(data: CallSessionInput!, documentId: ID!, status: PublicationStatus = PUBLISHED): CallSession
  updateClient(data: ClientInput!, documentId: ID!, status: PublicationStatus = PUBLISHED): Client
  updateContinent(data: ContinentInput!, documentId: ID!, status: PublicationStatus = PUBLISHED): Continent
  updateCountry(data: CountryInput!, documentId: ID!, status: PublicationStatus = PUBLISHED): Country
  updateGroup(data: GroupInput!, documentId: ID!, status: PublicationStatus = PUBLISHED): Group
  updateMasterData(data: MasterDataInput!, documentId: ID!, status: PublicationStatus = PUBLISHED): MasterData
  updateMasterDataCategory(data: MasterDataCategoryInput!, documentId: ID!, status: PublicationStatus = PUBLISHED): MasterDataCategory
  updatePatient(data: PatientInput!, documentId: ID!, status: PublicationStatus = PUBLISHED): Patient
  updateRegion(data: RegionInput!, documentId: ID!, status: PublicationStatus = PUBLISHED): Region
  updateReviewWorkflowsWorkflow(data: ReviewWorkflowsWorkflowInput!, documentId: ID!, status: PublicationStatus = PUBLISHED): ReviewWorkflowsWorkflow
  updateReviewWorkflowsWorkflowStage(data: ReviewWorkflowsWorkflowStageInput!, documentId: ID!, status: PublicationStatus = PUBLISHED): ReviewWorkflowsWorkflowStage
  updateSite(data: SiteInput!, documentId: ID!, status: PublicationStatus = PUBLISHED): Site
  updateState(data: StateInput!, documentId: ID!, status: PublicationStatus = PUBLISHED): State
  updateStatus(data: StatusInput!, documentId: ID!, status: PublicationStatus = PUBLISHED): Status
  updateUploadFile(id: ID!, info: FileInfoInput): UploadFile!

  """Update an existing role"""
  updateUsersPermissionsRole(data: UsersPermissionsRoleInput!, id: ID!): UsersPermissionsUpdateRolePayload

  """Update an existing user"""
  updateUsersPermissionsUser(data: UsersPermissionsUserInput!, id: ID!): UsersPermissionsUserEntityResponse!
  updateViewSetting(data: CustomViewSettingsInput!, documentId: ID, status: PublicationStatus = PUBLISHED): ViewSetting
}

type Pagination {
  page: Int!
  pageCount: Int!
  pageSize: Int!
  total: Int!
}

input PaginationArg {
  limit: Int
  page: Int
  pageSize: Int
  start: Int
}

type Patient {
  CallSessions(filters: CallSessionFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [CallSession]!
  CallSessions_connection(filters: CallSessionFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): CallSessionRelationResponseCollection
  CreatedByUser: UsersPermissionsUser
  Group: Group
  Race: MasterData
  Sex: MasterData
  UpdatedByUser: UsersPermissionsUser
  contact: String
  createdAt: DateTime
  dob: Date
  documentId: ID!
  firstName: String!
  lastName: String!
  namePrefix: String
  nameSuffix: String
  phone: String!
  pid: String!
  publishedAt: DateTime
  ssn: String!
  updatedAt: DateTime
}

type PatientEntity {
  attributes: Patient
  id: ID
}

type PatientEntityResponse {
  data: Patient
}

type PatientEntityResponseCollection {
  nodes: [Patient!]!
  pageInfo: Pagination!
}

input PatientFiltersInput {
  CallSessions: CallSessionFiltersInput
  CreatedByUser: UsersPermissionsUserFiltersInput
  Group: GroupFiltersInput
  Race: MasterDataFiltersInput
  Sex: MasterDataFiltersInput
  UpdatedByUser: UsersPermissionsUserFiltersInput
  and: [PatientFiltersInput]
  contact: StringFilterInput
  createdAt: DateTimeFilterInput
  dob: DateFilterInput
  documentId: IDFilterInput
  firstName: StringFilterInput
  lastName: StringFilterInput
  namePrefix: StringFilterInput
  nameSuffix: StringFilterInput
  not: PatientFiltersInput
  or: [PatientFiltersInput]
  phone: StringFilterInput
  pid: StringFilterInput
  publishedAt: DateTimeFilterInput
  ssn: StringFilterInput
  updatedAt: DateTimeFilterInput
}

input PatientInput {
  CallSessions: [ID]
  CreatedByUser: ID
  Group: ID
  Race: ID
  Sex: ID
  UpdatedByUser: ID
  contact: String
  dob: Date
  firstName: String
  lastName: String
  namePrefix: String
  nameSuffix: String
  phone: String
  pid: String
  publishedAt: DateTime
  ssn: String
}

type PatientRelationResponseCollection {
  nodes: [Patient!]!
}

enum PublicationStatus {
  DRAFT
  PUBLISHED
}

type Query {
  address(documentId: ID!, status: PublicationStatus = PUBLISHED): Address
  addresses(filters: AddressFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): [Address]!
  addresses_connection(filters: AddressFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): AddressEntityResponseCollection
  callSession(documentId: ID!, status: PublicationStatus = PUBLISHED): CallSession
  callSessions(filters: CallSessionFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): [CallSession]!
  callSessions_connection(filters: CallSessionFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): CallSessionEntityResponseCollection
  client(documentId: ID!, status: PublicationStatus = PUBLISHED): Client
  clients(filters: ClientFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): [Client]!
  clients_connection(filters: ClientFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): ClientEntityResponseCollection
  continent(documentId: ID!, status: PublicationStatus = PUBLISHED): Continent
  continents(filters: ContinentFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): [Continent]!
  continents_connection(filters: ContinentFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): ContinentEntityResponseCollection
  countries(filters: CountryFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): [Country]!
  countries_connection(filters: CountryFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): CountryEntityResponseCollection
  country(documentId: ID!, status: PublicationStatus = PUBLISHED): Country
  group(documentId: ID!, status: PublicationStatus = PUBLISHED): Group
  groups(filters: GroupFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): [Group]!
  groups_connection(filters: GroupFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): GroupEntityResponseCollection
  i18NLocale(documentId: ID!, status: PublicationStatus = PUBLISHED): I18NLocale
  i18NLocales(filters: I18NLocaleFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): [I18NLocale]!
  i18NLocales_connection(filters: I18NLocaleFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): I18NLocaleEntityResponseCollection
  masterData(documentId: ID!, status: PublicationStatus = PUBLISHED): MasterData
  masterDataCategories(filters: MasterDataCategoryFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): [MasterDataCategory]!
  masterDataCategories_connection(filters: MasterDataCategoryFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): MasterDataCategoryEntityResponseCollection
  masterDataCategory(documentId: ID!, status: PublicationStatus = PUBLISHED): MasterDataCategory
  masterDatas(filters: MasterDataFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): [MasterData]!
  masterDatas_connection(filters: MasterDataFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): MasterDataEntityResponseCollection
  me: UsersPermissionsMe
  patient(documentId: ID!, status: PublicationStatus = PUBLISHED): Patient
  patients(filters: PatientFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): [Patient]!
  patients_connection(filters: PatientFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): PatientEntityResponseCollection
  region(documentId: ID!, status: PublicationStatus = PUBLISHED): Region
  regions(filters: RegionFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): [Region]!
  regions_connection(filters: RegionFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): RegionEntityResponseCollection
  reviewWorkflowsWorkflow(documentId: ID!, status: PublicationStatus = PUBLISHED): ReviewWorkflowsWorkflow
  reviewWorkflowsWorkflowStage(documentId: ID!, status: PublicationStatus = PUBLISHED): ReviewWorkflowsWorkflowStage
  reviewWorkflowsWorkflowStages(filters: ReviewWorkflowsWorkflowStageFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): [ReviewWorkflowsWorkflowStage]!
  reviewWorkflowsWorkflowStages_connection(filters: ReviewWorkflowsWorkflowStageFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): ReviewWorkflowsWorkflowStageEntityResponseCollection
  reviewWorkflowsWorkflows(filters: ReviewWorkflowsWorkflowFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): [ReviewWorkflowsWorkflow]!
  reviewWorkflowsWorkflows_connection(filters: ReviewWorkflowsWorkflowFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): ReviewWorkflowsWorkflowEntityResponseCollection
  site(documentId: ID!, status: PublicationStatus = PUBLISHED): Site
  sites(filters: SiteFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): [Site]!
  sites_connection(filters: SiteFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): SiteEntityResponseCollection
  state(documentId: ID!, status: PublicationStatus = PUBLISHED): State
  states(filters: StateFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): [State]!
  states_connection(filters: StateFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): StateEntityResponseCollection
  status(documentId: ID!, status: PublicationStatus = PUBLISHED): Status
  statuses(filters: StatusFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): [Status]!
  statuses_connection(filters: StatusFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): StatusEntityResponseCollection
  uploadFile(documentId: ID!, status: PublicationStatus = PUBLISHED): UploadFile
  uploadFiles(filters: UploadFileFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): [UploadFile]!
  uploadFiles_connection(filters: UploadFileFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): UploadFileEntityResponseCollection
  usersPermissionsRole(documentId: ID!, status: PublicationStatus = PUBLISHED): UsersPermissionsRole
  usersPermissionsRoles(filters: UsersPermissionsRoleFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): [UsersPermissionsRole]!
  usersPermissionsRoles_connection(filters: UsersPermissionsRoleFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): UsersPermissionsRoleEntityResponseCollection
  usersPermissionsUser(documentId: ID!, status: PublicationStatus = PUBLISHED): UsersPermissionsUser
  usersPermissionsUsers(filters: UsersPermissionsUserFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): [UsersPermissionsUser]!
  usersPermissionsUsers_connection(filters: UsersPermissionsUserFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): UsersPermissionsUserEntityResponseCollection
  viewSetting(documentId: ID!, status: PublicationStatus = PUBLISHED): ViewSetting
  viewSettings(filters: ViewSettingFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): [ViewSetting]!
  viewSettings_connection(filters: ViewSettingFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): ViewSettingEntityResponseCollection
}

type Region {
  Continent: Continent
  Countries(filters: CountryFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [Country]!
  Countries_connection(filters: CountryFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): CountryRelationResponseCollection
  CreatedByUser: UsersPermissionsUser
  Status: Status
  UpdatedByUser: UsersPermissionsUser
  createdAt: DateTime
  documentId: ID!
  name: String!
  publishedAt: DateTime
  updatedAt: DateTime
}

type RegionEntity {
  attributes: Region
  id: ID
}

type RegionEntityResponse {
  data: Region
}

type RegionEntityResponseCollection {
  nodes: [Region!]!
  pageInfo: Pagination!
}

input RegionFiltersInput {
  Continent: ContinentFiltersInput
  Countries: CountryFiltersInput
  CreatedByUser: UsersPermissionsUserFiltersInput
  Status: StatusFiltersInput
  UpdatedByUser: UsersPermissionsUserFiltersInput
  and: [RegionFiltersInput]
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  name: StringFilterInput
  not: RegionFiltersInput
  or: [RegionFiltersInput]
  publishedAt: DateTimeFilterInput
  updatedAt: DateTimeFilterInput
}

input RegionInput {
  Continent: ID
  Countries: [ID]
  CreatedByUser: ID
  Status: ID
  UpdatedByUser: ID
  name: String
  publishedAt: DateTime
}

type RegionRelationResponseCollection {
  nodes: [Region!]!
}

type ResponseCollectionMeta {
  pagination: Pagination!
}

type ReviewWorkflowsWorkflow {
  contentTypes: JSON!
  createdAt: DateTime
  documentId: ID!
  name: String!
  publishedAt: DateTime
  stageRequiredToPublish: ReviewWorkflowsWorkflowStage
  stages(filters: ReviewWorkflowsWorkflowStageFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ReviewWorkflowsWorkflowStage]!
  stages_connection(filters: ReviewWorkflowsWorkflowStageFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): ReviewWorkflowsWorkflowStageRelationResponseCollection
  updatedAt: DateTime
}

type ReviewWorkflowsWorkflowEntity {
  attributes: ReviewWorkflowsWorkflow
  id: ID
}

type ReviewWorkflowsWorkflowEntityResponse {
  data: ReviewWorkflowsWorkflow
}

type ReviewWorkflowsWorkflowEntityResponseCollection {
  nodes: [ReviewWorkflowsWorkflow!]!
  pageInfo: Pagination!
}

input ReviewWorkflowsWorkflowFiltersInput {
  and: [ReviewWorkflowsWorkflowFiltersInput]
  contentTypes: JSONFilterInput
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  name: StringFilterInput
  not: ReviewWorkflowsWorkflowFiltersInput
  or: [ReviewWorkflowsWorkflowFiltersInput]
  publishedAt: DateTimeFilterInput
  stageRequiredToPublish: ReviewWorkflowsWorkflowStageFiltersInput
  stages: ReviewWorkflowsWorkflowStageFiltersInput
  updatedAt: DateTimeFilterInput
}

input ReviewWorkflowsWorkflowInput {
  contentTypes: JSON
  name: String
  publishedAt: DateTime
  stageRequiredToPublish: ID
  stages: [ID]
}

type ReviewWorkflowsWorkflowRelationResponseCollection {
  nodes: [ReviewWorkflowsWorkflow!]!
}

type ReviewWorkflowsWorkflowStage {
  color: String
  createdAt: DateTime
  documentId: ID!
  name: String
  publishedAt: DateTime
  updatedAt: DateTime
  workflow: ReviewWorkflowsWorkflow
}

type ReviewWorkflowsWorkflowStageEntity {
  attributes: ReviewWorkflowsWorkflowStage
  id: ID
}

type ReviewWorkflowsWorkflowStageEntityResponse {
  data: ReviewWorkflowsWorkflowStage
}

type ReviewWorkflowsWorkflowStageEntityResponseCollection {
  nodes: [ReviewWorkflowsWorkflowStage!]!
  pageInfo: Pagination!
}

input ReviewWorkflowsWorkflowStageFiltersInput {
  and: [ReviewWorkflowsWorkflowStageFiltersInput]
  color: StringFilterInput
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  name: StringFilterInput
  not: ReviewWorkflowsWorkflowStageFiltersInput
  or: [ReviewWorkflowsWorkflowStageFiltersInput]
  publishedAt: DateTimeFilterInput
  updatedAt: DateTimeFilterInput
  workflow: ReviewWorkflowsWorkflowFiltersInput
}

input ReviewWorkflowsWorkflowStageInput {
  color: String
  name: String
  publishedAt: DateTime
  workflow: ID
}

type ReviewWorkflowsWorkflowStageRelationResponseCollection {
  nodes: [ReviewWorkflowsWorkflowStage!]!
}

type Site {
  Address: Address
  Client: Client
  CreatedByUser: UsersPermissionsUser
  UpdatedByUser: UsersPermissionsUser
  blockoutDates: JSON
  businessHours: JSON!
  contact: String!
  createdAt: DateTime
  documentId: ID!
  name: String!
  phone: String!
  publishedAt: DateTime
  updatedAt: DateTime
}

type SiteEntity {
  attributes: Site
  id: ID
}

type SiteEntityResponse {
  data: Site
}

type SiteEntityResponseCollection {
  nodes: [Site!]!
  pageInfo: Pagination!
}

input SiteFiltersInput {
  Address: AddressFiltersInput
  Client: ClientFiltersInput
  CreatedByUser: UsersPermissionsUserFiltersInput
  UpdatedByUser: UsersPermissionsUserFiltersInput
  and: [SiteFiltersInput]
  blockoutDates: JSONFilterInput
  businessHours: JSONFilterInput
  contact: StringFilterInput
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  name: StringFilterInput
  not: SiteFiltersInput
  or: [SiteFiltersInput]
  phone: StringFilterInput
  publishedAt: DateTimeFilterInput
  updatedAt: DateTimeFilterInput
}

input SiteInput {
  Address: ID
  Client: ID
  CreatedByUser: ID
  UpdatedByUser: ID
  blockoutDates: JSON
  businessHours: JSON
  contact: String
  name: String
  phone: String
  publishedAt: DateTime
}

type SiteRelationResponseCollection {
  nodes: [Site!]!
}

type State {
  Country: Country
  CreatedByUser: UsersPermissionsUser
  Status: Status
  UpdatedByUser: UsersPermissionsUser
  code: String!
  createdAt: DateTime
  documentId: ID!
  name: String!
  publishedAt: DateTime
  updatedAt: DateTime
}

type StateEntity {
  attributes: State
  id: ID
}

type StateEntityResponse {
  data: State
}

type StateEntityResponseCollection {
  nodes: [State!]!
  pageInfo: Pagination!
}

input StateFiltersInput {
  Country: CountryFiltersInput
  CreatedByUser: UsersPermissionsUserFiltersInput
  Status: StatusFiltersInput
  UpdatedByUser: UsersPermissionsUserFiltersInput
  and: [StateFiltersInput]
  code: StringFilterInput
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  name: StringFilterInput
  not: StateFiltersInput
  or: [StateFiltersInput]
  publishedAt: DateTimeFilterInput
  updatedAt: DateTimeFilterInput
}

input StateInput {
  Country: ID
  CreatedByUser: ID
  Status: ID
  UpdatedByUser: ID
  code: String
  name: String
  publishedAt: DateTime
}

type StateRelationResponseCollection {
  nodes: [State!]!
}

type Status {
  CreatedByUser: UsersPermissionsUser
  Name: String!
  UpdatedByUser: UsersPermissionsUser
  createdAt: DateTime
  documentId: ID!
  publishedAt: DateTime
  updatedAt: DateTime
}

type StatusEntity {
  attributes: Status
  id: ID
}

type StatusEntityResponse {
  data: Status
}

type StatusEntityResponseCollection {
  nodes: [Status!]!
  pageInfo: Pagination!
}

input StatusFiltersInput {
  CreatedByUser: UsersPermissionsUserFiltersInput
  Name: StringFilterInput
  UpdatedByUser: UsersPermissionsUserFiltersInput
  and: [StatusFiltersInput]
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  not: StatusFiltersInput
  or: [StatusFiltersInput]
  publishedAt: DateTimeFilterInput
  updatedAt: DateTimeFilterInput
}

input StatusInput {
  CreatedByUser: ID
  Name: String
  UpdatedByUser: ID
  publishedAt: DateTime
}

type StatusRelationResponseCollection {
  nodes: [Status!]!
}

input StringFilterInput {
  and: [String]
  between: [String]
  contains: String
  containsi: String
  endsWith: String
  eq: String
  eqi: String
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nei: String
  not: StringFilterInput
  notContains: String
  notContainsi: String
  notIn: [String]
  notNull: Boolean
  null: Boolean
  or: [String]
  startsWith: String
}

"""A time string with format HH:mm:ss.SSS"""
scalar Time

input TimeFilterInput {
  and: [Time]
  between: [Time]
  contains: Time
  containsi: Time
  endsWith: Time
  eq: Time
  eqi: Time
  gt: Time
  gte: Time
  in: [Time]
  lt: Time
  lte: Time
  ne: Time
  nei: Time
  not: TimeFilterInput
  notContains: Time
  notContainsi: Time
  notIn: [Time]
  notNull: Boolean
  null: Boolean
  or: [Time]
  startsWith: Time
}

type UploadFile {
  alternativeText: String
  caption: String
  createdAt: DateTime
  documentId: ID!
  ext: String
  formats: JSON
  hash: String!
  height: Int
  mime: String!
  name: String!
  previewUrl: String
  provider: String!
  provider_metadata: JSON
  publishedAt: DateTime
  related: [GenericMorph]
  size: Float!
  updatedAt: DateTime
  url: String!
  width: Int
}

type UploadFileEntity {
  attributes: UploadFile
  id: ID
}

type UploadFileEntityResponse {
  data: UploadFile
}

type UploadFileEntityResponseCollection {
  nodes: [UploadFile!]!
  pageInfo: Pagination!
}

input UploadFileFiltersInput {
  alternativeText: StringFilterInput
  and: [UploadFileFiltersInput]
  caption: StringFilterInput
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  ext: StringFilterInput
  formats: JSONFilterInput
  hash: StringFilterInput
  height: IntFilterInput
  mime: StringFilterInput
  name: StringFilterInput
  not: UploadFileFiltersInput
  or: [UploadFileFiltersInput]
  previewUrl: StringFilterInput
  provider: StringFilterInput
  provider_metadata: JSONFilterInput
  publishedAt: DateTimeFilterInput
  size: FloatFilterInput
  updatedAt: DateTimeFilterInput
  url: StringFilterInput
  width: IntFilterInput
}

input UploadFileInput {
  alternativeText: String
  caption: String
  ext: String
  formats: JSON
  hash: String
  height: Int
  mime: String
  name: String
  previewUrl: String
  provider: String
  provider_metadata: JSON
  publishedAt: DateTime
  size: Float
  url: String
  width: Int
}

type UploadFileRelationResponseCollection {
  nodes: [UploadFile!]!
}

type UsersPermissionsCreateRolePayload {
  ok: Boolean!
}

type UsersPermissionsDeleteRolePayload {
  ok: Boolean!
}

input UsersPermissionsLoginInput {
  identifier: String!
  password: String!
  provider: String! = "local"
}

type UsersPermissionsLoginPayload {
  jwt: String
  user: UsersPermissionsMe!
}

type UsersPermissionsMe {
  blocked: Boolean
  confirmed: Boolean
  documentId: ID!
  email: String
  id: ID!
  role: UsersPermissionsMeRole
  username: String!
}

type UsersPermissionsMeRole {
  description: String
  id: ID!
  name: String!
  type: String
}

type UsersPermissionsPasswordPayload {
  ok: Boolean!
}

type UsersPermissionsPermission {
  action: String!
  createdAt: DateTime
  documentId: ID!
  publishedAt: DateTime
  role: UsersPermissionsRole
  updatedAt: DateTime
}

type UsersPermissionsPermissionEntity {
  attributes: UsersPermissionsPermission
  id: ID
}

type UsersPermissionsPermissionEntityResponse {
  data: UsersPermissionsPermission
}

type UsersPermissionsPermissionEntityResponseCollection {
  nodes: [UsersPermissionsPermission!]!
  pageInfo: Pagination!
}

input UsersPermissionsPermissionFiltersInput {
  action: StringFilterInput
  and: [UsersPermissionsPermissionFiltersInput]
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  not: UsersPermissionsPermissionFiltersInput
  or: [UsersPermissionsPermissionFiltersInput]
  publishedAt: DateTimeFilterInput
  role: UsersPermissionsRoleFiltersInput
  updatedAt: DateTimeFilterInput
}

input UsersPermissionsPermissionInput {
  action: String
  publishedAt: DateTime
  role: ID
}

type UsersPermissionsPermissionRelationResponseCollection {
  nodes: [UsersPermissionsPermission!]!
}

input UsersPermissionsRegisterInput {
  email: String!
  password: String!
  username: String!
}

type UsersPermissionsRole {
  createdAt: DateTime
  description: String
  documentId: ID!
  name: String!
  permissions(filters: UsersPermissionsPermissionFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [UsersPermissionsPermission]!
  permissions_connection(filters: UsersPermissionsPermissionFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): UsersPermissionsPermissionRelationResponseCollection
  publishedAt: DateTime
  type: String
  updatedAt: DateTime
  users(filters: UsersPermissionsUserFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [UsersPermissionsUser]!
  users_connection(filters: UsersPermissionsUserFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): UsersPermissionsUserRelationResponseCollection
}

type UsersPermissionsRoleEntity {
  attributes: UsersPermissionsRole
  id: ID
}

type UsersPermissionsRoleEntityResponse {
  data: UsersPermissionsRole
}

type UsersPermissionsRoleEntityResponseCollection {
  nodes: [UsersPermissionsRole!]!
  pageInfo: Pagination!
}

input UsersPermissionsRoleFiltersInput {
  and: [UsersPermissionsRoleFiltersInput]
  createdAt: DateTimeFilterInput
  description: StringFilterInput
  documentId: IDFilterInput
  name: StringFilterInput
  not: UsersPermissionsRoleFiltersInput
  or: [UsersPermissionsRoleFiltersInput]
  permissions: UsersPermissionsPermissionFiltersInput
  publishedAt: DateTimeFilterInput
  type: StringFilterInput
  updatedAt: DateTimeFilterInput
  users: UsersPermissionsUserFiltersInput
}

input UsersPermissionsRoleInput {
  description: String
  name: String
  permissions: [ID]
  publishedAt: DateTime
  type: String
  users: [ID]
}

type UsersPermissionsRoleRelationResponseCollection {
  nodes: [UsersPermissionsRole!]!
}

type UsersPermissionsUpdateRolePayload {
  ok: Boolean!
}

type UsersPermissionsUser {
  blocked: Boolean
  confirmed: Boolean
  createdAt: DateTime
  documentId: ID!
  email: String!
  provider: String
  publishedAt: DateTime
  role: UsersPermissionsRole
  updatedAt: DateTime
  username: String!
}

type UsersPermissionsUserEntity {
  attributes: UsersPermissionsUser
  id: ID
}

type UsersPermissionsUserEntityResponse {
  data: UsersPermissionsUser
}

type UsersPermissionsUserEntityResponseCollection {
  nodes: [UsersPermissionsUser!]!
  pageInfo: Pagination!
}

input UsersPermissionsUserFiltersInput {
  and: [UsersPermissionsUserFiltersInput]
  blocked: BooleanFilterInput
  confirmed: BooleanFilterInput
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  email: StringFilterInput
  not: UsersPermissionsUserFiltersInput
  or: [UsersPermissionsUserFiltersInput]
  provider: StringFilterInput
  publishedAt: DateTimeFilterInput
  role: UsersPermissionsRoleFiltersInput
  updatedAt: DateTimeFilterInput
  username: StringFilterInput
}

input UsersPermissionsUserInput {
  blocked: Boolean
  confirmed: Boolean
  email: String
  password: String
  provider: String
  publishedAt: DateTime
  role: ID
  username: String
}

type UsersPermissionsUserRelationResponseCollection {
  nodes: [UsersPermissionsUser!]!
}

type ViewSetting {
  CreatedByUser: UsersPermissionsUser
  UpdatedByUser: UsersPermissionsUser
  User: UsersPermissionsUser
  createdAt: DateTime
  documentId: ID!
  name: String
  publishedAt: DateTime
  settings: JSON
  updatedAt: DateTime
}

type ViewSettingEntity {
  attributes: ViewSetting
  id: ID
}

type ViewSettingEntityResponse {
  data: ViewSetting
}

type ViewSettingEntityResponseCollection {
  nodes: [ViewSetting!]!
  pageInfo: Pagination!
}

input ViewSettingFiltersInput {
  CreatedByUser: UsersPermissionsUserFiltersInput
  UpdatedByUser: UsersPermissionsUserFiltersInput
  User: UsersPermissionsUserFiltersInput
  and: [ViewSettingFiltersInput]
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  name: StringFilterInput
  not: ViewSettingFiltersInput
  or: [ViewSettingFiltersInput]
  publishedAt: DateTimeFilterInput
  settings: JSONFilterInput
  updatedAt: DateTimeFilterInput
}

input ViewSettingInput {
  CreatedByUser: ID
  UpdatedByUser: ID
  User: ID
  name: String
  publishedAt: DateTime
  settings: JSON
}

type ViewSettingRelationResponseCollection {
  nodes: [ViewSetting!]!
}