export default ({ env }: { env: any }) => ({
	host: env('HOST', '0.0.0.0'),
	port: env.int('PORT', 1337),
	app: {
		keys: env.array('APP_KEYS'),
	},
	proxy: true,
	// custom configuration TODO:maybe separate this into a separate file if it gets too big
	appName: env('APP_NAME', 'Emerald App'),
	publicUrl: env('PUBLIC_URL', 'http://localhost:1337'),

	frontendUrl: env('FRONTEND_URL', 'http://localhost:3000'),

	twilio: {
		accountSid: env('TWILIO_ACCOUNT_SID', ''),
		authToken: env('TWILIO_AUTH_TOKEN', ''),
		fromNumber: env('TWILIO_PHONE_NUMBER', ''),
	},
})
