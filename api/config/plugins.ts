export default () => ({
	email: {
		config: {
			provider: 'mailgun',
			providerOptions: {
				key: process.env.MAILGUN_KEY,
				domain: process.env.MAILGUN_DOMAIN,
				url: process.env.MAILGUN_URL,
			},
			settings: {
				defaultFrom: `${process.env.APP_NAME} <${process.env.MAILGUN_DEFAULT_FROM}>`,
				defaultReplyTo: process.env.MAILGUN_DEFAULT_REPLY_TO,
			},
		},
	},
	'users-permissions': {
		config: {
			ratelimit: {
				interval: 60000,
				max: 100000,
			},
		},
	},
	graphql: {
		enabled: true,
		config: {
			// set this to true if you want to enable the playground in production
			landingPage: true,
			generateArtifacts: true,
			defaultLimit: 100,
			artifacts: {
				schema: true,
			},
		},
	},
})
