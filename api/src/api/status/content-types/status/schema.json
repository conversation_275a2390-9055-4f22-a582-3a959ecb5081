{"kind": "collectionType", "collectionName": "statuses", "info": {"singularName": "status", "pluralName": "statuses", "displayName": "Status"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"Name": {"type": "string", "required": true}, "CreatedByUser": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "configurable": false}, "UpdatedByUser": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "configurable": false}}}