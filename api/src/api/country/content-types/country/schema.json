{"kind": "collectionType", "collectionName": "countries", "info": {"singularName": "country", "pluralName": "countries", "displayName": "Country", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"CreatedByUser": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "configurable": false}, "UpdatedByUser": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "configurable": false}, "name": {"type": "string", "required": true}, "code": {"type": "string", "required": true, "unique": false}, "Status": {"type": "relation", "relation": "oneToOne", "target": "api::status.status"}, "codeThree": {"type": "string", "required": true}, "States": {"type": "relation", "relation": "oneToMany", "target": "api::state.state", "mappedBy": "Country"}, "Region": {"type": "relation", "relation": "manyToOne", "target": "api::region.region", "inversedBy": "Countries"}}}