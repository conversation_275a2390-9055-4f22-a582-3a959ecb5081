{"kind": "collectionType", "collectionName": "clients", "info": {"singularName": "client", "pluralName": "clients", "displayName": "Client", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"CreatedByUser": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "configurable": false}, "UpdatedByUser": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "configurable": false}, "name": {"type": "string", "required": true, "unique": true}, "contact": {"type": "email", "required": true}, "phone": {"type": "string", "required": false}, "Sites": {"type": "relation", "relation": "oneToMany", "target": "api::site.site", "mappedBy": "Client"}, "Groups": {"type": "relation", "relation": "oneToMany", "target": "api::group.group", "mappedBy": "Client"}}}