export default {
	routes: [
		{
			method: 'POST',
			path: '/twilio/gather-pin',
			handler: 'twilio.gatherPin',
			config: {
				policies: [],
				middlewares: ['global::twilio-signature'],
				auth: false,
			},
		},
		{
			method: 'POST',
			path: '/twilio/language-selection',
			handler: 'twilio.languageSelection',
			config: {
				policies: [],
				middlewares: ['global::twilio-signature'],
				auth: false,
			},
		},
		{
			method: 'POST',
			path: '/twilio/verify-pin',
			handler: 'twilio.verifyPin',
			config: {
				policies: [],
				middlewares: ['global::twilio-signature'],
				auth: false,
			},
		},
	],
}
