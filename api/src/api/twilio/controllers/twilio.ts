import TwilioSDK from 'twilio'
import type { Context } from 'koa'
import { TwilioCallData, TwilioGatherPayload } from '../../../../types/types'
import bcrypt from 'bcryptjs'
import { omit } from 'lodash'
import VoiceResponse = TwilioSDK.twiml.VoiceResponse

const SENSITIVE_TWILIO_FIELDS: Array<keyof TwilioCallData> = ['CallToken', 'AccountSid']

export default {
	async gatherPin(ctx: Context): Promise<void> {
		const twiml = new VoiceResponse()
		const body = ctx.request.body as TwilioCallData

		try {
			const existingSession = await strapi.documents('api::call-session.call-session').findFirst({
				filters: { callSid: body.CallSid },
			})

			if (!existingSession) {
				const publicUrl = strapi.config.get('server.publicUrl') as string
				const appName = strapi.config.get('server.appName') as string

				const languageGather = twiml.gather({
					numDigits: 1,
					action: `${publicUrl}/api/twilio/language-selection`,
					method: 'POST',
					timeout: 6,
				})

				languageGather.say(`Welcome to ${appName}. For English, press 1. Para español, presione 2.`)
				twiml.say('We did not receive any input. Goodbye.')
				twiml.hangup()

				ctx.set('Content-Type', 'text/xml')
				ctx.body = twiml.toString()
				return
			} else {
				if ((existingSession?.attempts ?? 0) >= 3) {
					strapi.log.warn(`Maximum attempts reached for CallSid: ${body.CallSid}`)
					twiml.hangup()
					ctx.set('Content-Type', 'text/xml')
					ctx.body = twiml.toString()
					return
				}
				strapi.log.info(`Retry attempt for CallSid: ${body.CallSid}, current attempts: ${existingSession.attempts}`)
			}

			const publicUrl = strapi.config.get('server.publicUrl') as string

			const gather = twiml.gather({
				numDigits: 4,
				action: `${publicUrl}/api/twilio/verify-pin`,
				method: 'POST',
				finishOnKey: '#',
				timeout: 10,
			})

			const selectedLanguage = existingSession.language || 'en'

			if (selectedLanguage === 'es') {
				gather.say('Por favor ingrese su PIN de cuatro dígitos, seguido del símbolo numeral.')
			} else {
				gather.say('Please enter your four-digit PIN, followed by the pound sign.')
			}

			twiml.say('We did not receive any input. Goodbye.')
			twiml.hangup()

			ctx.set('Content-Type', 'text/xml')
			ctx.body = twiml.toString()
		} catch (error) {
			strapi.log.error(error)
			ctx.throw(500, 'Internal Server Error')
		}
	},

	async languageSelection(ctx: Context): Promise<void> {
		const { Digits: selectedLanguage, CallSid, From: callerNumber } = ctx.request.body as TwilioGatherPayload
		const twiml = new VoiceResponse()

		try {
			if (selectedLanguage !== '1' && selectedLanguage !== '2') {
				const publicUrl = strapi.config.get('server.publicUrl') as string

				const languageGather = twiml.gather({
					numDigits: 1,
					action: `${publicUrl}/api/twilio/language-selection`,
					method: 'POST',
					timeout: 6,
				})

				languageGather.say('Invalid selection. For English, press 1. Para español, presione 2.')
				twiml.say('We did not receive any input. Goodbye.')
				twiml.hangup()

				ctx.set('Content-Type', 'text/xml')
				ctx.body = twiml.toString()
				return
			}

			const language = selectedLanguage === '2' ? 'es' : 'en'

			const patient = await strapi.documents('api::patient.patient').findFirst({
				filters: { phone: callerNumber },
			})

			if (!patient) {
				if (language === 'es') {
					twiml.say('No pudimos encontrar su cuenta. Adiós.')
				} else {
					twiml.say('We could not find your account. Goodbye.')
				}
				twiml.hangup()

				await strapi.documents('api::call-session.call-session').create({
					data: {
						callSid: CallSid,
						attempts: 0,
						callerNumber,
						twilioBody: sanitizeTwilioBody(ctx.request.body),
						language: language,
					},
				})

				ctx.set('Content-Type', 'text/xml')
				ctx.body = twiml.toString()
				return
			}

			await strapi.documents('api::call-session.call-session').create({
				data: {
					callSid: CallSid,
					attempts: 0,
					callerNumber,
					twilioBody: sanitizeTwilioBody(ctx.request.body),
					Patient: patient.documentId,
					language: language,
				},
			})

			const publicUrl = strapi.config.get('server.publicUrl') as string
			const gather = twiml.gather({
				numDigits: 4,
				action: `${publicUrl}/api/twilio/verify-pin`,
				method: 'POST',
				finishOnKey: '#',
				timeout: 10,
			})

			if (language === 'es') {
				gather.say('Por favor ingrese su PIN de cuatro dígitos, seguido del símbolo numeral.')
				twiml.say('No recibimos ninguna entrada. Adiós.')
			} else {
				gather.say('Please enter your four-digit PIN, followed by the pound sign.')
				twiml.say('We did not receive any input. Goodbye.')
			}

			twiml.hangup()
			ctx.set('Content-Type', 'text/xml')
			ctx.body = twiml.toString()
		} catch (error) {
			strapi.log.error('Error in language selection:', error)
			twiml.say('System error. Please try calling again later. Goodbye.')
			twiml.hangup()
			ctx.set('Content-Type', 'text/xml')
			ctx.body = twiml.toString()
		}
	},

	async verifyPin(ctx: Context): Promise<void> {
		const { Digits: enteredPin, CallSid, From: callerNumber } = ctx.request.body as TwilioGatherPayload
		const twiml = new VoiceResponse()

		try {
			let session = await strapi.documents('api::call-session.call-session').findFirst({
				filters: { callSid: { $eq: CallSid } },
			})

			if (!session) {
				session = await strapi.documents('api::call-session.call-session').create({
					data: {
						callSid: CallSid,
						attempts: 0,
						callerNumber,
						twilioBody: sanitizeTwilioBody(ctx.request.body),
					},
				})
			}

			const sessionDocId = session.documentId
			const attempts = session.attempts || 0
			const selectedLanguage = session.language || 'en'

			const patient = await strapi.documents('api::patient.patient').findFirst({
				filters: { phone: { $eq: callerNumber } },
			})

			if (!patient) {
				if (selectedLanguage === 'es') {
					twiml.say('No pudimos encontrar su cuenta. Adiós.')
				} else {
					twiml.say('We could not find your account. Goodbye.')
				}
				twiml.hangup()
				ctx.set('Content-Type', 'text/xml')
				ctx.body = twiml.toString()
				return
			}

			const isMatch = await bcrypt.compare(enteredPin, patient.pin as string)
			if (!isMatch) {
				const newAttempts = attempts + 1

				await strapi.documents('api::call-session.call-session').update({
					documentId: sessionDocId,
					data: { attempts: newAttempts },
				})

				if (newAttempts >= 3) {
					if (selectedLanguage === 'es') {
						twiml.say('Demasiados intentos. Por favor intente más tarde. Adiós.')
					} else {
						twiml.say('Too many attempts reached. Please try again later. Goodbye.')
					}
					twiml.hangup()
				} else {
					const remainingAttempts = 3 - newAttempts

					if (selectedLanguage === 'es') {
						twiml.say(`PIN incorrecto. Intente de nuevo. Le quedan ${remainingAttempts} intento${remainingAttempts > 1 ? 's' : ''}.`)
					} else {
						twiml.say(`Incorrect PIN. Try again. You have ${remainingAttempts} attempt${remainingAttempts > 1 ? 's' : ''} left.`)
					}

					// Redirect back to gather-pin to collect PIN again
					const publicUrl = strapi.config.get('server.publicUrl') as string
					const gatherUrl = `${publicUrl}/api/twilio/gather-pin`
					twiml.redirect(gatherUrl)
				}

				ctx.set('Content-Type', 'text/xml')
				ctx.body = twiml.toString()
				return
			}

			strapi.log.info(`Successful PIN verification for patient: ${patient.documentId}`)

			if (selectedLanguage === 'es') {
				twiml.say(`Hola ${patient.firstName}. Su identidad ha sido verificada. Gracias por llamar.`)
			} else {
				twiml.say(`Hello ${patient.firstName}. Your identity has been verified. Thank you for calling.`)
			}
			twiml.hangup()
			ctx.set('Content-Type', 'text/xml')
			ctx.body = twiml.toString()
		} catch (error) {
			strapi.log.error(error)
			ctx.throw(500, 'Internal Server Error')
		}
	},
}

function sanitizeTwilioBody(twilioBody: any): any {
	return omit(twilioBody, SENSITIVE_TWILIO_FIELDS)
}
