{"kind": "collectionType", "collectionName": "patients", "info": {"singularName": "patient", "pluralName": "patients", "displayName": "Patient", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"CreatedByUser": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "configurable": false}, "UpdatedByUser": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "configurable": false}, "firstName": {"type": "string", "required": true}, "lastName": {"type": "string", "required": true}, "nameSuffix": {"type": "string"}, "namePrefix": {"type": "string"}, "ssn": {"type": "string", "required": true, "unique": true}, "pid": {"type": "string", "unique": true, "required": true}, "Group": {"type": "relation", "relation": "manyToOne", "target": "api::group.group", "inversedBy": "Patients"}, "Race": {"type": "relation", "relation": "oneToOne", "target": "api::master-data.master-data"}, "Sex": {"type": "relation", "relation": "oneToOne", "target": "api::master-data.master-data"}, "dob": {"type": "date"}, "pin": {"type": "password", "private": true}, "phone": {"type": "string", "required": true}, "contact": {"type": "email"}, "CallSessions": {"type": "relation", "relation": "oneToMany", "target": "api::call-session.call-session", "mappedBy": "Patient"}}}