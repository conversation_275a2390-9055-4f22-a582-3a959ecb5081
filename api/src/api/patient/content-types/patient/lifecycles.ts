import { randomInt } from 'crypto'
import { LifecycleEvent } from '../../../../../types/types'

import TwilioService from '../../../../twilio'

const PIN_LENGTH = 4
const HASH_ROUNDS = 10

function generateRawPin(len = PIN_LENGTH): string {
	let pin = ''
	while (pin.length < len) {
		pin += randomInt(0, 10).toString()
	}
	return pin
}

export default {
	async beforeCreate(event: LifecycleEvent<'api::patient.patient'>): Promise<void> {
		const { data } = event.params
		// if (!data.phone) {
		// 	throw new Error('Phone number is required for patient creation.')
		// }
		//
		// const rawPin = generateRawPin()
		// // Hash the PIN before storing it
		// data.pin = await bcrypt.hash(rawPin, HASH_ROUNDS)
		//
		// await sendPin(data.phone, rawPin)
	},
}

async function sendPin(phone: string, rawPin: string): Promise<void> {
	try {
		await TwilioService.sendSms(phone, `Your verification PIN is ${rawPin}. Please use this to find your next appointment.`)
	} catch (error) {
		strapi.log.error(error)
		throw new Error(`Failed to send PIN to ${phone}: ${(error as any).message}`)
	}
}
