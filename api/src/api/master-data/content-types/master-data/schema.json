{"kind": "collectionType", "collectionName": "master_datas", "info": {"singularName": "master-data", "pluralName": "master-datas", "displayName": "Master Data", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"CreatedByUser": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "configurable": false}, "UpdatedByUser": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "configurable": false}, "value": {"type": "string", "required": true, "unique": false}, "Category": {"type": "relation", "relation": "manyToOne", "target": "api::master-data-category.master-data-category", "inversedBy": "MasterData"}}}