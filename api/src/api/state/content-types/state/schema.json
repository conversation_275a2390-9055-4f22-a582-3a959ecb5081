{"kind": "collectionType", "collectionName": "states", "info": {"singularName": "state", "pluralName": "states", "displayName": "State", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true}, "code": {"type": "string", "required": true}, "Status": {"type": "relation", "relation": "oneToOne", "target": "api::status.status"}, "Country": {"type": "relation", "relation": "manyToOne", "target": "api::country.country", "inversedBy": "States"}, "CreatedByUser": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "configurable": false}, "UpdatedByUser": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "configurable": false}}}