{"kind": "collectionType", "collectionName": "addresses", "info": {"singularName": "address", "pluralName": "addresses", "displayName": "Address"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"address": {"type": "json"}, "CreatedByUser": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "configurable": false}, "UpdatedByUser": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "configurable": false}}}