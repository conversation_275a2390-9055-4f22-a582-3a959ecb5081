import { isObject } from 'lodash'

import { errors } from '@strapi/utils'
import { Context } from '../../../extensions/graphql/utils'
import { UserEntity } from '../../../../types/types'

export default {
	async viewSettings(_parent: unknown, args: any, context: Context): Promise<any> {
		const { toEntityResponse } = strapi.service('plugin::graphql.format').returnTypes
		try {
			const { user } = context.state as { user: UserEntity } as { user: UserEntity }
			const userID = user.documentId
			const mappers = strapi.services['plugin::graphql.utils'].mappers

			const filters = args.filters
				? {
					...args.filters,
					User: { documentId: { eq: userID } },
				}
				: { User: { documentId: { eq: userID } } }
			const viewSettingsContentType = strapi.contentTypes['api::view-setting.view-setting']
			const convertedFilters = mappers.graphQLFiltersToStrapiQuery(filters, viewSettingsContentType)

			const response = await strapi.services['api::view-setting.view-setting'].find({
				filters: convertedFilters,
			})

			return response.results
		} catch (error) {
			return toEntityResponse([{ error: error }])
		}
	},

	async viewSetting(_parent: unknown, args: any, context: Context): Promise<any> {
		const { toEntityResponse } = strapi.service('plugin::graphql.format').returnTypes
		const { user } = context.state as { user: UserEntity }
		const userID = user.documentId

		const id = args.documentId

		if (!id) {
			throw new errors.ValidationError('The id is required!')
		}

		let response
		try {
			response = await strapi.services['api::view-setting.view-setting'].findOne(id, {
				populate: ['User'],
			})
		} catch {
			throw new errors.ValidationError('ViewSetting fetching data error!')
		}

		if (response?.User?.documentId !== userID) {
			throw new errors.ValidationError('You are not allowed to access this view settings')
		}

		console.log({ response })

		return toEntityResponse(response)
	},

	async createViewSetting(_parent: unknown, args: any, context: Context): Promise<any> {
		const { toEntityResponse } = strapi.service('plugin::graphql.format').returnTypes

		const { user } = context.state as { user: UserEntity }
		const userID = user.documentId

		if (!args.data) {
			throw new errors.ValidationError('The data is required!')
		}

		const { name, settings } = args.data

		if (!name) {
			throw new errors.ValidationError('The name is required!')
		}

		if (!settings) {
			throw new errors.ValidationError('The settings is required!')
		}

		if (!isObject(settings)) {
			throw new errors.ValidationError(`The settings for key: ${name} is not a correct object!`)
		}

		let response
		try {
			response = await strapi.services['api::view-setting.view-setting'].find({
				filters: { User: { documentId: userID } },
				populate: ['User'],
			})
		} catch (error) {
			return toEntityResponse({ error: error })
		}

		let viewSettingsData

		if (!response.results.length) {
			viewSettingsData = await strapi.services['api::view-setting.view-setting'].create({
				data: {
					User: userID,
					settings: { [name]: settings },
				},
			})
		} else {
			viewSettingsData = response.results[0]
			const workingSettings = viewSettingsData.settings
			if (workingSettings[name]) {
				throw new errors.ValidationError(`View settings for ${name} already exists!`)
			}
			workingSettings[name] = settings
			viewSettingsData = await strapi.services['api::view-setting.view-setting'].update(viewSettingsData.documentId, {
				data: { settings: workingSettings },
			})
		}

		return viewSettingsData
	},

	async updateViewSetting(_parent: unknown, args: any, context: Context): Promise<any> {
		const { toEntityResponse } = strapi.service('plugin::graphql.format').returnTypes
		const { user } = context.state as { user: UserEntity }
		const userID = user.documentId

		if (!args.data) {
			throw new errors.ValidationError('The data is required!')
		}

		const { name, settings } = args.data

		if (!name) {
			throw new errors.ValidationError('The name is required!')
		}

		if (!settings) {
			throw new errors.ValidationError('The settings is required!')
		}

		if (!isObject(settings)) {
			throw new errors.ValidationError(`The settings for key: ${name} is not a correct object!`)
		}

		let response
		try {
			response = await strapi.services['api::view-setting.view-setting'].find({
				filters: { User: { documentId: userID } },
				populate: ['User'],
			})
		} catch (error) {
			return toEntityResponse({ error: error })
		}

		let viewSettingsData

		if (!response.results.length) {
			viewSettingsData = await strapi.services['api::view-setting.view-setting'].create({
				data: {
					User: userID,
					settings: { [name]: settings },
				},
			})
		} else {
			viewSettingsData = response.results[0]
			const workingSettings = viewSettingsData.settings
			workingSettings[name] = settings

			if (!workingSettings || !isObject(workingSettings)) throw new errors.ValidationError('Invalid settings data!')
			if (!viewSettingsData.documentId) throw new errors.ValidationError('Invalid settings data!')

			viewSettingsData = await strapi.services['api::view-setting.view-setting'].update(viewSettingsData.documentId, {
				data: { settings: workingSettings },
			})
		}

		return viewSettingsData
	},

	async deleteViewSetting(_parent: unknown, args: any, context: Context): Promise<any> {
		const { toEntityResponse } = strapi.service('plugin::graphql.format').returnTypes
		const { user } = context.state as { user: UserEntity }
		const userID = user.documentId

		if (!args.name) {
			throw new errors.ValidationError('The name is required!')
		}

		const name = args.name

		let response
		try {
			response = await strapi.services['api::view-setting.view-setting'].find({
				filters: { User: { documentId: userID } },
				populate: ['User'],
			})
		} catch (error) {
			return toEntityResponse({ error: error })
		}

		let viewSettingsData

		if (!response.results.length) {
			viewSettingsData = await strapi.services['api::view-setting.view-setting'].create({
				data: {
					User: userID,
					settings: {},
				},
			})
		} else {
			viewSettingsData = response.results[0]
			const workingSettings = viewSettingsData.settings
			delete workingSettings[name]

			if (!workingSettings || !isObject(workingSettings)) throw new errors.ValidationError('Invalid settings data!')
			if (!viewSettingsData.documentId) throw new errors.ValidationError('Invalid settings data!')

			viewSettingsData = await strapi.services['api::view-setting.view-setting'].update(viewSettingsData.documentId, {
				data: { settings: workingSettings },
			})
		}

		return viewSettingsData
	},
}
