{"kind": "collectionType", "collectionName": "view_settings", "info": {"singularName": "view-setting", "pluralName": "view-settings", "displayName": "View Setting", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "settings": {"type": "json"}, "User": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "CreatedByUser": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "configurable": false}, "UpdatedByUser": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "configurable": false}}}