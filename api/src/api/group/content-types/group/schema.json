{"kind": "collectionType", "collectionName": "groups", "info": {"singularName": "group", "pluralName": "groups", "displayName": "Group", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"CreatedByUser": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "configurable": false}, "UpdatedByUser": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "configurable": false}, "name": {"type": "string", "required": true}, "Patients": {"type": "relation", "relation": "oneToMany", "target": "api::patient.patient", "mappedBy": "Group"}, "Client": {"type": "relation", "relation": "manyToOne", "target": "api::client.client", "inversedBy": "Groups"}}}