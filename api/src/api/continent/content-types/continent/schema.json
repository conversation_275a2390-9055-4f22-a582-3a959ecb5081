{"kind": "collectionType", "collectionName": "continents", "info": {"singularName": "continent", "pluralName": "continents", "displayName": "Continent", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true}, "Status": {"type": "relation", "relation": "oneToOne", "target": "api::status.status"}, "Regions": {"type": "relation", "relation": "oneToMany", "target": "api::region.region", "mappedBy": "Continent"}, "CreatedByUser": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "configurable": false}, "UpdatedByUser": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "configurable": false}}}