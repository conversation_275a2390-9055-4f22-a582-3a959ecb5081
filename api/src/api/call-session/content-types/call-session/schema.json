{"kind": "collectionType", "collectionName": "call_sessions", "info": {"singularName": "call-session", "pluralName": "call-sessions", "displayName": "Call Session", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"callSid": {"type": "string"}, "attempts": {"type": "integer"}, "callerNumber": {"type": "string"}, "Patient": {"type": "relation", "relation": "manyToOne", "target": "api::patient.patient", "inversedBy": "CallSessions"}, "twilioBody": {"type": "json"}, "language": {"type": "enumeration", "enum": ["en", "es"], "default": "en"}}}