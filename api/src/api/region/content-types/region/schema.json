{"kind": "collectionType", "collectionName": "regions", "info": {"singularName": "region", "pluralName": "regions", "displayName": "Region", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true}, "Countries": {"type": "relation", "relation": "oneToMany", "target": "api::country.country", "mappedBy": "Region"}, "Status": {"type": "relation", "relation": "oneToOne", "target": "api::status.status"}, "Continent": {"type": "relation", "relation": "manyToOne", "target": "api::continent.continent", "inversedBy": "Regions"}, "CreatedByUser": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "configurable": false}, "UpdatedByUser": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "configurable": false}}}