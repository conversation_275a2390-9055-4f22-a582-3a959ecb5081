{"kind": "collectionType", "collectionName": "master_data_categories", "info": {"singularName": "master-data-category", "pluralName": "master-data-categories", "displayName": "Master Data Category", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"CreatedByUser": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "configurable": false}, "UpdatedByUser": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "configurable": false}, "name": {"type": "string", "unique": true, "required": true}, "MasterData": {"type": "relation", "relation": "oneToMany", "target": "api::master-data.master-data", "mappedBy": "Category"}}}