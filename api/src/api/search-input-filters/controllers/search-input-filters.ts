/**
 * A set of functions called "actions" for `search-input-filters`
 */
import type { Context, Next } from 'koa'
import { generateModelSchema } from '../utils'
import cache from '../utils/cache'

export default {
	getSearchInputFilters: async (ctx: Context, next: Next) => {
		try {
			const generatedSchema = cache.get('generateModelSchema')

			if (generatedSchema) {
				strapi.log.info('cache hit')
				return generatedSchema
			}
			strapi.log.info('cache miss')
			generateBaseGraphqlSchema()

			const generatedModelSchema = generateModelSchema()
			ctx.send({ ...generatedModelSchema })
		} catch (err) {
			ctx.body = err
			ctx.status = 422
			await next()
		}
	},
}

function generateBaseGraphqlSchema() {
	const graphqlPlugin = strapi.plugin('graphql')
	const contentApiService = graphqlPlugin.service('content-api')

	const graphqlSchema = contentApiService.buildSchema()

	const typeMap = graphqlSchema.getTypeMap()
	const queryType = graphqlSchema.getQueryType()

	const schemaData = {
		types: Object.values(typeMap).map((type: any) => ({
			name: type.name,
			inputFields: type.getFields
				? Object.values(type.getFields()).map((field: any) => ({
					name: field.name,
					type: {
						name: field.type.name || field.type.toString(),
						kind: field.type.constructor.name,
					},
				}))
				: [],
		})),
		queryType: {
			fields: queryType
				? Object.values(queryType.getFields()).map((field: any) => ({
					name: field.name,
					type: {
						name: field.type.name || field.type.toString(),
					},
				}))
				: [],
		},
	}

	cache.set('inputFields', schemaData.types)
	cache.set('queryTypeFields', schemaData.queryType.fields)
	cache.set('schema', schemaData)

	strapi.log.info('GraphQL schema generated and cached')
}
