import cache from './cache'
import { <PERSON>figModel, GeneratedField, GeneratedFieldFilter, GeneratedModelSchema, InputField, ModelAttribute, SchemaTypes } from './types'

import { camelCase } from 'lodash/fp'
// deprecated
const query = `query IntrospectionQuery {
  __schema {
    queryType {
      fields {
        name
        type {
          name
        }
      }
    }
    types {
      name
      inputFields {
        name
        type {
          name
          kind
        }
      }
    }
  }
}`

// deprecated
export function queryGraphQLInputFields(url: string): Promise<any> {
	return new Promise((resolve, reject) => {
		const cachedSchema = cache.get('schema')
		if (cachedSchema) {
			resolve(cachedSchema)
			return
		}

		const graphqlEndpoint = url + '/graphql'
		const body = JSON.stringify({
			query,
			operationName: 'IntrospectionQuery',
		})

		console.log('[GRAPHQL REQUEST BODY]', body)

		fetch(graphqlEndpoint, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'x-apollo-operation-name': 'IntrospectionQuery',
			},
			credentials: 'include',
			body,
		})
			.then((res) => res.json())
			.then((schema: any) => {
				if (schema.error || schema.errors) {
					reject(schema)
					return
				}
				cache.set('inputFields', schema.data.__schema.types)
				cache.set('queryTypeFields', schema.data.__schema.queryType.fields)
				cache.set('schema', schema.data.__schema)
				resolve(schema.data.__schema)
			})
			.catch(reject)
	})
}

export function getCollectionTypeFilters(globalId: string): InputField[] {
	const types: SchemaTypes = cache.get('inputFields')
	const collectionTypeFilters = types.find((type) => type.name === globalId + 'FiltersInput')
	if (!collectionTypeFilters) return []
	return collectionTypeFilters.inputFields.filter((inputField) => !['and', 'or', 'not'].includes(inputField.name))
}

export function getModels(): ConfigModel[] {
	const models = Object.values(strapi.contentTypes as unknown as any[])

	return models.filter((model) => model.kind === 'collectionType' && (model.pluginOptions?.['content-manager']?.visible !== false || model.uid === 'admin::user'))
}

export function getComponents(): ConfigModel[] {
	const models = Object.values(strapi.contentTypes as unknown as any[])

	return models.filter((model) => model.modelType === 'component')
}

export function getModelFields(model: ConfigModel, models: ConfigModel[]): GeneratedField[] {
	const inputFields = getCollectionTypeFilters(model.globalId)
	const idInputField = inputFields?.find((inputField) => inputField.name === 'id')
	const idField = getIdField(idInputField)
	const fields = idField ? [idField] : []
	for (const key in model.attributes) {
		const inputField = inputFields.find((inputField) => inputField.name === key)
		if (!inputField) continue
		if ((model.attributes[key] as unknown as { private?: boolean }).private) continue
		if (model.attributes[key].type === 'password') continue
		const field = model.attributes[key] as ModelAttribute
		if (field.type === 'component') {
			const component = getComponents().find((component) => component.uid === field.component)
			if (component?.attributes) {
				for (const name in component.attributes) {
					const componentField = component.attributes[name] as ModelAttribute
					fields.push(getComponentField(componentField, name, inputField, component))
				}
			}
			continue
		}
		fields.push(getModelField(field, inputField, models))
	}
	return fields
}

export function getIdField(idFilters: InputField | undefined): GeneratedField | null {
	if (!idFilters) return null
	return {
		name: 'id',
		type: 'ID',
		unique: true,
		required: true,
		relationType: 'SCALAR',
		filters: getFieldFilters({ type: 'id' } as ModelAttribute, idFilters),
	}
}

export function getModelField(field: ModelAttribute, inputField: InputField, models: ConfigModel[]): GeneratedField {
	return {
		name: inputField.name,
		type: field.type,
		required: field.required ?? false,
		relationType: field.relation ?? 'SCALAR',
		enumValues: field.enum?.map((name) => name.replace(/[\s-]/g, '_')),
		target: models.find((model) => model.uid === field.target)?.globalId ?? field.target,
		filters: getFieldFilters(field, inputField),
		unique: field.unique ?? false,
	}
}

export function getComponentField(field: ModelAttribute, name: string, inputField: InputField, component: ConfigModel): GeneratedField {
	const types: SchemaTypes = cache.get('inputFields')
	const componentTypes = types.find((type) => type.name === inputField.type.name)
	const componentInputField = componentTypes?.inputFields?.find((input) => input.name === name)
	if (!componentInputField) throw new Error('Component input field not found')
	return {
		name: inputField.name,
		child: name,
		type: field.type,
		required: field.required ?? false,
		unique: field.unique ?? false,
		relationType: component.modelType,
		enumValues: field.enum?.map((name) => name.replace('-', '_')),
		target: component.globalId,
		filters: getFieldFilters(field, componentInputField),
	}
}

export function getFieldFilters(field: ModelAttribute, inputField: InputField): GeneratedFieldFilter[] {
	const types: SchemaTypes = cache.get('inputFields')
	const type = types.find((type) => type.name === inputField.type.name)
	if (!type) return []
	const filters: InputField[] = type.inputFields.filter((inputField) => !['and', 'or', 'not'].includes(inputField.name))
	switch (field.type) {
	case 'string':
	case 'text':
	case 'email':
		return getStringFilters(filters, field.type)
	case 'decimal':
	case 'float':
	case 'integer':
	case 'biginteger':
		return getNumberFilters(filters, field.type)
	case 'boolean':
		return getBooleanFilters(filters, field.type)
	case 'date':
		return getDateFilters(filters, field.type)
	case 'datetime':
	case 'time':
		return getDateTimeFilters(filters, field.type)
	case 'json':
	case 'blocks':
	case 'markdown':
	case 'richtext':
		return getJSONFilters(filters, field.type)
	case 'enumeration':
		return getEnumFilters(filters, field.type)
	case 'relation':
		return getRelationFilters(field)
	default:
		return mapFilters(filters, field.type)
	}
}

export function getStringFilters(filters: InputField[], type: string): GeneratedFieldFilter[] {
	const stringFilters = filters.filter((filter) => !['gt', 'gte', 'lt', 'lte', 'between'].includes(filter.name))
	return mapFilters(stringFilters, type)
}

export function getDateTimeFilters(filters: InputField[], type: string): GeneratedFieldFilter[] {
	const dateTimeFilterOperators = ['gt', 'gte', 'lt', 'lte', 'eq', 'ne', 'null', 'notNull', 'between']
	const stringFilters = filters.filter((filter) => dateTimeFilterOperators.includes(filter.name))
	return mapFilters(stringFilters, type)
}

export function getDateFilters(filters: InputField[], type: string): GeneratedFieldFilter[] {
	const dateTimeFilterOperators = ['gt', 'gte', 'lt', 'lte', 'eq', 'ne', 'null', 'notNull', 'between', 'in', 'notIn']
	const stringFilters = filters.filter((filter) => dateTimeFilterOperators.includes(filter.name))
	return mapFilters(stringFilters, type)
}

export function getBooleanFilters(filters: InputField[], type: string): GeneratedFieldFilter[] {
	const stringFilters = filters.filter((filter) => ['eq', 'null', 'notNull'].includes(filter.name))
	return mapFilters(stringFilters, type)
}

export function getNumberFilters(filters: InputField[], type: string): GeneratedFieldFilter[] {
	const numberFilterOperators = ['gt', 'gte', 'lt', 'lte', 'eq', 'ne', 'null', 'notNull', 'in', 'notIn', 'between']
	const numberFilters = filters.filter((filter) => numberFilterOperators.includes(filter.name))
	return mapFilters(numberFilters, type)
}

export function getJSONFilters(filters: InputField[], type: string): GeneratedFieldFilter[] {
	const stringFilters = filters.filter((filter) => ['contains', 'notContains', 'containsi', 'notContainsi', 'null', 'notNull'].includes(filter.name))
	return mapFilters(stringFilters, type)
}

function getEnumFilters(filters: InputField[], type: string): GeneratedFieldFilter[] {
	const stringFilters = filters.filter((filter) => ['eq', 'ne', 'in', 'notIn', 'null', 'notNull'].includes(filter.name))
	return mapFilters(stringFilters, type)
}

export function getRelationFilters(field: ModelAttribute): GeneratedFieldFilter[] {
	const types: SchemaTypes = cache.get('inputFields')
	const filters = types.find((type) => type.name === 'IDFilterInput')
	if (!filters) return []
	if (['oneToOne', 'manyToOne'].includes(field.relation)) {
		const oneRelationFilters = filters.inputFields.filter((filter) => ['eq', 'ne', 'null', 'notNull'].includes(filter.name))
		return mapFilters(oneRelationFilters, 'OneRelation')
	}
	const manyRelationFilters = filters.inputFields.filter((filter) => ['in', 'notIn'].includes(filter.name))
	return mapFilters(manyRelationFilters, 'ManyRelation')
}

/*export function getComponentFilters(filters: InputField[], field: ModelAttribute): GeneratedFieldFilter[] {
	const component = getComponents().find(component => component.uid === field.component);
	const types: SchemaTypes = cache.get('inputFields');
	const componentFilters: SchemaType = types.find(type => type.name === 'IDFilterInput');
	console.log(filters);
	return [];
}*/

export function mapFilters(filters: InputField[], type: string): GeneratedFieldFilter[] {
	if (['OneRelation', 'ManyRelation'].includes(type)) {
		return filters.map((filter) => ({
			name: 'id',
			label: getFilterLabel(filter.name, type),
			type: type,
			subField: filter.name,
		}))
	}
	return filters.map((filter) => ({
		name: filter.name,
		label: getFilterLabel(filter.name, type),
		type: getFilterType(filter, type),
	}))
}

export function getFilterLabel(name: string, type: string): string {
	let label = name
	const nonCsFields = ['enumeration', 'boolean', 'datetime', 'date', 'time', 'decimal', 'float', 'integer', 'biginteger', 'OneRelation', 'ManyRelation']
	switch (name) {
	case 'eq':
		label = 'Equal to'
		if (!nonCsFields.includes(type)) {
			label += ' (cs)'
		}
		break
	case 'eqi':
		label = 'Equal to'
		break
	case 'ne':
		label = 'Not equal to'
		if (!nonCsFields.includes(type)) {
			label += ' (cs)'
		}
		break
	case 'nei':
		label = 'Not equal to'
		break
	case 'startsWith':
		label = 'Starts with'
		break
	case 'endsWith':
		label = 'Ends with'
		break
	case 'contains':
		label = 'Contains (cs)'
		break
	case 'notContains':
		label = 'Exclude (cs)'
		break
	case 'containsi':
		label = 'Contains'
		break
	case 'notContainsi':
		label = 'Exclude'
		break
	case 'gt':
		if (type === 'datetime') {
			label = 'After'
			break
		}
		label = 'Greater than'
		break
	case 'gte':
		if (type === 'datetime') {
			label = 'On or after'
			break
		}
		label = 'Greater than or equal'
		break
	case 'lt':
		if (type === 'datetime') {
			label = 'Before'
			break
		}
		label = 'Less than'
		break
	case 'lte':
		if (type === 'datetime') {
			label = 'On or before'
			break
		}
		label = 'Less than or equal'
		break
	case 'null':
		label = 'Is empty'
		break
	case 'notNull':
		label = 'Is not empty'
		break
	case 'in':
		label = 'Within'
		break
	case 'notIn':
		label = 'Outside'
		break
	case 'between':
		label = 'Between'
		break
	}
	return label
}

export const filterTypes = {
	id: 'ID',
	enumeration: 'Enumeration',
	text: 'Textarea',
	string: 'String',
	email: 'String',
	boolean: 'Boolean',
	integer: 'Int',
	biginteger: 'BigInt',
	decimal: 'Decimal',
	float: 'Float',
	datetime: 'DateTime',
	date: 'Date',
	time: 'Time',
	json: 'JSON',
}

export function getFilterType(filter: InputField, type: string): string {
	if (filter.type.kind === 'LIST') {
		return ((filterTypes as any)?.[type] ?? type) + ':LIST'
	}
	if (filter.type.name === 'Boolean') {
		return filter.type.name
	}
	return (filterTypes as any)?.[type] ?? filter.type.name
}

export function generateModelSchema(): GeneratedModelSchema {
	const configModels: ConfigModel[] = getModels()
	const models: GeneratedModelSchema = {}
	for (const model of configModels) {
		switch (model.globalId) {
		case 'UsersPermissionsRole':
		case 'UsersPermissionsUser':
			model.info = {
				...model.info,
				singularName: model.globalId,
				pluralName: `${model.globalId}s`,
			}
			break
		}

		models[model.globalId] = {
			name: model.modelName,
			caption: model.info.displayName,
			globalId: model.globalId,
			singularName: camelCase(model.info.singularName),
			pluralName: camelCase(model.info.pluralName),
			fields: getModelFields(model, configModels),
		}
	}
	cache.set('generateModelSchema', models)
	return models
}
