import type { Model } from '@strapi/database/dist/types'

export type SchemaTypes = SchemaType[]

export interface SchemaType {
  name: string
  inputFields: InputField[]
}

export interface InputField {
  name: string
  type: {
    name: string
    kind: string
  }
}

export interface ConfigModel extends Model {
  modelName: string
  globalId: string
  kind: string
  modelType: string
  pluginOptions: { [key: string]: any }
  info: {
    displayName: string
    singularName: string
    pluralName: string
  }
}

export interface ModelAttribute {
  type: string
  required: boolean
  enum: string[]
  relation: string
  unique: boolean
  target: string
  component?: string
}

export interface GeneratedModelSchema {
  [key: string]: GeneratedModel
}

export interface GeneratedModel {
  name: string
  caption: string
  globalId: string
  singularName: string
  pluralName: string
  fields: GeneratedField[]
}

export interface GeneratedField {
  name: string
  type: string
  required: boolean
  relationType: string
  unique: boolean
  enumValues?: string[]
  target?: string
  child?: string
  filters: GeneratedFieldFilter[]
}

export interface GeneratedFieldFilter {
  name: string
  label: string
  type: string
  subField?: string
}
