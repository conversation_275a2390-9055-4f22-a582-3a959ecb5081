import { getCollectionTypeFilters, getFilterLabel, getModelField, getModelFields, getModels, mapFilters, queryGraphQLInputFields } from './index'
import cache from './cache'
import * as fs from 'fs'
import * as path from 'path'
import { afterAll, beforeAll, describe, expect, it } from '@jest/globals'
import { cleanupStrapi, setupStrapi } from '../../../../tests/helpers/strapi'
import type { InputField, ModelAttribute } from './types'

const introspectionQueryResponse = JSON.parse(fs.readFileSync(path.join(__dirname, '../../../../tests/mocks/introspectionQuery.json'), 'utf-8'))

beforeAll(async () => {
	await setupStrapi()
	cache.set('inputFields', introspectionQueryResponse.data.__schema.types)
	cache.set('queryTypeFields', introspectionQueryResponse.data.__schema.queryType.fields)
	cache.set('schema', introspectionQueryResponse.data.__schema)
})
afterAll(async () => {
	await cleanupStrapi()
	cache.dump()
})

describe('queryGraphQLInputFields', () => {
	it('should return cached schema', async () => {
		const result = await queryGraphQLInputFields('https://localhost:1337')
		expect(result).toBe(introspectionQueryResponse.data.__schema)
	})
})

describe('getCollectionTypeFilters', () => {
	it('should return empty array if no matching type found', () => {
		const result = getCollectionTypeFilters('testGlobalId')
		expect(result).toEqual([])
	})
	it('should return an array of input fields', () => {
		const result = getCollectionTypeFilters('Config')
		expect(result).not.toBe([])
		expect(result.some((inputField) => ['and', 'or', 'not'].includes(inputField.name))).toEqual(false)
	})
})

describe('getModelFields', () => {
	it('should return an array of input fields', () => {
		const models = getModels()
		expect(models.length > 0).toBe(true)
		const model = models.find((model) => model.globalId === 'Config')
		const result = getModelFields(model, models)
		expect(result.length > 0).toBe(true)
	})
})

describe('getModelField', () => {
	it('should return a field', () => {
		const models = getModels()
		const model = models.find((model) => model.globalId === 'Config')
		const inputFields = getCollectionTypeFilters(model.globalId)
		const inputField = inputFields.find((inputField) => inputField.name === 'name')
		const result = getModelField(model.attributes.name as ModelAttribute, inputField, models)
		expect(result.name).toBe('name')
		expect(result.type).toBe('string')
		expect(typeof result.required).toBe('boolean')
		expect(result.relationType).toBe('SCALAR')
		expect(result.filters.length > 0).toBe(true)
	})
})

describe('mapFilters', () => {
	it('should return correct filters for OneRelation', () => {
		const filters: InputField[] = [{ name: 'eq', type: { name: 'ID', kind: 'SCALAR' } }]
		const result = mapFilters(filters, 'OneRelation')

		expect(result).toEqual([{ name: 'id', label: 'Equal to', type: 'OneRelation', subField: 'eq' }])
	})

	it('should return correct filters for ManyRelation', () => {
		const filters: InputField[] = [{ name: 'in', type: { name: null, kind: 'LIST' } }]
		const result = mapFilters(filters, 'ManyRelation')

		expect(result).toEqual([{ name: 'id', label: 'Within', type: 'ManyRelation', subField: 'in' }])
	})

	it('should return correct filters for other types', () => {
		const filters: InputField[] = [{ name: 'eq', type: { name: 'ID', kind: 'SCALAR' } }]
		const result = mapFilters(filters, 'string')

		expect(result).toEqual([{ name: 'eq', label: 'Equal to (cs)', type: 'String' }])
	})
})

describe('getFilterLabel', () => {
	it('should return correct label for eq filter and non-cs field', () => {
		const result = getFilterLabel('eq', 'enumeration')
		expect(result).toBe('Equal to')
	})

	it('should return correct label for eq filter and cs field', () => {
		const result = getFilterLabel('eq', 'string')
		expect(result).toBe('Equal to (cs)')
	})

	it('should return correct label for eqi filter', () => {
		const result = getFilterLabel('eqi', 'string')
		expect(result).toBe('Equal to')
	})

	it('should return correct label for ne filter and non-cs field', () => {
		const result = getFilterLabel('ne', 'boolean')
		expect(result).toBe('Not equal to')
	})

	it('should return correct label for ne filter and cs field', () => {
		const result = getFilterLabel('ne', 'string')
		expect(result).toBe('Not equal to (cs)')
	})

	it('should return correct label for nei filter', () => {
		const result = getFilterLabel('nei', 'string')
		expect(result).toBe('Not equal to')
	})

	it('should return correct label for startsWith filter', () => {
		const result = getFilterLabel('startsWith', 'string')
		expect(result).toBe('Starts with')
	})

	it('should return correct label for endsWith filter', () => {
		const result = getFilterLabel('endsWith', 'string')
		expect(result).toBe('Ends with')
	})

	it('should return correct label for contains filter', () => {
		const result = getFilterLabel('contains', 'string')
		expect(result).toBe('Contains (cs)')
	})

	it('should return correct label for notContains filter', () => {
		const result = getFilterLabel('notContains', 'string')
		expect(result).toBe('Exclude (cs)')
	})

	it('should return correct label for containsi filter', () => {
		const result = getFilterLabel('containsi', 'string')
		expect(result).toBe('Contains')
	})

	it('should return correct label for notContainsi filter', () => {
		const result = getFilterLabel('notContainsi', 'string')
		expect(result).toBe('Exclude')
	})

	it('should return correct label for gt filter and datetime field', () => {
		const result = getFilterLabel('gt', 'datetime')
		expect(result).toBe('After')
	})

	it('should return correct label for gt filter and non-datetime field', () => {
		const result = getFilterLabel('gt', 'string')
		expect(result).toBe('Greater than')
	})

	it('should return correct label for gte filter and datetime field', () => {
		const result = getFilterLabel('gte', 'datetime')
		expect(result).toBe('On or after')
	})

	it('should return correct label for gte filter and non-datetime field', () => {
		const result = getFilterLabel('gte', 'string')
		expect(result).toBe('Greater than or equal')
	})

	it('should return correct label for lt filter and datetime field', () => {
		const result = getFilterLabel('lt', 'datetime')
		expect(result).toBe('Before')
	})

	it('should return correct label for lt filter and non-datetime field', () => {
		const result = getFilterLabel('lt', 'string')
		expect(result).toBe('Less than')
	})

	it('should return correct label for lte filter and datetime field', () => {
		const result = getFilterLabel('lte', 'datetime')
		expect(result).toBe('On or before')
	})

	it('should return correct label for lte filter and non-datetime field', () => {
		const result = getFilterLabel('lte', 'string')
		expect(result).toBe('Less than or equal')
	})

	it('should return correct label for null filter', () => {
		const result = getFilterLabel('null', 'string')
		expect(result).toBe('Is empty')
	})

	it('should return correct label for notNull filter', () => {
		const result = getFilterLabel('notNull', 'string')
		expect(result).toBe('Is not empty')
	})

	it('should return correct label for in filter', () => {
		const result = getFilterLabel('in', 'string')
		expect(result).toBe('Within')
	})

	it('should return correct label for notIn filter', () => {
		const result = getFilterLabel('notIn', 'string')
		expect(result).toBe('Outside')
	})

	it('should return correct label for between filter', () => {
		const result = getFilterLabel('between', 'string')
		expect(result).toBe('Between')
	})
})
