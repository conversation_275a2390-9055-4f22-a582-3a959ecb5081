{"kind": "collectionType", "collectionName": "sites", "info": {"singularName": "site", "pluralName": "sites", "displayName": "Site", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"CreatedByUser": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "configurable": false}, "UpdatedByUser": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "configurable": false}, "name": {"type": "string", "required": true}, "contact": {"type": "email", "required": true}, "phone": {"type": "string", "required": true}, "Address": {"type": "relation", "relation": "oneToOne", "target": "api::address.address"}, "businessHours": {"type": "json", "required": true}, "blockoutDates": {"type": "json"}, "Client": {"type": "relation", "relation": "manyToOne", "target": "api::client.client", "inversedBy": "Sites"}}}