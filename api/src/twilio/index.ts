import Twilio from 'twilio'
import type { MessageInstance } from 'twilio/lib/rest/api/v2010/account/message'

class TwilioService {
	private client: ReturnType<typeof Twilio>
	private readonly fromNumber: string

	constructor() {
		this.fromNumber = strapi.config.get('server.twilio.fromNumber') as string
		const accountSid = strapi.config.get('server.twilio.accountSid') as string
		const authToken = strapi.config.get('server.twilio.authToken') as string

		if (!accountSid || !authToken || !this.fromNumber) {
			throw new Error('Twilio credentials (SID, TOKEN, FROM) must be set in env.')
		}

		this.client = Twilio(accountSid, authToken)
	}

	/**
   * Send a one-off SMS.
   * @param to   E.164 phone number of the recipient
   * @param body SMS body text
   */
	async sendSms(to: string, body: string): Promise<MessageInstance> {
		return this.client.messages.create({
			from: this.fromNumber,
			to,
			body,
		})
	}
}

export default new TwilioService()
