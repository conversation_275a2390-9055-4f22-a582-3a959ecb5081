<!-- HTML for static distribution bundle build --><!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <title>Swagger UI</title>
    <link rel="stylesheet" type="text/css" href="/plugins/documentation/swagger-ui.css">
    <link rel="icon" type="image/png" href="/plugins/documentation/favicon-32x32.png" sizes="32x32">
    <link rel="icon" type="image/png" href="/plugins/documentation/favicon-16x16.png" sizes="16x16">
    <style>
      html {
        box-sizing: border-box;
        overflow: -moz-scrollbars-vertical;
        overflow-y: scroll;
      }

      *,
      *:before,
      *:after {
        box-sizing: inherit;
      }

      body {
        margin: 0;
        background: #fafafa;
      }
    </style>
  </head>

  <body>
    <div id="swagger-ui"></div>
    <script class="custom-swagger-ui">
      window.onload = function() {
        const ui = SwaggerUIBundle({
          url: "https://petstore.swagger.io/v2/swagger.json",
          spec: {"openapi":"3.0.0","info":{"version":"1.0.0","title":"DOCUMENTATION","description":"","termsOfService":"YOUR_TERMS_OF_SERVICE_URL","contact":{"name":"TEAM","email":"<EMAIL>","url":"mywebsite.io"},"license":{"name":"Apache 2.0","url":"https://www.apache.org/licenses/LICENSE-2.0.html"},"x-generation-date":"2025-05-30T07:47:11.642Z"},"x-strapi-config":{"plugins":["upload","users-permissions"]},"servers":[{"url":"http://localhost:1337/api","description":"Development server"}],"externalDocs":{"description":"Find out more","url":"https://docs.strapi.io/developer-docs/latest/getting-started/introduction.html"},"security":[{"bearerAuth":[]}],"paths":{"/addresses":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/AddressListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Address"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object","additionalProperties":true},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/addresses"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/AddressResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Address"],"parameters":[],"operationId":"post/addresses","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/AddressRequest"}}}}}},"/addresses/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/AddressResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Address"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/addresses/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/AddressResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Address"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/addresses/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/AddressRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Address"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/addresses/{id}"}},"/call-sessions":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/CallSessionListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Call-session"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object","additionalProperties":true},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/call-sessions"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/CallSessionResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Call-session"],"parameters":[],"operationId":"post/call-sessions","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/CallSessionRequest"}}}}}},"/call-sessions/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/CallSessionResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Call-session"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/call-sessions/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/CallSessionResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Call-session"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/call-sessions/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/CallSessionRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Call-session"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/call-sessions/{id}"}},"/clients":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ClientListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Client"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object","additionalProperties":true},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/clients"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ClientResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Client"],"parameters":[],"operationId":"post/clients","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/ClientRequest"}}}}}},"/clients/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ClientResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Client"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/clients/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ClientResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Client"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/clients/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/ClientRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Client"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/clients/{id}"}},"/continents":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ContinentListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Continent"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object","additionalProperties":true},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/continents"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ContinentResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Continent"],"parameters":[],"operationId":"post/continents","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/ContinentRequest"}}}}}},"/continents/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ContinentResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Continent"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/continents/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ContinentResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Continent"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/continents/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/ContinentRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Continent"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/continents/{id}"}},"/countries":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/CountryListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Country"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object","additionalProperties":true},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/countries"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/CountryResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Country"],"parameters":[],"operationId":"post/countries","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/CountryRequest"}}}}}},"/countries/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/CountryResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Country"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/countries/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/CountryResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Country"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/countries/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/CountryRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Country"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/countries/{id}"}},"/groups":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/GroupListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Group"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object","additionalProperties":true},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/groups"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/GroupResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Group"],"parameters":[],"operationId":"post/groups","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/GroupRequest"}}}}}},"/groups/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/GroupResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Group"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/groups/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/GroupResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Group"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/groups/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/GroupRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Group"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/groups/{id}"}},"/master-datas":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/MasterDataListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Master-data"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object","additionalProperties":true},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/master-datas"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/MasterDataResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Master-data"],"parameters":[],"operationId":"post/master-datas","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/MasterDataRequest"}}}}}},"/master-datas/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/MasterDataResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Master-data"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/master-datas/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/MasterDataResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Master-data"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/master-datas/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/MasterDataRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Master-data"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/master-datas/{id}"}},"/master-data-categories":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/MasterDataCategoryListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Master-data-category"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object","additionalProperties":true},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/master-data-categories"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/MasterDataCategoryResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Master-data-category"],"parameters":[],"operationId":"post/master-data-categories","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/MasterDataCategoryRequest"}}}}}},"/master-data-categories/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/MasterDataCategoryResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Master-data-category"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/master-data-categories/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/MasterDataCategoryResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Master-data-category"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/master-data-categories/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/MasterDataCategoryRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Master-data-category"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/master-data-categories/{id}"}},"/patients":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/PatientListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Patient"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object","additionalProperties":true},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/patients"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/PatientResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Patient"],"parameters":[],"operationId":"post/patients","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/PatientRequest"}}}}}},"/patients/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/PatientResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Patient"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/patients/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/PatientResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Patient"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/patients/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/PatientRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Patient"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/patients/{id}"}},"/regions":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/RegionListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Region"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object","additionalProperties":true},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/regions"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/RegionResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Region"],"parameters":[],"operationId":"post/regions","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/RegionRequest"}}}}}},"/regions/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/RegionResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Region"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/regions/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/RegionResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Region"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/regions/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/RegionRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Region"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/regions/{id}"}},"/sites":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/SiteListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Site"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object","additionalProperties":true},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/sites"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/SiteResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Site"],"parameters":[],"operationId":"post/sites","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/SiteRequest"}}}}}},"/sites/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/SiteResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Site"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/sites/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/SiteResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Site"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/sites/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/SiteRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Site"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/sites/{id}"}},"/states":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/StateListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["State"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object","additionalProperties":true},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/states"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/StateResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["State"],"parameters":[],"operationId":"post/states","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/StateRequest"}}}}}},"/states/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/StateResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["State"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/states/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/StateResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["State"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/states/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/StateRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["State"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/states/{id}"}},"/statuses":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/StatusListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Status"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object","additionalProperties":true},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/statuses"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/StatusResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Status"],"parameters":[],"operationId":"post/statuses","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/StatusRequest"}}}}}},"/statuses/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/StatusResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Status"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/statuses/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/StatusResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Status"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/statuses/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/StatusRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Status"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/statuses/{id}"}},"/view-settings":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ViewSettingListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["View-setting"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object","additionalProperties":true},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/view-settings"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ViewSettingResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["View-setting"],"parameters":[],"operationId":"post/view-settings","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/ViewSettingRequest"}}}}}},"/view-settings/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ViewSettingResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["View-setting"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/view-settings/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ViewSettingResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["View-setting"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/view-settings/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/ViewSettingRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["View-setting"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/view-settings/{id}"}},"/upload":{"post":{"description":"Upload files","responses":{"200":{"description":"response","content":{"application/json":{"schema":{"type":"array","items":{"$ref":"#/components/schemas/UploadFile"}}}}}},"summary":"","tags":["Upload - File"],"requestBody":{"description":"Upload files","required":true,"content":{"multipart/form-data":{"schema":{"required":["files"],"type":"object","properties":{"path":{"type":"string","description":"The folder where the file(s) will be uploaded to (only supported on strapi-provider-upload-aws-s3)."},"refId":{"type":"string","description":"The ID of the entry which the file(s) will be linked to"},"ref":{"type":"string","description":"The unique ID (uid) of the model which the file(s) will be linked to (api::restaurant.restaurant)."},"field":{"type":"string","description":"The field of the entry which the file(s) will be precisely linked to."},"files":{"type":"array","items":{"type":"string","format":"binary"}}}}}}}}},"/upload?id={id}":{"post":{"parameters":[{"name":"id","in":"query","description":"File id","required":true,"schema":{"type":"string"}}],"description":"Upload file information","responses":{"200":{"description":"response","content":{"application/json":{"schema":{"type":"array","items":{"$ref":"#/components/schemas/UploadFile"}}}}}},"summary":"","tags":["Upload - File"],"requestBody":{"description":"Upload files","required":true,"content":{"multipart/form-data":{"schema":{"type":"object","properties":{"fileInfo":{"type":"object","properties":{"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"}}},"files":{"type":"string","format":"binary"}}}}}}}},"/upload/files":{"get":{"tags":["Upload - File"],"responses":{"200":{"description":"Get a list of files","content":{"application/json":{"schema":{"type":"array","items":{"$ref":"#/components/schemas/UploadFile"}}}}}}}},"/upload/files/{id}":{"get":{"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"string"}}],"tags":["Upload - File"],"responses":{"200":{"description":"Get a specific file","content":{"application/json":{"schema":{"$ref":"#/components/schemas/UploadFile"}}}}}},"delete":{"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"string"}}],"tags":["Upload - File"],"responses":{"200":{"description":"Delete a file","content":{"application/json":{"schema":{"$ref":"#/components/schemas/UploadFile"}}}}}}},"/connect/{provider}":{"get":{"parameters":[{"name":"provider","in":"path","required":true,"description":"Provider name","schema":{"type":"string","pattern":".*"}}],"tags":["Users-Permissions - Auth"],"summary":"Login with a provider","description":"Redirects to provider login before being redirect to /auth/{provider}/callback","responses":{"301":{"description":"Redirect response"},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/auth/local":{"post":{"tags":["Users-Permissions - Auth"],"summary":"Local login","description":"Returns a jwt token and user info","requestBody":{"content":{"application/json":{"schema":{"type":"object","properties":{"identifier":{"type":"string"},"password":{"type":"string"}}},"example":{"identifier":"foobar","password":"Test1234"}}},"required":true},"responses":{"200":{"description":"Connection","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Users-Permissions-UserRegistration"}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/auth/local/register":{"post":{"tags":["Users-Permissions - Auth"],"summary":"Register a user","description":"Returns a jwt token and user info","requestBody":{"content":{"application/json":{"schema":{"type":"object","properties":{"username":{"type":"string"},"email":{"type":"string"},"password":{"type":"string"}}},"example":{"username":"foobar","email":"<EMAIL>","password":"Test1234"}}},"required":true},"responses":{"200":{"description":"Successful registration","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Users-Permissions-UserRegistration"}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/auth/{provider}/callback":{"get":{"tags":["Users-Permissions - Auth"],"summary":"Default Callback from provider auth","parameters":[{"name":"provider","in":"path","required":true,"description":"Provider name","schema":{"type":"string"}}],"responses":{"200":{"description":"Returns a jwt token and user info","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Users-Permissions-UserRegistration"}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/auth/forgot-password":{"post":{"tags":["Users-Permissions - Auth"],"summary":"Send rest password email","requestBody":{"required":true,"content":{"application/json":{"schema":{"type":"object","properties":{"email":{"type":"string"}}},"example":{"email":"<EMAIL>"}}}},"responses":{"200":{"description":"Returns ok","content":{"application/json":{"schema":{"type":"object","properties":{"ok":{"type":"string","enum":[true]}}}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/auth/reset-password":{"post":{"tags":["Users-Permissions - Auth"],"summary":"Rest user password","requestBody":{"required":true,"content":{"application/json":{"schema":{"type":"object","properties":{"password":{"type":"string"},"passwordConfirmation":{"type":"string"},"code":{"type":"string"}}},"example":{"password":"Test1234","passwordConfirmation":"Test1234","code":"zertyoaizndoianzodianzdonaizdoinaozdnia"}}}},"responses":{"200":{"description":"Returns a jwt token and user info","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Users-Permissions-UserRegistration"}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/auth/change-password":{"post":{"tags":["Users-Permissions - Auth"],"summary":"Update user's own password","requestBody":{"required":true,"content":{"application/json":{"schema":{"type":"object","required":["password","currentPassword","passwordConfirmation"],"properties":{"password":{"type":"string"},"currentPassword":{"type":"string"},"passwordConfirmation":{"type":"string"}}}}}},"responses":{"200":{"description":"Returns a jwt token and user info","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Users-Permissions-UserRegistration"}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/auth/email-confirmation":{"get":{"tags":["Users-Permissions - Auth"],"summary":"Confirm user email","parameters":[{"in":"query","name":"confirmation","schema":{"type":"string"},"description":"confirmation token received by email"}],"responses":{"301":{"description":"Redirects to the configure email confirmation redirect url"},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/auth/send-email-confirmation":{"post":{"tags":["Users-Permissions - Auth"],"summary":"Send confirmation email","requestBody":{"required":true,"content":{"application/json":{"schema":{"type":"object","properties":{"email":{"type":"string"}}}}}},"responses":{"200":{"description":"Returns email and boolean to confirm email was sent","content":{"application/json":{"schema":{"type":"object","properties":{"email":{"type":"string"},"sent":{"type":"string","enum":[true]}}}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/users-permissions/permissions":{"get":{"tags":["Users-Permissions - Users & Roles"],"summary":"Get default generated permissions","responses":{"200":{"description":"Returns the permissions tree","content":{"application/json":{"schema":{"type":"object","properties":{"permissions":{"$ref":"#/components/schemas/Users-Permissions-PermissionsTree"}}},"example":{"permissions":{"api::content-type.content-type":{"controllers":{"controllerA":{"find":{"enabled":false,"policy":""},"findOne":{"enabled":false,"policy":""},"create":{"enabled":false,"policy":""}},"controllerB":{"find":{"enabled":false,"policy":""},"findOne":{"enabled":false,"policy":""},"create":{"enabled":false,"policy":""}}}}}}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/users-permissions/roles":{"get":{"tags":["Users-Permissions - Users & Roles"],"summary":"List roles","responses":{"200":{"description":"Returns list of roles","content":{"application/json":{"schema":{"type":"object","properties":{"roles":{"type":"array","items":{"allOf":[{"$ref":"#/components/schemas/Users-Permissions-Role"},{"type":"object","properties":{"nb_users":{"type":"number"}}}]}}}},"example":{"roles":[{"id":1,"name":"Public","description":"Default role given to unauthenticated user.","type":"public","createdAt":"2022-05-19T17:35:35.097Z","updatedAt":"2022-05-31T16:05:36.603Z","nb_users":0}]}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}},"post":{"tags":["Users-Permissions - Users & Roles"],"summary":"Create a role","requestBody":{"$ref":"#/components/requestBodies/Users-Permissions-RoleRequest"},"responses":{"200":{"description":"Returns ok if the role was create","content":{"application/json":{"schema":{"type":"object","properties":{"ok":{"type":"string","enum":[true]}}}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/users-permissions/roles/{id}":{"get":{"tags":["Users-Permissions - Users & Roles"],"summary":"Get a role","parameters":[{"in":"path","name":"id","required":true,"schema":{"type":"string"},"description":"role Id"}],"responses":{"200":{"description":"Returns the role","content":{"application/json":{"schema":{"type":"object","properties":{"role":{"$ref":"#/components/schemas/Users-Permissions-Role"}}},"example":{"role":{"id":1,"name":"Public","description":"Default role given to unauthenticated user.","type":"public","createdAt":"2022-05-19T17:35:35.097Z","updatedAt":"2022-05-31T16:05:36.603Z","permissions":{"api::content-type.content-type":{"controllers":{"controllerA":{"find":{"enabled":true}}}}}}}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/users-permissions/roles/{role}":{"put":{"tags":["Users-Permissions - Users & Roles"],"summary":"Update a role","parameters":[{"in":"path","name":"role","required":true,"schema":{"type":"string"},"description":"role Id"}],"requestBody":{"$ref":"#/components/requestBodies/Users-Permissions-RoleRequest"},"responses":{"200":{"description":"Returns ok if the role was udpated","content":{"application/json":{"schema":{"type":"object","properties":{"ok":{"type":"string","enum":[true]}}}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}},"delete":{"tags":["Users-Permissions - Users & Roles"],"summary":"Delete a role","parameters":[{"in":"path","name":"role","required":true,"schema":{"type":"string"},"description":"role Id"}],"responses":{"200":{"description":"Returns ok if the role was delete","content":{"application/json":{"schema":{"type":"object","properties":{"ok":{"type":"string","enum":[true]}}}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/users":{"get":{"tags":["Users-Permissions - Users & Roles"],"summary":"Get list of users","responses":{"200":{"description":"Returns an array of users","content":{"application/json":{"schema":{"type":"array","items":{"$ref":"#/components/schemas/Users-Permissions-User"}},"example":[{"id":9,"username":"<EMAIL>","email":"<EMAIL>","provider":"local","confirmed":false,"blocked":false,"createdAt":"2022-06-01T18:32:35.211Z","updatedAt":"2022-06-01T18:32:35.217Z"}]}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}},"post":{"tags":["Users-Permissions - Users & Roles"],"summary":"Create a user","requestBody":{"required":true,"content":{"application/json":{"schema":{"type":"object","required":["username","email","password"],"properties":{"email":{"type":"string"},"username":{"type":"string"},"password":{"type":"string"}}},"example":{"username":"foo","email":"<EMAIL>","password":"foo-password"}}}},"responses":{"201":{"description":"Returns created user info","content":{"application/json":{"schema":{"allOf":[{"$ref":"#/components/schemas/Users-Permissions-User"},{"type":"object","properties":{"role":{"$ref":"#/components/schemas/Users-Permissions-Role"}}}]},"example":{"id":1,"username":"foo","email":"<EMAIL>","provider":"local","confirmed":false,"blocked":false,"createdAt":"2022-05-19T17:35:35.096Z","updatedAt":"2022-05-19T17:35:35.096Z","role":{"id":1,"name":"X","description":"Default role given to authenticated user.","type":"authenticated","createdAt":"2022-05-19T17:35:35.096Z","updatedAt":"2022-06-04T07:11:59.551Z"}}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/users/{id}":{"get":{"tags":["Users-Permissions - Users & Roles"],"summary":"Get a user","parameters":[{"in":"path","name":"id","required":true,"schema":{"type":"string"},"description":"user Id"}],"responses":{"200":{"description":"Returns a user","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Users-Permissions-User"},"example":{"id":1,"username":"foo","email":"<EMAIL>","provider":"local","confirmed":false,"blocked":false,"createdAt":"2022-05-19T17:35:35.096Z","updatedAt":"2022-05-19T17:35:35.096Z"}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}},"put":{"tags":["Users-Permissions - Users & Roles"],"summary":"Update a user","parameters":[{"in":"path","name":"id","required":true,"schema":{"type":"string"},"description":"user Id"}],"requestBody":{"required":true,"content":{"application/json":{"schema":{"type":"object","required":["username","email","password"],"properties":{"email":{"type":"string"},"username":{"type":"string"},"password":{"type":"string"}}},"example":{"username":"foo","email":"<EMAIL>","password":"foo-password"}}}},"responses":{"200":{"description":"Returns updated user info","content":{"application/json":{"schema":{"allOf":[{"$ref":"#/components/schemas/Users-Permissions-User"},{"type":"object","properties":{"role":{"$ref":"#/components/schemas/Users-Permissions-Role"}}}]},"example":{"id":1,"username":"foo","email":"<EMAIL>","provider":"local","confirmed":false,"blocked":false,"createdAt":"2022-05-19T17:35:35.096Z","updatedAt":"2022-05-19T17:35:35.096Z","role":{"id":1,"name":"X","description":"Default role given to authenticated user.","type":"authenticated","createdAt":"2022-05-19T17:35:35.096Z","updatedAt":"2022-06-04T07:11:59.551Z"}}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}},"delete":{"tags":["Users-Permissions - Users & Roles"],"summary":"Delete a user","parameters":[{"in":"path","name":"id","required":true,"schema":{"type":"string"},"description":"user Id"}],"responses":{"200":{"description":"Returns deleted user info","content":{"application/json":{"schema":{"allOf":[{"$ref":"#/components/schemas/Users-Permissions-User"}]},"example":{"id":1,"username":"foo","email":"<EMAIL>","provider":"local","confirmed":false,"blocked":false,"createdAt":"2022-05-19T17:35:35.096Z","updatedAt":"2022-05-19T17:35:35.096Z"}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/users/me":{"get":{"tags":["Users-Permissions - Users & Roles"],"summary":"Get authenticated user info","responses":{"200":{"description":"Returns user info","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Users-Permissions-User"},"example":{"id":1,"username":"foo","email":"<EMAIL>","provider":"local","confirmed":false,"blocked":false,"createdAt":"2022-05-19T17:35:35.096Z","updatedAt":"2022-05-19T17:35:35.096Z"}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/users/count":{"get":{"tags":["Users-Permissions - Users & Roles"],"summary":"Get user count","responses":{"200":{"description":"Returns a number","content":{"application/json":{"schema":{"type":"number"},"example":1}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}}},"components":{"securitySchemes":{"bearerAuth":{"type":"http","scheme":"bearer","bearerFormat":"JWT"}},"schemas":{"Error":{"type":"object","required":["error"],"properties":{"data":{"nullable":true,"oneOf":[{"type":"object"},{"type":"array","items":{"type":"object"}}]},"error":{"type":"object","properties":{"status":{"type":"integer"},"name":{"type":"string"},"message":{"type":"string"},"details":{"type":"object"}}}}},"AddressRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"address":{},"CreatedByUser":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"UpdatedByUser":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"locale":{"type":"string"},"localizations":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}}}}}},"AddressListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/Address"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"Address":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"address":{},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"provider":{"type":"string"},"resetPasswordToken":{"type":"string"},"confirmationToken":{"type":"string"},"confirmed":{"type":"boolean"},"blocked":{"type":"boolean"},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"description":{"type":"string"},"type":{"type":"string"},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"address":{},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}}}},"AddressResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/Address"},"meta":{"type":"object"}}},"CallSessionRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"callSid":{"type":"string"},"attempts":{"type":"integer"},"callerNumber":{"type":"string"},"Patient":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"twilioBody":{},"language":{"type":"string","enum":["en","es"]},"locale":{"type":"string"},"localizations":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}}}}}},"CallSessionListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/CallSession"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"CallSession":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"callSid":{"type":"string"},"attempts":{"type":"integer"},"callerNumber":{"type":"string"},"Patient":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"provider":{"type":"string"},"resetPasswordToken":{"type":"string"},"confirmationToken":{"type":"string"},"confirmed":{"type":"boolean"},"blocked":{"type":"boolean"},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"description":{"type":"string"},"type":{"type":"string"},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"firstName":{"type":"string"},"lastName":{"type":"string"},"nameSuffix":{"type":"string"},"namePrefix":{"type":"string"},"ssn":{"type":"string"},"pid":{"type":"string"},"Group":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"name":{"type":"string"},"Patients":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"Client":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"name":{"type":"string"},"contact":{"type":"string","format":"email"},"phone":{"type":"string"},"Sites":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"name":{"type":"string"},"contact":{"type":"string","format":"email"},"phone":{"type":"string"},"Address":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"address":{},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"businessHours":{},"blockoutDates":{},"Client":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"Groups":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"Race":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"value":{"type":"string"},"Category":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"name":{"type":"string"},"MasterData":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"Sex":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"dob":{"type":"string","format":"date"},"phone":{"type":"string"},"contact":{"type":"string","format":"email"},"CallSessions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"callSid":{"type":"string"},"attempts":{"type":"integer"},"callerNumber":{"type":"string"},"Patient":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"twilioBody":{},"language":{"type":"string","enum":["en","es"]},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"twilioBody":{},"language":{"type":"string","enum":["en","es"]},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"CallSessionResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/CallSession"},"meta":{"type":"object"}}},"ClientRequest":{"type":"object","required":["data"],"properties":{"data":{"required":["name","contact"],"type":"object","properties":{"CreatedByUser":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"UpdatedByUser":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"name":{"type":"string"},"contact":{"type":"string","format":"email"},"phone":{"type":"string"},"Sites":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}},"Groups":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}}}}}},"ClientListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/Client"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"Client":{"type":"object","required":["name","contact"],"properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"provider":{"type":"string"},"resetPasswordToken":{"type":"string"},"confirmationToken":{"type":"string"},"confirmed":{"type":"boolean"},"blocked":{"type":"boolean"},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"description":{"type":"string"},"type":{"type":"string"},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"name":{"type":"string"},"contact":{"type":"string","format":"email"},"phone":{"type":"string"},"Sites":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"name":{"type":"string"},"contact":{"type":"string","format":"email"},"phone":{"type":"string"},"Address":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"address":{},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"businessHours":{},"blockoutDates":{},"Client":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"name":{"type":"string"},"contact":{"type":"string","format":"email"},"phone":{"type":"string"},"Sites":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"Groups":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"name":{"type":"string"},"Patients":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"firstName":{"type":"string"},"lastName":{"type":"string"},"nameSuffix":{"type":"string"},"namePrefix":{"type":"string"},"ssn":{"type":"string"},"pid":{"type":"string"},"Group":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"Race":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"value":{"type":"string"},"Category":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"name":{"type":"string"},"MasterData":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"Sex":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"dob":{"type":"string","format":"date"},"phone":{"type":"string"},"contact":{"type":"string","format":"email"},"CallSessions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"callSid":{"type":"string"},"attempts":{"type":"integer"},"callerNumber":{"type":"string"},"Patient":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"twilioBody":{},"language":{"type":"string","enum":["en","es"]},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"Client":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"Groups":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"ClientResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/Client"},"meta":{"type":"object"}}},"ContinentRequest":{"type":"object","required":["data"],"properties":{"data":{"required":["name"],"type":"object","properties":{"name":{"type":"string"},"Status":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"Regions":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}},"CreatedByUser":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"UpdatedByUser":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"locale":{"type":"string"},"localizations":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}}}}}},"ContinentListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/Continent"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"Continent":{"type":"object","required":["name"],"properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"Status":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"Name":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"provider":{"type":"string"},"resetPasswordToken":{"type":"string"},"confirmationToken":{"type":"string"},"confirmed":{"type":"boolean"},"blocked":{"type":"boolean"},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"description":{"type":"string"},"type":{"type":"string"},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"Regions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"Countries":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"name":{"type":"string"},"code":{"type":"string"},"Status":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"codeThree":{"type":"string"},"States":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"code":{"type":"string"},"Status":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"Country":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"Region":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"Status":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"Continent":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"Status":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"Regions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"ContinentResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/Continent"},"meta":{"type":"object"}}},"CountryRequest":{"type":"object","required":["data"],"properties":{"data":{"required":["name","code","codeThree"],"type":"object","properties":{"CreatedByUser":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"UpdatedByUser":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"name":{"type":"string"},"code":{"type":"string"},"Status":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"codeThree":{"type":"string"},"States":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}},"Region":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"locale":{"type":"string"},"localizations":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}}}}}},"CountryListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/Country"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"Country":{"type":"object","required":["name","code","codeThree"],"properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"provider":{"type":"string"},"resetPasswordToken":{"type":"string"},"confirmationToken":{"type":"string"},"confirmed":{"type":"boolean"},"blocked":{"type":"boolean"},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"description":{"type":"string"},"type":{"type":"string"},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"name":{"type":"string"},"code":{"type":"string"},"Status":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"Name":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"codeThree":{"type":"string"},"States":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"code":{"type":"string"},"Status":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"Country":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"name":{"type":"string"},"code":{"type":"string"},"Status":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"codeThree":{"type":"string"},"States":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"Region":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"Countries":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"Status":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"Continent":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"Status":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"Regions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"Region":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"CountryResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/Country"},"meta":{"type":"object"}}},"GroupRequest":{"type":"object","required":["data"],"properties":{"data":{"required":["name"],"type":"object","properties":{"CreatedByUser":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"UpdatedByUser":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"name":{"type":"string"},"Patients":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}},"Client":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"locale":{"type":"string"},"localizations":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}}}}}},"GroupListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/Group"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"Group":{"type":"object","required":["name"],"properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"provider":{"type":"string"},"resetPasswordToken":{"type":"string"},"confirmationToken":{"type":"string"},"confirmed":{"type":"boolean"},"blocked":{"type":"boolean"},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"description":{"type":"string"},"type":{"type":"string"},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"name":{"type":"string"},"Patients":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"firstName":{"type":"string"},"lastName":{"type":"string"},"nameSuffix":{"type":"string"},"namePrefix":{"type":"string"},"ssn":{"type":"string"},"pid":{"type":"string"},"Group":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"name":{"type":"string"},"Patients":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"Client":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"name":{"type":"string"},"contact":{"type":"string","format":"email"},"phone":{"type":"string"},"Sites":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"name":{"type":"string"},"contact":{"type":"string","format":"email"},"phone":{"type":"string"},"Address":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"address":{},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"businessHours":{},"blockoutDates":{},"Client":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"Groups":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"Race":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"value":{"type":"string"},"Category":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"name":{"type":"string"},"MasterData":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"Sex":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"dob":{"type":"string","format":"date"},"phone":{"type":"string"},"contact":{"type":"string","format":"email"},"CallSessions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"callSid":{"type":"string"},"attempts":{"type":"integer"},"callerNumber":{"type":"string"},"Patient":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"twilioBody":{},"language":{"type":"string","enum":["en","es"]},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"Client":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"GroupResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/Group"},"meta":{"type":"object"}}},"MasterDataRequest":{"type":"object","required":["data"],"properties":{"data":{"required":["value"],"type":"object","properties":{"CreatedByUser":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"UpdatedByUser":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"value":{"type":"string"},"Category":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"locale":{"type":"string"},"localizations":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}}}}}},"MasterDataListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/MasterData"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"MasterData":{"type":"object","required":["value"],"properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"provider":{"type":"string"},"resetPasswordToken":{"type":"string"},"confirmationToken":{"type":"string"},"confirmed":{"type":"boolean"},"blocked":{"type":"boolean"},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"description":{"type":"string"},"type":{"type":"string"},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"value":{"type":"string"},"Category":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"name":{"type":"string"},"MasterData":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"value":{"type":"string"},"Category":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"MasterDataResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/MasterData"},"meta":{"type":"object"}}},"MasterDataCategoryRequest":{"type":"object","required":["data"],"properties":{"data":{"required":["name"],"type":"object","properties":{"CreatedByUser":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"UpdatedByUser":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"name":{"type":"string"},"MasterData":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}}}}}},"MasterDataCategoryListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/MasterDataCategory"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"MasterDataCategory":{"type":"object","required":["name"],"properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"provider":{"type":"string"},"resetPasswordToken":{"type":"string"},"confirmationToken":{"type":"string"},"confirmed":{"type":"boolean"},"blocked":{"type":"boolean"},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"description":{"type":"string"},"type":{"type":"string"},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"name":{"type":"string"},"MasterData":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"value":{"type":"string"},"Category":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"name":{"type":"string"},"MasterData":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"MasterDataCategoryResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/MasterDataCategory"},"meta":{"type":"object"}}},"PatientRequest":{"type":"object","required":["data"],"properties":{"data":{"required":["firstName","lastName","ssn","pid"],"type":"object","properties":{"CreatedByUser":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"UpdatedByUser":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"firstName":{"type":"string"},"lastName":{"type":"string"},"nameSuffix":{"type":"string"},"namePrefix":{"type":"string"},"ssn":{"type":"string"},"pid":{"type":"string"},"Group":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"Race":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"Sex":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"dob":{"type":"string","format":"date"},"pin":{"type":"string","format":"password","example":"*******"},"phone":{"type":"string"},"contact":{"type":"string","format":"email"},"CallSessions":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}}}}}},"PatientListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/Patient"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"Patient":{"type":"object","required":["firstName","lastName","ssn","pid"],"properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"provider":{"type":"string"},"resetPasswordToken":{"type":"string"},"confirmationToken":{"type":"string"},"confirmed":{"type":"boolean"},"blocked":{"type":"boolean"},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"description":{"type":"string"},"type":{"type":"string"},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"firstName":{"type":"string"},"lastName":{"type":"string"},"nameSuffix":{"type":"string"},"namePrefix":{"type":"string"},"ssn":{"type":"string"},"pid":{"type":"string"},"Group":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"name":{"type":"string"},"Patients":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"firstName":{"type":"string"},"lastName":{"type":"string"},"nameSuffix":{"type":"string"},"namePrefix":{"type":"string"},"ssn":{"type":"string"},"pid":{"type":"string"},"Group":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"Race":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"value":{"type":"string"},"Category":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"name":{"type":"string"},"MasterData":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"Sex":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"dob":{"type":"string","format":"date"},"phone":{"type":"string"},"contact":{"type":"string","format":"email"},"CallSessions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"callSid":{"type":"string"},"attempts":{"type":"integer"},"callerNumber":{"type":"string"},"Patient":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"twilioBody":{},"language":{"type":"string","enum":["en","es"]},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"Client":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"name":{"type":"string"},"contact":{"type":"string","format":"email"},"phone":{"type":"string"},"Sites":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"name":{"type":"string"},"contact":{"type":"string","format":"email"},"phone":{"type":"string"},"Address":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"address":{},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"businessHours":{},"blockoutDates":{},"Client":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"Groups":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"Race":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"Sex":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"dob":{"type":"string","format":"date"},"phone":{"type":"string"},"contact":{"type":"string","format":"email"},"CallSessions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"PatientResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/Patient"},"meta":{"type":"object"}}},"RegionRequest":{"type":"object","required":["data"],"properties":{"data":{"required":["name"],"type":"object","properties":{"name":{"type":"string"},"Countries":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}},"Status":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"Continent":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"CreatedByUser":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"UpdatedByUser":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"locale":{"type":"string"},"localizations":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}}}}}},"RegionListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/Region"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"Region":{"type":"object","required":["name"],"properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"Countries":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"provider":{"type":"string"},"resetPasswordToken":{"type":"string"},"confirmationToken":{"type":"string"},"confirmed":{"type":"boolean"},"blocked":{"type":"boolean"},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"description":{"type":"string"},"type":{"type":"string"},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"name":{"type":"string"},"code":{"type":"string"},"Status":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"Name":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"codeThree":{"type":"string"},"States":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"code":{"type":"string"},"Status":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"Country":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"Region":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"Countries":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"Status":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"Continent":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"Status":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"Regions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"Status":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"Continent":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"RegionResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/Region"},"meta":{"type":"object"}}},"SiteRequest":{"type":"object","required":["data"],"properties":{"data":{"required":["name","contact","phone","businessHours"],"type":"object","properties":{"CreatedByUser":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"UpdatedByUser":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"name":{"type":"string"},"contact":{"type":"string","format":"email"},"phone":{"type":"string"},"Address":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"businessHours":{},"blockoutDates":{},"Client":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"locale":{"type":"string"},"localizations":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}}}}}},"SiteListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/Site"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"Site":{"type":"object","required":["name","contact","phone","businessHours"],"properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"provider":{"type":"string"},"resetPasswordToken":{"type":"string"},"confirmationToken":{"type":"string"},"confirmed":{"type":"boolean"},"blocked":{"type":"boolean"},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"description":{"type":"string"},"type":{"type":"string"},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"name":{"type":"string"},"contact":{"type":"string","format":"email"},"phone":{"type":"string"},"Address":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"address":{},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"businessHours":{},"blockoutDates":{},"Client":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"name":{"type":"string"},"contact":{"type":"string","format":"email"},"phone":{"type":"string"},"Sites":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"name":{"type":"string"},"contact":{"type":"string","format":"email"},"phone":{"type":"string"},"Address":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"businessHours":{},"blockoutDates":{},"Client":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"Groups":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"name":{"type":"string"},"Patients":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"firstName":{"type":"string"},"lastName":{"type":"string"},"nameSuffix":{"type":"string"},"namePrefix":{"type":"string"},"ssn":{"type":"string"},"pid":{"type":"string"},"Group":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"Race":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"value":{"type":"string"},"Category":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"name":{"type":"string"},"MasterData":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"Sex":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"dob":{"type":"string","format":"date"},"phone":{"type":"string"},"contact":{"type":"string","format":"email"},"CallSessions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"callSid":{"type":"string"},"attempts":{"type":"integer"},"callerNumber":{"type":"string"},"Patient":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"twilioBody":{},"language":{"type":"string","enum":["en","es"]},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"Client":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"SiteResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/Site"},"meta":{"type":"object"}}},"StateRequest":{"type":"object","required":["data"],"properties":{"data":{"required":["name","code"],"type":"object","properties":{"name":{"type":"string"},"code":{"type":"string"},"Status":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"Country":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"CreatedByUser":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"UpdatedByUser":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"locale":{"type":"string"},"localizations":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}}}}}},"StateListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/State"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"State":{"type":"object","required":["name","code"],"properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"code":{"type":"string"},"Status":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"Name":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"provider":{"type":"string"},"resetPasswordToken":{"type":"string"},"confirmationToken":{"type":"string"},"confirmed":{"type":"boolean"},"blocked":{"type":"boolean"},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"description":{"type":"string"},"type":{"type":"string"},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"Country":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"name":{"type":"string"},"code":{"type":"string"},"Status":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"codeThree":{"type":"string"},"States":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"code":{"type":"string"},"Status":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"Country":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"Region":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"Countries":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"Status":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"Continent":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"Status":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"Regions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"StateResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/State"},"meta":{"type":"object"}}},"StatusRequest":{"type":"object","required":["data"],"properties":{"data":{"required":["Name"],"type":"object","properties":{"Name":{"type":"string"},"CreatedByUser":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"UpdatedByUser":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"locale":{"type":"string"},"localizations":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}}}}}},"StatusListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/Status"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"Status":{"type":"object","required":["Name"],"properties":{"id":{"type":"number"},"documentId":{"type":"string"},"Name":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"provider":{"type":"string"},"resetPasswordToken":{"type":"string"},"confirmationToken":{"type":"string"},"confirmed":{"type":"boolean"},"blocked":{"type":"boolean"},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"description":{"type":"string"},"type":{"type":"string"},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"Name":{"type":"string"},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}}}},"StatusResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/Status"},"meta":{"type":"object"}}},"ViewSettingRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"name":{"type":"string"},"settings":{},"User":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"CreatedByUser":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"UpdatedByUser":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"locale":{"type":"string"},"localizations":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}}}}}},"ViewSettingListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/ViewSetting"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"ViewSetting":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"settings":{},"User":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"provider":{"type":"string"},"resetPasswordToken":{"type":"string"},"confirmationToken":{"type":"string"},"confirmed":{"type":"boolean"},"blocked":{"type":"boolean"},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"description":{"type":"string"},"type":{"type":"string"},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"settings":{},"User":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"CreatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"UpdatedByUser":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}}}},"ViewSettingResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/ViewSetting"},"meta":{"type":"object"}}},"UploadFile":{"properties":{"id":{"type":"number"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"number","format":"integer"},"height":{"type":"number","format":"integer"},"formats":{"type":"number"},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"double"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{"type":"object"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"}}},"Users-Permissions-Role":{"type":"object","properties":{"id":{"type":"number"},"name":{"type":"string"},"description":{"type":"string"},"type":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"}}},"Users-Permissions-User":{"type":"object","properties":{"id":{"type":"number","example":1},"username":{"type":"string","example":"foo.bar"},"email":{"type":"string","example":"<EMAIL>"},"provider":{"type":"string","example":"local"},"confirmed":{"type":"boolean","example":true},"blocked":{"type":"boolean","example":false},"createdAt":{"type":"string","format":"date-time","example":"2022-06-02T08:32:06.258Z"},"updatedAt":{"type":"string","format":"date-time","example":"2022-06-02T08:32:06.267Z"}}},"Users-Permissions-UserRegistration":{"type":"object","properties":{"jwt":{"type":"string","example":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"},"user":{"$ref":"#/components/schemas/Users-Permissions-User"}}},"Users-Permissions-PermissionsTree":{"type":"object","additionalProperties":{"type":"object","description":"every api","properties":{"controllers":{"description":"every controller of the api","type":"object","additionalProperties":{"type":"object","additionalProperties":{"description":"every action of every controller","type":"object","properties":{"enabled":{"type":"boolean"},"policy":{"type":"string"}}}}}}}}},"requestBodies":{"Users-Permissions-RoleRequest":{"required":true,"content":{"application/json":{"schema":{"type":"object","properties":{"name":{"type":"string"},"description":{"type":"string"},"type":{"type":"string"},"permissions":{"$ref":"#/components/schemas/Users-Permissions-PermissionsTree"}}},"example":{"name":"foo","description":"role foo","permissions":{"api::content-type.content-type":{"controllers":{"controllerA":{"find":{"enabled":true}}}}}}}}}}},"tags":[{"name":"Users-Permissions - Auth","description":"Authentication endpoints","externalDocs":{"description":"Find out more","url":"https://docs.strapi.io/developer-docs/latest/plugins/users-permissions.html"}},{"name":"Users-Permissions - Users & Roles","description":"Users, roles, and permissions endpoints","externalDocs":{"description":"Find out more","url":"https://docs.strapi.io/developer-docs/latest/plugins/users-permissions.html"}}]},
          dom_id: '#swagger-ui',
          docExpansion: "none",
          deepLinking: true,
          presets: [
            SwaggerUIBundle.presets.apis,
            SwaggerUIStandalonePreset,
          ],
          plugins: [
            SwaggerUIBundle.plugins.DownloadUrl,
          ],
          layout: "StandaloneLayout",
        });

        window.ui = ui;
      }
    </script>

    <script src="/plugins/documentation/swagger-ui-bundle.js"></script>
    <script src="/plugins/documentation/swagger-ui-standalone-preset.js"></script>
  </body>
</html>
