import { setAutogeneratedFields } from './utils'
import type { Core } from '@strapi/strapi'

export default (plugin: Core.Plugin) => {
	const controllers = {
		createContentType: plugin.controllers['content-types'].createContentType,
	}

	/** Overrides the createContentType controller to add CreatedByUser
   * and UpdatedBy<PERSON>ser autogenerated fields to all content types
   */
	plugin.controllers['content-types'].createContentType = async (ctx, next) => {
		const { body } = ctx.request as any
		setAutogeneratedFields(body)
		return controllers.createContentType(ctx, next)
	}
	return plugin
}
