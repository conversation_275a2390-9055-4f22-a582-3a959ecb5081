import { describe, expect, it } from '@jest/globals'
import { setAutogeneratedFields } from './index'

describe('assigns user', () => {
	it('should assign a user to the mutation arguments', async () => {
		const mock = {
			contentType: {
				attributes: {
					mockField: {
						type: 'string',
						required: true,
					},
				},
			},
		}
		setAutogeneratedFields(mock)
		expect(mock.contentType.attributes).toMatchObject({
			CreatedByUser: {
				type: 'relation',
				relation: 'oneToOne',
				target: 'plugin::users-permissions.user',
				configurable: false,
			},
			UpdatedByUser: {
				type: 'relation',
				relation: 'oneToOne',
				target: 'plugin::users-permissions.user',
				configurable: false,
			},
		})
	})
})

describe('assigns user error', () => {
	it('should return an error when assigning a user to the mutation arguments', async () => {
		const mock = {
			contentType: {
				attributes: {
					CreatedByUser: {
						type: 'relation',
						relation: 'oneToOne',
						target: 'plugin::users-permissions.user',
						configurable: false,
					},
				},
			},
		}
		expect(() => setAutogeneratedFields(mock)).toThrow(new Error('CreatedByUser or UpdatedByUser are autogenerated.'))
	})
})
