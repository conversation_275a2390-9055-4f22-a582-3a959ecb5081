import { has } from 'lodash'

export function setAutogeneratedFields(body: any) {
	if (has(body.contentType.attributes, 'CreatedByUser') || has(body.contentType.attributes, 'UpdatedByUser')) {
		throw new Error('CreatedByUser or UpdatedByUser are autogenerated.')
	}
	body.contentType.draftAndPublish = false
	body.contentType.attributes = {
		...body.contentType.attributes,
		CreatedByUser: {
			type: 'relation',
			relation: 'oneToOne',
			target: 'plugin::users-permissions.user',
			configurable: false,
		},
		UpdatedByUser: {
			type: 'relation',
			relation: 'oneToOne',
			target: 'plugin::users-permissions.user',
			configurable: false,
		},
	}
}
