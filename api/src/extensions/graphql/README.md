## Custom Resolvers

- Whenever a custom resolver is needed to be applied, either on existing queries or extended queries, it should be created as a separated directory in the **`/src/extensions/graphql/resolvers`**.
- After creating the directory, import and assign the resolver method to the **`resolverConfig`** .

## Extended Query/Mutation configuration

In order to be able to assign permissions to a certain extended **`GraphQL`** query or mutaion follow the steps.

### Step 1: Add the policy to the extended query/mutation

In **`service.ts`** file, where the extended query has been initialised add the policy, depending of the type of the extended query/mutation, to the `resolversConfig` property.

```javascript
resolversConfig: {
 // Extended Queries
 '<type>.<name>': {policies},
},
```

### Step 2: Create a controller

Inside the **`/src/api/custom-query-permissions/controllers`** directory, depening on wethear your extending a query or mutation, go to the appropriate controller file and create a new exported method.

Method should be exactly like:

```javascript
import {Context} from 'koa';
 export default {
   <extended query/mutation name>: (ctx: Context) => {
       ctx.badRequest('API not accessible');
} };
```

### Step 3: Create a route

Inside the routes directory create a route for the controller that was created.  
Assing the handler based on the controller that was created.

```javascript
export default {
  routes: [
    {
      method: 'GET',
      path: '/custom-query/<extended query/mutation name>',
      handler: '<controller name>.<extended query/mutation name>',
    },
  ],
}
```

> Keep in mind that the **controllers** and **routes** are only for strapi to think of them as legit api, and we will not have to ever used them.
