import _ from 'lodash'
import type { Context as KoaContext } from 'koa'

import type { GraphQLResolveInfo } from 'graphql/type/definition'
import { has } from 'lodash/fp'

// TODO:types should be moved in a separate file
export enum OperationType {
  Get = 'get',
  GetOne = 'getOne',
  Create = 'create',
  Update = 'update',
  Delete = 'delete',
}

export interface MiddlewareConfig {
  name: string
  operations: OperationType[]
}

interface Payload {
  [key: string]: string | number | boolean | unknown | null | undefined
}

export interface GetOnePayload {
  id: string
}

export interface UpdatePayload {
  id: string
  data: Payload
}

export interface CreatePayload {
  data: Payload
}

export interface DeletePayload {
  id: string
}

export interface PreliminaryReportPayload {
  id: string
  data: PreliminaryPayload[]
}

export interface PreliminaryPayload {
  disciplineId: string
  reports: string[]
}

export type Args = CreatePayload & GetOnePayload & UpdatePayload & DeletePayload & unknown

export type Context = KoaContext

export type GraphQLInfo = GraphQLResolveInfo

export type GraphQLMiddleware = (parent: unknown, args: Args, context: Context, info: GraphQLInfo) => Promise<GraphQLMiddleware>

export function withMiddlewares(resolverConfig: Record<string, any>, globalMiddlewares: MiddlewareConfig[]): unknown {
	const contentTypes = strapi.contentTypes
	for (const uid in contentTypes) {
		const key = uid as keyof typeof contentTypes
		if (contentTypes[key].kind === 'collectionType') {
			if (contentTypes[key].collectionName === 'admin_users' || contentTypes[key].collectionName === 'admin_permissions' || contentTypes[key].collectionName === 'admin_roles') continue
			const info = _.mapValues(contentTypes[key].info, _.rearg(_.camelCase, 0))
			const operations: { [key in OperationType]?: string } = {
				[OperationType.GetOne]: `Query.${info.singularName}`,
				[OperationType.Get]: `Query.${info.pluralName}`,
				[OperationType.Create]: `Mutation.create${_.upperFirst(info.singularName.toString())}`,
				[OperationType.Update]: `Mutation.update${_.upperFirst(info.singularName.toString())}`,
				[OperationType.Delete]: `Mutation.delete${_.upperFirst(info.singularName.toString())}`,
			}
			for (const operation in operations) {
				const operationKey = operations[operation as OperationType]
				if (!operationKey) continue

				const middlewares = [...globalMiddlewares.filter((middleware) => middleware.operations.map((op) => op.toString()).includes(operation)).map((middleware) => middleware.name), ...(resolverConfig[operationKey]?.middlewares ?? [])]
				const config = {
					...resolverConfig[operationKey],
					...(middlewares.length > 0 && { middlewares }),
				}
				if (_.isEmpty(config)) continue
				resolverConfig[operationKey] = config
			}
		}
	}
	return resolverConfig
}

export function isEntityStateUpdate(data: Payload): boolean {
	const requiredFields = ['UpdatedByUser', 'EntityState']
	return requiredFields.every((field) => has(field, data)) && Object.keys(data).length === requiredFields.length
}
