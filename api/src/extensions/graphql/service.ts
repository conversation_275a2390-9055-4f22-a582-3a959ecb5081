import fs from 'fs'
import path from 'path'
import { MiddlewareConfig, OperationType, withMiddlewares } from './utils'
import type { Core } from '@strapi/strapi'
import viewSettingCustomController from '../../api/view-setting/controllers/custom-controllers'

// Extended Queries policies
const customQueryPolicies = ['api::custom-query-permissions.policy']

// Declare here global GraphQL middlewares
const globalMiddlewares: MiddlewareConfig[] = [
	{
		name: 'global::created-updated-by',
		operations: [OperationType.Create, OperationType.Update],
	},
]

const resolverConfig = {
	// Extended Queries
}

export default function (service: Core.Service): void {
	service.use({
		typeDefs: fs.readFileSync(path.join(__dirname, '..', '..', '..', '..', 'src', 'extensions', 'graphql', 'schema-custom.graphql')).toString(),
		resolvers: {
			Query: {
				viewSettings: { resolve: viewSettingCustomController.viewSettings },
				viewSetting: { resolve: viewSettingCustomController.viewSetting },
			},
			Mutation: {
				createViewSetting: { resolve: viewSettingCustomController.createViewSetting },
				updateViewSetting: { resolve: viewSettingCustomController.updateViewSetting },
				deleteViewSetting: { resolve: viewSettingCustomController.deleteViewSetting },
			},
		},
		resolversConfig: withMiddlewares(resolverConfig, globalMiddlewares),
	})
}
