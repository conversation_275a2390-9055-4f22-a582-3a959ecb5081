import type { Core } from '@strapi/strapi'
import { UserEntity } from '../../../types/types'
import { randomBytes } from 'crypto'
import { errors } from '@strapi/utils'
import { isEmpty } from 'lodash'

export default (plugin: Core.Plugin): Core.Plugin => {
	const originalCreateContentManagerCreate = plugin.controllers.contentmanageruser.create

	plugin.controllers.user.me = async (ctx, next) => {
		const authUser = ctx.state.user as UserEntity
		ctx.body = {
			id: authUser.id,
			documentId: authUser.documentId,
			username: authUser.username,
			email: authUser.email,
			role: authUser.role,
		}
	}

	plugin.controllers.contentmanageruser.create = async (ctx, next) => {
		const { email, username, role } = ctx.request.body
		if (!email) {
			throw new errors.ValidationError('Email is required')
		}

		if (!ctx.request.body['username']) {
			ctx.request.body['username'] = username || email.split('@')[0]
		}
		if (isEmpty(role?.connect)) {
			throw new errors.ValidationError('User must have a role')
		}

		const emailOrUsernameTaken = await strapi.documents('plugin::users-permissions.user').count({
			filters: {
				$or: [{ email: email }, { username: username }],
			},
		})

		if (emailOrUsernameTaken) {
			throw new errors.ValidationError('Username or email already taken')
		}

		ctx.request.body['password'] = generateRandomPassword()
		ctx.request.body['confirmed'] = true
		await originalCreateContentManagerCreate(ctx, next)
		await sendEmailNotification(email, ctx.request.body['password'])
	}

	if (!plugin?.contentTypes?.role?.schema?.pluginOptions) {
		throw new Error('Plugin options are not defined')
	}

	plugin.contentTypes.role.schema.pluginOptions['content-manager'] = {
		visible: true,
	}
	plugin.contentTypes.role.schema.pluginOptions['content-type-builder'] = {
		visible: true,
	}
	return plugin
}

function generateRandomPassword(length = 12): string {
	const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+[]{}|;:,.<>?'
	const charsLength = chars.length
	const randomBuffer = randomBytes(length)

	let password = ''
	for (let i = 0; i < length; i++) {
		password += chars[randomBuffer[i] % charsLength]
	}

	return password
}

async function sendEmailNotification(email: string, password: string) {
	const frontendUrl = strapi.config.get('server.frontendUrl') + '/login'
	const appName = strapi.config.get('server.appName')

	await strapi.plugins['email'].services.email.send({
		to: email,
		subject: 'Your account has been created',
		html: `
        <p>Welcome to ${appName}!</p>
        <p>Your account has been created successfully.</p>
        <p>Your password is: <strong>${password}</strong></p>
        <p>Please log in at <a href="${frontendUrl}">${frontendUrl}</a></p>
    `,
	})
}
