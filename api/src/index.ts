import type { Core } from '@strapi/strapi'
import extendGraphqlSchemas from './extensions/graphql/service'
import { setMigrationTableDefaultColumns } from './bootstrap/migrations'
import runBeforeBootstrapMigrations from './bootstrap/run-before-bootstrap-migrations'
import updateUserPermissionsPluginStore from './bootstrap/update-user-permissions-plugin-store'

export default {
	/**
   * An asynchronous register function that runs before
   * your application is initialized.
   *
   * This gives you an opportunity to extend code.
   */
	register({ strapi }: { strapi: Core.Strapi }): void {
		const extensionService = strapi.plugin('graphql').service('extension')
		extendGraphqlSchemas(extensionService)
	},

	/**
   * An asynchronous bootstrap function that runs before
   * your application gets started.
   *
   * This gives you an opportunity to set up your data model,
   * run jobs, or perform some special logic.
   */
	async bootstrap(): Promise<void> {
		await setMigrationTableDefaultColumns()

		await Promise.all([runBeforeBootstrapMigrations(), updateUserPermissionsPluginStore()])
	},
}
