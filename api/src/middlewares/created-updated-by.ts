import { startsWith } from 'lodash'
import type { Args, Context, GraphQLInfo, GraphQLMiddleware } from '../extensions/graphql/utils'
import { UserEntity } from '../../types/types'

export default async function (next: GraphQLMiddleware, parent: unknown, args: Args, context: Context, info: GraphQLInfo): Promise<GraphQLMiddleware> {
	const { user } = context.state
	setUser(info.fieldName, user, args)
	return await next(parent, args, context, info)
}

export function setUser(operation: string, user: UserEntity, args: Args): void {
	if (startsWith(operation, 'create')) {
		delete args.data.UpdatedByUser
		args.data.CreatedByUser = user.documentId
	} else if (startsWith(operation, 'update')) {
		delete args.data.CreatedByUser
		args.data.UpdatedByUser = user.documentId
	}
}
