/**
 * `disable-rest` middleware
 */
import type { Context } from 'koa'

export const allowedRestRoutes: string[] = ['/users/me', '/auth/local', '/auth/local/register', '/auth/email-confirmation', '/search-input-filters', '/twilio/gather-pin', '/twilio/language-selection', '/twilio/verify-pin']

export default function () {
	return async (ctx: Context, next: () => Promise<void>): Promise<void> => {
		// Block REST API requests
		if (ctx.request.url.startsWith('/api') && !allowedRestRoutes.some((route) => ctx.request.url.startsWith('/api' + route))) {
			strapi.log.error(`Accessing ${ctx.request.url} from REST api was forbidden.`)
			ctx.response.status = 403
			ctx.forbidden('Rest API access is forbidden!')
			return
		}

		await next()
	}
}
