import { validateRequest } from 'twilio'
import type { Context, Next } from 'koa'
import { Strapi } from '@strapi/types/dist/core'

// Middleware to validate Twilio signatures
export default (config: Record<string, any>, { strapi }: { strapi: Strapi }) =>
	async (ctx: Context, next: Next): Promise<unknown> => {
		const authToken = strapi.config.get('server.twilio.authToken') as string
		const publicUrl = strapi.config.get('server.publicUrl') as string
		const signature = ctx.get('X-Twilio-Signature') || ''

		const url = `${publicUrl}${ctx.request.url}`
		const params = { ...ctx.request.body }

		if (!validateRequest(authToken, signature, url, params)) {
			strapi.log.warn('Invalid Twilio signature detected')
			ctx.throw(403, 'Invalid Twilio signature')
			return
		}

		await next()
	}
