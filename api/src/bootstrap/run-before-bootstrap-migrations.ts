import { exec } from 'child_process'

export default async function runBeforeBootstrapMigrations(): Promise<void> {
	await new Promise<void>((resolve, reject) => {
		exec('npx knex migrate:latest', (err, stdout, stderr) => {
			if (err) {
				strapi.log.error(err)
				reject(err)
				return
			}
			console.log(stdout)
			console.error(stderr)
			if (stdout.includes('migrations') || stdout.includes('Already up to date')) {
				strapi.log.info('before bootstrap migrations finished')
				resolve()
			} else {
				strapi.log.info('migration not finish yet..')
			}
		})
	})
}
