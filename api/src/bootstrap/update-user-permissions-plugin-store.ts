import { isEqual } from 'lodash'

interface EmailTemplate {
  display: string
  icon: string
  options: {
    from: {
      name: string
      email: string
    }
    response_email: string
    object: string
    message: string
  }
}

interface EmailTemplatesConfig {
  reset_password: EmailTemplate
  email_confirmation: EmailTemplate
}

interface EmailConfig {
  unique_email: boolean
  allow_register: boolean
  email_confirmation: boolean
  email_reset_password: string
  email_confirmation_redirection: string
  default_role: string
}

const emailTemplates: EmailTemplatesConfig = {
	reset_password: {
		display: 'Email.template.reset_password',
		icon: 'sync',
		options: {
			from: {
				name: process.env.APP_NAME!,
				email: process.env.MAILGUN_DEFAULT_FROM!,
			},
			response_email: process.env.MAILGUN_DEFAULT_FROM!,
			object: 'Reset password',
			message: '<p>We heard that you lost your password. Sorry about that!</p>\n\n<p>But don’t worry! You can use the following link to reset your password:</p>\n<p><%= URL %>?code=<%= TOKEN %></p>\n\n<p>Thanks.</p>',
		},
	},
	email_confirmation: {
		display: 'Email.template.email_confirmation',
		icon: 'check-square',
		options: {
			from: {
				name: process.env.APP_NAME!,
				email: process.env.MAILGUN_DEFAULT_FROM!,
			},
			response_email: process.env.MAILGUN_DEFAULT_FROM!,
			object: 'Account confirmation',
			message: '<p>Thank you for registering!</p>\n\n<p>You have to confirm your email address. Please click on the link below.</p>\n\n<p><%= URL %>?confirmation=<%= CODE %></p>\n\n<p>Thanks.</p>',
		},
	},
}

const emailTemplatesConfig: EmailConfig = {
	unique_email: true,
	allow_register: false,
	email_confirmation: false,
	email_reset_password: `${process.env.FRONTEND_URL}/reset-password`,
	email_confirmation_redirection: `${process.env.FRONTEND_URL}/login`,
	default_role: 'authenticated',
}

export default async function (): Promise<void> {
	const [existingTemplates, existingConfig] = await Promise.all([
    strapi.store({ type: 'plugin', name: 'users-permissions', key: 'email' }).get() as Promise<EmailTemplatesConfig>,
    strapi.store({ type: 'plugin', name: 'users-permissions', key: 'advanced' }).get() as Promise<EmailConfig>,
	])

	const updates = []

	if (!isEqual(existingTemplates, emailTemplates)) {
		updates.push(
			strapi
				.store({ type: 'plugin', name: 'users-permissions', key: 'email' })
				.set({ value: emailTemplates })
				.then(() => strapi.log.info('Email templates updated successfully.')),
		)
	}

	if (!isEqual(existingConfig, emailTemplatesConfig)) {
		updates.push(
			strapi
				.store({ type: 'plugin', name: 'users-permissions', key: 'advanced' })
				.set({ value: emailTemplatesConfig })
				.then(() => strapi.log.info('Email config updated successfully.')),
		)
	}

	await Promise.all(updates)
}
