const migrationTable = 'strapi_migrations'

async function checkColumnExists(name: string): Promise<boolean> {
	return await strapi.db.connection.schema.hasColumn(migrationTable, name)
}

export async function setMigrationTableDefaultColumns(): Promise<void> {
	if ((await checkColumnExists('batch')) && (await checkColumnExists('migration_time'))) {
		return
	}

	await strapi!.db!.connection.schema.alterTable(migrationTable, (table) => {
		table.integer('batch').defaultTo(1)
		table.timestamp('migration_time').defaultTo(strapi!.db!.connection.fn.now())

		return table
	})
}
