#server
HOST=0.0.0.0
PORT=1337

# Secrets
APP_KEYS="toBeModified1,toBeModified2"
API_TOKEN_SALT=tobemodified
ADMIN_JWT_SECRET=tobemodified
TRANSFER_TOKEN_SALT=tobemodified
JWT_SECRET=tobemodified
ENCRYPTION_KEY=tobemodified

# Database
DATABASE_CLIENT=
DATABASE_HOST=
DATABASE_PORT=
DATABASE_NAME=
DATABASE_USERNAME=
DATABASE_PASSWORD=
DATABASE_SSL=
DATABASE_SSL_REJECT_UNAUTHORIZED=false
DATABASE_FILENAME=

#app
APP_NAME="APP_NAME"
PUBLIC_URL=

# mailgun
MAILGUN_DOMAIN=
MAILGUN_URL=
MAILGUN_KEY=
MAILGUN_RECIPENT_EMAIL=
MAILGUN_DEFAULT_FROM=
MAILGUN_DEFAULT_REPLY_TO=

#CORS
ALLOWED_CORS_URLS='["http://localhost:8080"]'

# Frontend
FRONTEND_URL='http://localhost:8080'


#twilio
TWILIO_ACCOUNT_SID=
TWILIO_AUTH_TOKEN=
TWILIO_PHONE_NUMBER=
