const getMigrationDate = require('../database/migrations/helpers/getMigrationDate')

/**
 * @param { import('knex').Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
	const { nanoid } = await import('nanoid')

	const status = await knex('statuses').where({ name: 'Active' }).first()
	if (status) {
		console.log('Status "Active" already exists, skipping...')
		return
	}

	await knex('statuses')
		.insert({
			name: 'Active',
			...getMigrationDate(),
			document_id: nanoid(25),
		})
		.catch(console.error)
}

/**
 * @param { import('knex').Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {}
