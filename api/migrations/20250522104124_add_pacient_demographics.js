const addMasterData = require('../database/migrations/helpers/masterData')

const masterDatas = [
	{
		categoryName: 'Race',
		values: ['American Indian or Alaska Native', 'Asian', 'Black or African American', 'Native Hawaiian or Other Pacific Islander', 'White', 'Other'],
	},
	{
		categoryName: 'Sex',
		values: ['Male', 'Female', 'Unknown', 'Prefer not to say'],
	},
]

/**
 * @param { import('knex').Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
	await addMasterData(knex, masterDatas)
}
exports.down = function (knex) {}
