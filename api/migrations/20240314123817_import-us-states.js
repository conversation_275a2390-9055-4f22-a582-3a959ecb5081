const parseCSV = require('../database/migrations/helpers/parseCSV')
const getMigrationDate = require('../database/migrations/helpers/getMigrationDate')

const statesPerCountryCount = {}

async function insertStatesAndLink(knex, usaStatesData) {
	const countryRecord = await knex('countries').where('name', 'United States').first()
	const { nanoid } = await import('nanoid')

	for (const state of usaStatesData) {
		if (countryRecord) {
			let [stateRecord] = await knex('states').where('name', state.Alabama)
			if (!stateRecord) {
				;[stateRecord] = await knex('states').insert(
					{
						name: state.Alabama,
						code: state.AL,
						...getMigrationDate(),
						document_id: nanoid(25),
					},
					['id'],
				)
				console.log('inserted state: ', state.Alabama)
			}

			const res = await knex('states_country_lnk')
				.where({
					state_id: stateRecord.id,
					country_id: countryRecord.id,
				})
				.first()
			if (res) continue

			await linkStateToCountry(knex, stateRecord.id, countryRecord.id)
			console.log('linked state to country: ', stateRecord.id, countryRecord.id)
		}
	}
}

async function linkStateToCountry(knex, stateId, countryId) {
	const order = statesPerCountryCount[countryId] || 0

	await knex('states_country_lnk')
		.insert({
			state_id: stateId,
			country_id: countryId,
			state_ord: order,
		})
		.catch(console.error)

	statesPerCountryCount[countryId] = order + 1
}

/**
 * @param { import('knex').Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
	const usaStatesData = await parseCSV('./files/csv/usa_states.csv')
	await insertStatesAndLink(knex, usaStatesData)
}

/**
 * @param { import('knex').Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {}
