// eslint-disable-next-line no-undef
const getMigrationDate = require('../database/migrations/helpers/getMigrationDate')

/**
 * @param { import('knex').Knex } knex
 * @returns { Promise<void> }
 */
// eslint-disable-next-line no-undef
exports.up = async function (knex) {
	const statuses = ['Suspended', 'Blocked', 'Deleted', 'New', 'Planned', 'Reviewed', 'In Progress', 'Completed']
	const { nanoid } = await import('nanoid')

	for (const name of statuses) {
		const status = await knex('statuses').where({ name }).first()
		if (status) {
			console.log(`Status "${name}" already exists, skipping...`)
		} else {
			await knex('statuses')
				.insert({
					name,
					document_id: nanoid(25),
					...getMigrationDate(),
				})
				.catch(console.error)
		}
	}
}

/**
 * @param { import('knex').Knex } knex
 * @returns { Promise<void> }
 */
// eslint-disable-next-line no-undef
exports.down = async function (knex) {
	const statuses = ['Suspended', 'Blocked', 'Deleted', 'New', 'Planned', 'Reviewed', 'In Progress', 'Completed']

	// Delete the statuses added in the up function
	for (const name of statuses) {
		await knex('statuses').where({ name }).del().catch(console.error)
	}
}
