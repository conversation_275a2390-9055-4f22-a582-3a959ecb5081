const count = {}
const removeLastString = (string) => string.slice(0, -1)

/**
 * @param { import('knex').Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
	const tables = [
		{ name: 'continents', linkTable: 'continents_status_lnk' },
		{
			name: 'countries',
			linkTable: 'countries_status_lnk',
		},
		{ name: 'regions', linkTable: 'regions_status_lnk' },
		{ name: 'states', linkTable: 'states_status_lnk' },
	]
	const activeStatus = await knex('statuses').where({ name: 'Active' }).first()
	for (const table of tables) {
		await knex(table.name)
			.select('id')
			.then(async (rows) => {
				count[table.name] = rows.length
				for (const row of rows) {
					const isCountry = table.name === 'countries'
					if (isCountry) {
						if (
							await knex(table.linkTable)
								.where({
									country_id: row.id,
									status_id: activeStatus.id,
								})
								.first()
						)
							continue
						await knex(table.linkTable)
							.insert({
								['country_id']: row.id,
								status_id: activeStatus.id,
							})
							.catch(console.error)
					} else {
						if (
							await knex(table.linkTable)
								.where({
									[`${removeLastString(table.name)}_id`]: row.id,
									status_id: activeStatus.id,
								})
								.first()
						)
							continue
						await knex(table.linkTable)
							.insert({
								[`${removeLastString(table.name)}_id`]: row.id,
								status_id: activeStatus.id,
							})
							.catch(console.error)
					}
					count[table.name] -= 1
					console.log(`adding Active Status to ${table.name}, remaning ${count[table.name]}`)
				}
			})
	}
}

/**
 * @param { import('knex').Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {}
