const parseCSV = require('../database/migrations/helpers/parseCSV')
const getMigrationDate = require('../database/migrations/helpers/getMigrationDate')

const regionsPerContinentCount = {}

async function insertRegionsAndLink(knex, countriesData) {
	const { nanoid } = await import('nanoid')

	const regionsWithContinents = countriesData.reduce((acc, item) => {
		const { Region, Continent } = item
		if (Region && Continent && !acc.some((r) => r.Region === Region)) {
			acc.push({ Region, Continent })
		}
		return acc
	}, [])

	for (const { Region, Continent } of regionsWithContinents) {
		console.log('Region: ', Region, 'Continent: ', Continent)
		const continentRecord = await knex('continents').where('name', Continent).first()

		if (continentRecord) {
			let [regionRecord] = await knex('regions').where('name', Region)
			if (!regionRecord) {
				;[regionRecord] = await knex('regions').insert(
					{
						name: Region,
						document_id: nanoid(25),
						...getMigrationDate(),
					},
					['id'],
				)
				console.log('inserted region: ', Region)
			}
			const res = await knex('regions_continent_lnk')
				.where({
					region_id: regionRecord.id,
					continent_id: continentRecord.id,
				})
				.first()
			if (res) continue

			await linkRegionToContinent(knex, regionRecord.id, continentRecord.id)
		}
	}
}

async function linkRegionToContinent(knex, regionId, continentId) {
	const order = regionsPerContinentCount[continentId] || 0

	await knex('regions_continent_lnk')
		.insert({
			region_id: regionId,
			continent_id: continentId,
			region_ord: order,
		})
		.catch(console.error)
	console.log('inserted region link: ', regionId, continentId, order)
	regionsPerContinentCount[continentId] = order + 1
}

/**
 * @param { import('knex').Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
	const countriesData = await parseCSV('./files/csv/countries.csv')
	await insertRegionsAndLink(knex, countriesData)
}

/**
 * @param { import('knex').Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {}
