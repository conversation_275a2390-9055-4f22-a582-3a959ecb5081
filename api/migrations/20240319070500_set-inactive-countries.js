const getMigrationDate = require('../database/migrations/helpers/getMigrationDate')

/**
 * @param { import('knex').Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
	let inactiveStatus

	const status = await knex('statuses').where({ name: 'Inactive' }).first()
	if (status) {
		console.log('Status "Inactive" already exists, skipping...')
		inactiveStatus = status
	} else {
		const [insertedStatus] = await knex('statuses')
			.insert({
				name: 'Inactive',
				...getMigrationDate(),
			})
			.returning('*')
			.catch(console.error)

		if (!insertedStatus) {
			throw new Error('Failed to insert new status "Inactive"')
		}

		inactiveStatus = insertedStatus
		console.log('Status "Inactive" created')
	}

	await knex('countries')
		.select('id')
		.whereNotIn('name', ['United States'])
		.then(async (rows) => {
			for (const row of rows) {
				const exists = await knex('countries_status_lnk').where({ country_id: row.id }).first()
				if (exists) {
					await knex('countries_status_lnk')
						.where({ country_id: row.id })
						.update({
							status_id: inactiveStatus.id,
						})
						.catch(console.error)
				} else {
					await knex('countries_status_lnk')
						.insert({
							country_id: row.id,
							status_id: inactiveStatus.id,
						})
						.catch(console.error)
				}
				console.log(`Setting Inactive Status for country ${row.id}`)
			}
		})
		.catch(console.error)
}

/**
 * @param { import('knex').Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {}
