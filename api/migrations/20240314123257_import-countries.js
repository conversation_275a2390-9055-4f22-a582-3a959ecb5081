const parseCSV = require('../database/migrations/helpers/parseCSV')
const getMigrationDate = require('../database/migrations/helpers/getMigrationDate')

const countriesPerRegionCount = {}

async function linkCountryToRegion(knex, countryId, regionId) {
	const order = countriesPerRegionCount[regionId] || 0
	// Insert the link with the determined order
	await knex('countries_region_lnk')
		.insert({
			country_id: countryId,
			region_id: regionId,
			country_ord: order,
		})
		.catch(console.error)

	countriesPerRegionCount[regionId] = order + 1
}

async function insertCountries(knex, countriesData) {
	const { nanoid } = await import('nanoid')

	for (const country of countriesData) {
		const regionRecord = await knex('regions').where('name', country.Region).first()
		const continentRecord = await knex('continents').where('name', country.Continent).first()

		if (regionRecord && continentRecord) {
			let [countryRecord] = await knex('countries').where('name', country.Country)
			if (!countryRecord) {
				;[countryRecord] = await knex('countries').insert(
					{
						name: country.Country,
						code: country['ISO(2)'],
						code_three: country['ISO(3)'],
						...getMigrationDate(),
						document_id: nanoid(25),
					},
					['id'],
				)
				console.log('inserted country: ', country.Country)
			}

			const res = await knex('countries_region_lnk')
				.where({
					country_id: countryRecord.id,
					region_id: regionRecord.id,
				})
				.first()
			if (res) continue

			await linkCountryToRegion(knex, countryRecord.id, regionRecord.id)
			console.log('linked country to region: ', countryRecord.id, regionRecord.id)
		}
	}
}

/**
 * @param { import('knex').Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
	const countriesData = await parseCSV('./files/csv/countries.csv')
	await insertCountries(knex, countriesData)
}

/**
 * @param { import('knex').Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {}
