const addMasterData = require('../database/migrations/helpers/masterData')

const masterDatas = [
	{
		categoryName: 'Race',
		values: ['White'],
	},
	{
		categoryName: 'Sex',
		values: ['Unknown'],
	},
]

/**
 * @param { import('knex').Knex } knex
 * @returns { Promise<void> }
 */

exports.up = async function (knex) {
	const values = masterDatas.flatMap((md) => md.values)
	console.log(values)

	await knex('master_datas').whereIn('value', values).del()
	await addMasterData(knex, masterDatas)
}
exports.down = function (knex) {}
