const parseCSV = require('../database/migrations/helpers/parseCSV')
const getMigrationDate = require('../database/migrations/helpers/getMigrationDate')

async function insertContinents(knex, continents) {
	const { nanoid } = await import('nanoid')

	for (const name of continents) {
		const exists = await knex('continents').where('name', name).first()
		if (exists) continue
		console.log('inserting continent: ', name)
		await knex('continents')
			.insert({
				name,
				...getMigrationDate(),
				document_id: nanoid(25),
			})
			.catch(console.error)
	}
}

/**
 * @param { import('knex').Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
	const countriesData = await parseCSV('./files/csv/countries.csv')
	const uniqueContinents = [...new Set(countriesData.map((item) => item.Continent).filter(Boolean))]
	await insertContinents(knex, uniqueContinents)
}

/**
 * @param { import('knex').Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {}
