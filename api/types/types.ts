import type { Data } from '@strapi/strapi'
import * as AttributeUtils from '@strapi/types/dist/modules/documents/params/attributes'
import * as UID from '@strapi/types/dist/uid'
import { Event, Params } from '@strapi/database/dist/lifecycles'

export type UserEntity = Data.Entity<'plugin::users-permissions.user'>

export type LifecycleEvent<T extends UID.Schema> = Omit<Event, 'result' | 'params'> & {
  result?: AttributeUtils.GetValues<T>
  params: Omit<Params, 'data'> & {
    data: Omit<AttributeUtils.GetValues<T>, 'id' | 'documentId' | 'localizations' | 'locale'>
  }
}

export interface TwilioCallData {
  Called: string
  ToState: string
  CallerCountry: string
  Direction: string
  CallerState: string
  ToZip: string
  CallSid: string
  To: string
  CallerZip: string
  ToCountry: string
  CallToken: string
  CalledZip: string
  ApiVersion: string
  CalledCity: string
  CallStatus: string
  From: string
  AccountSid: string
  CalledCountry: string
  CallerCity: string
  ToCity: string
  FromCountry: string
  Caller: string
  FromCity: string
  CalledState: string
  FromZip: string
  FromState: string
  selectedLanguage: 'en' | 'fr'
}

export interface TwilioGatherPayload {
  msg: string
  Called: string
  Digits: string
  ToState: string
  CallerCountry: string
  Direction: string
  CallerState: string
  ToZip: string
  CallSid: string
  To: string
  CallerZip: string
  ToCountry: string
  FinishedOnKey: string
  CalledZip: string
  ApiVersion: string
  CalledCity: string
  CallStatus: string
  From: string
  AccountSid: string
  CalledCountry: string
  CallerCity: string
  ToCity: string
  FromCountry: string
  Caller: string
  FromCity: string
  CalledState: string
  FromZip: string
  FromState: string
}
