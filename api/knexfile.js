require('dotenv').config()

/**
 * @type { Object.<string, import('knex').Knex.Config> }
 */
const dbConfig = {
  client: process.env.DATABASE_CLIENT,
  connection: {
    host: process.env.DATABASE_HOST,
    database: process.env.DATABASE_NAME,
    user: process.env.DATABASE_USERNAME,
    password: process.env.DATABASE_PASSWORD,
    port: process.env.DATABASE_PORT,
    ssl:
      process.env.DATABASE_SSL === 'false' || !process.env.DATABASE_SSL
        ? undefined
        : { rejectUnauthorized: process.env.DATABASE_SSL_REJECT_UNAUTHORIZED === 'true' },
    DATABASE_SSL: process.env.DATABASE_SSL === 'true',
  },
  pool: {
    min: 2,
    max: 10,
  },
  migrations: {
    tableName: 'strapi_migrations',
    disableMigrationsListValidation: true,
  },
}

module.exports = {
  production: dbConfig,
  development: dbConfig,
}
