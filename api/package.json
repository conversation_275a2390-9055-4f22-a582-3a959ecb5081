{"name": "api", "version": "0.1.0", "private": true, "description": "A Strapi application", "scripts": {"build": "strapi build", "console": "strapi console", "deploy": "strapi deploy", "dev": "strapi develop", "develop": "strapi develop", "start": "strapi start", "strapi": "strapi", "upgrade": "npx @strapi/upgrade latest", "upgrade:dry": "npx @strapi/upgrade latest --dry"}, "dependencies": {"@strapi/plugin-cloud": "5.13.0", "@strapi/plugin-documentation": "^5.13.0", "@strapi/plugin-graphql": "^5.13.0", "@strapi/plugin-users-permissions": "5.13.0", "@strapi/provider-email-mailgun": "^5.13.0", "@strapi/strapi": "5.13.0", "@types/koa2-ratelimit": "^0.9.6", "bcryptjs": "^3.0.2", "better-sqlite3": "11.3.0", "csv-parser": "^3.2.0", "knex": "^3.1.0", "koa2-ratelimit": "^1.1.3", "lru-cache": "^11.1.0", "nanoid": "^5.1.5", "pg": "^8.16.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "styled-components": "^6.0.0", "twilio": "^5.6.1"}, "devDependencies": {"@eslint/js": "^9.27.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^5.4.0", "globals": "^16.1.0", "typescript": "^5", "typescript-eslint": "^8.32.1"}, "engines": {"node": ">=18.0.0 <=22.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************", "installId": "606aa1ae04346ec17dcbfcad5927c6ab73486a448f6fa452635120a4f5886d1d"}}