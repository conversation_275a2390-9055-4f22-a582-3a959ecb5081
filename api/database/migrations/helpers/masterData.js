const getMigrationDate = require('./getMigrationDate')

async function addMasterData(knex, masterDatas) {
	const { nanoid } = await import('nanoid') // dynamic import here
	for (const { categoryName, values } of masterDatas) {
		let category = await knex('master_data_categories').where('name', categoryName).first()
		if (!category) {
			category = await knex
				.insert([
					{
						...getMigrationDate(),
						name: categoryName,
						document_id: nanoid(25),
					},
				])
				.into('master_data_categories')
		}
		const categoryId = await knex('master_data_categories').where({ name: categoryName }).select('id').first()
		if (values) {
			for (const value of values) {
				const existingMasterDatas = await knex('master_datas_category_lnk').where({ master_data_category_id: categoryId.id })

				const valueExistsInCategory = await knex('master_datas')
					.whereIn(
						'id',
						existingMasterDatas.map((masterData) => masterData.master_data_id),
					)
					.where('value', value)
					.first()

				if (valueExistsInCategory) continue

				const existingValue = existingMasterDatas.find((masterData) => masterData.value === value)

				if (!existingValue) {
					console.log('Creating value', value, 'for category', categoryName)
					await knex
						.insert([
							{
								value,
								...getMigrationDate(),
								document_id: nanoid(25),
							},
						])
						.into('master_datas')
					const masterDataId = await knex('master_datas').where({ value }).select('id').orderBy('id', 'desc').first()
					await knex
						.insert([
							{
								master_data_id: masterDataId.id,
								master_data_category_id: categoryId.id,
							},
						])
						.into('master_datas_category_lnk')
				}
			}
		}
	}
}

module.exports = addMasterData
