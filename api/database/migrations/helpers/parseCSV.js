const csvParser = require('csv-parser');
const fs = require('fs');

// eslint-disable-next-line no-undef
module.exports = function parseCSV(filePath) {
	const data = [];
	return new Promise((resolve, reject) => {
		fs.createReadStream(filePath)
			.pipe(csvParser({
				mapHeaders: ({ header, index }) => header.trim(),
				mapValues: ({ header, index, value }) => value.trim(),
			}))
			.on('data', (row) => data.push(row))
			.on('end', () => resolve(data))
			.on('error', reject);
	});
};