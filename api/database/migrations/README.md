# Migrations

A migration in Knex.js is a JavaScript file that outlines the changes to be applied to the database schema. It typically
contains two main functions: up and down. The up function defines the transformations to be applied to the database,
such as creating tables, adding columns, or inserting data. The down function provides the steps to revert these
changes, ensuring that the migration can be rolled back if necessary.

### Creating a Migration

To create a new migration, use the following command:

```bash
npx knex migrate:make `name`
```

Replace `name` with a descriptive name for your migration. This command will generate a new migration file in the
`migrations` directory.

### Using `setAuditVars` at the Beginning of Each Migration

Using `setAuditVars` at the Beginning of Each Migration
At the beginning of each migration, you need to call the setAuditVars function. This function sets the necessary audit
variables for tracking changes. This ensures that every migration has the required audit fields, such as timestamps and
user information, to maintain data integrity and traceability.

Example:

```javascript
const setAuditVars = require('../database/migrations/helpers/setAuditVars');

/**
 * @param {knex} knex
 */
exports.up = async function (knex) {
    await setAuditVars(knex);
    // Your migration code here
};

exports.down = function (knex) {
    // Code to undo the migration
};
```

### Using `getSystemUser` for Insert or Update Operations

For each insert or update operation, use the `getSystemUser` function to link the changes to the `created_by` and
`updated_by` fields.

Example:

```javascript
const getSystemUser = require('../database/migrations/helpers/getSystemUser');

/**
 * @param {knex} knex
 */
exports.up = async function (knex) {
    await setAuditVars(knex);
    const user = await getSystemUser(knex);

    // Your migration code here
};

exports.down = function (knex) {
    // Code to undo the migration
};
```

By following these guidelines, you ensure that all changes are properly audited and linked to the system user.