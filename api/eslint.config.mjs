import globals from 'globals'
import pluginJs from '@eslint/js'
import tseslint from 'typescript-eslint'

// eslint-disable-next-line no-undef
const isWindows = process?.platform === 'win32'

export default [
	{ files: ['**/*.{js,mjs,cjs,ts}'] },
	{
		languageOptions: {
			globals: globals.browser,
			parserOptions: {
				ecmaVersion: 2022,
				sourceType: 'module',
				parser: tseslint.parser,
			},
		},
	},
	pluginJs.configs.recommended,
	tseslint.configs.base,
	...tseslint.configs.recommended,
	{
		settings: {
			'import/resolver': {
				node: {
					extensions: ['.js', '.jsx', '.ts', '.tsx'],
					moduleDirectory: ['node_modules', 'src'],
				},
				typescript: {
					alwaysTryTypes: true,
					project: './tsconfig.json',
				},
			},
		},
	},
	{
		rules: {
			// General JS/TS rules
			indent: ['error', 'tab'],
			'linebreak-style': ['error', isWindows ? 'windows' : 'unix'],
			quotes: ['error', 'single'],
			'comma-dangle': ['warn', 'always-multiline'],
			'consistent-return': 'error',
			eqeqeq: ['error', 'always'],
			'prefer-arrow-callback': 'error',
			'arrow-body-style': ['error', 'as-needed'],
			'object-curly-spacing': ['error', 'always'],
			'array-bracket-spacing': ['error', 'never'],
			'template-curly-spacing': ['error', 'never'],
			'max-params': ['error', 8],
			'max-lines': ['warn', 1000],
			complexity: ['warn', 25],
			'max-depth': ['warn', 5],
			'prefer-const': 'error',
			'no-mixed-operators': 'error',
			'prefer-object-spread': 'warn',

			// TypeScript specific rules
			'@typescript-eslint/no-empty-function': 'warn',
			'@typescript-eslint/no-explicit-any': 'warn',
			'@typescript-eslint/explicit-module-boundary-types': 'warn',
			'@typescript-eslint/no-unused-vars': 'error',
			'@typescript-eslint/consistent-type-definitions': ['error', 'interface'],
			'@typescript-eslint/no-inferrable-types': 'error',
			'@typescript-eslint/ban-ts-ignore': 'off',
		},
	},
	{
		files: ['**/*.{js,mjs,ts}'],
	},
]
