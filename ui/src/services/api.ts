import axios from 'axios'
import { buildBaseBackendUrl } from '@/utils/helpers.ts'
import { useAuthStore } from '@/stores/auth.store.ts'

export const authEvents = new EventTarget()

const api = axios.create({
  baseURL: `${buildBaseBackendUrl()}/api`,
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
  },
})

api.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore()
    const token = authStore.jwt
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)

api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    if (error.response && error.response.status === 401) {
      console.info('Token expired or invalid, logging out...')
      authEvents.dispatchEvent(new Event('logout'))
    }
    return Promise.reject(error)
  },
)

export default api
