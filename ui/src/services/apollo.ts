import { ApolloClient, ApolloLink, createHttpLink, from, InMemoryCache } from '@apollo/client/core'
import { buildBaseBackendUrl } from '@/utils/helpers'
import { onError } from '@apollo/client/link/error'
import type { ServerParseError } from '@apollo/client/link/http/parseAndCheckHttpResponse'

import createUploadLink from 'apollo-upload-client/createUploadLink.mjs'
import { useAuthStore } from '@/stores/auth.store.ts'
import type { HttpOptions } from '@apollo/client/link/http/selectHttpOptionsAndBody'
import { authEvents } from '@/services/api.ts'

// @ts-expect-error - This is a valid import
const authMiddleware = new ApolloLink(async (operation, forward) => {
  const authStore = useAuthStore()
  const headers = operation.getContext().headers ?? {}
  operation.setContext({
    headers: {
      ...headers,
      Authorization: authStore.isAuthenticated ? `Bearer ${authStore.jwt}` : '',
    },
  })

  return forward(operation)
})

const errorLink = onError(({ networkError }) => {
  if ((networkError as ServerParseError)?.statusCode === 401) {
    console.info('Token expired or invalid, logging out...')
    authEvents.dispatchEvent(new Event('logout'))
  }
})

const httpOptions: HttpOptions = {
  uri: `${buildBaseBackendUrl()}/graphql`,
}

const httpLink = ApolloLink.split((operation) => operation.getContext().hasUpload, createUploadLink(httpOptions), createHttpLink(httpOptions))

const cache = new InMemoryCache()
const apolloClient = new ApolloClient({
  link: from([authMiddleware, errorLink, httpLink]),
  cache,
  devtools: {
    enabled: true,
  },
  defaultOptions: {
    watchQuery: {
      fetchPolicy: 'network-only',
    },
    mutate: {
      fetchPolicy: 'no-cache', // This will disable caching for mutations
    },
  },
})

export default apolloClient
