import api from './api'
import type { UsersPermissionsUser } from '@/gql/graphql.ts'

export interface RegisterData {
  username: string
  email: string
  password: string
}

export interface LoginData {
  identifier: string // Can be email or username
  password: string
}

export interface AuthResponse {
  jwt: string
  user: UsersPermissionsUser
}

class Auth {
  async register(data: RegisterData): Promise<AuthResponse> {
    try {
      const response = await api.post('/auth/local/register', data)
      return response.data
    } catch (error) {
      console.error('Registration API error:', error)
      throw error
    }
  }

  async login(data: LoginData): Promise<AuthResponse> {
    try {
      const response = await api.post('/auth/local', data)
      return response.data
    } catch (error) {
      throw error
    }
  }

  async getCurrentUser(): Promise<AuthResponse['user']> {
    try {
      const response = await api.get('/users/me')
      return response.data
    } catch (error) {
      console.error('Get current user API error:', error)
      throw error
    }
  }
}

export default new Auth()
