import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import authService, { type LoginData, type RegisterData } from '@/services/auth.ts'
import api from '../services/api'
import { useModelStore } from './model.store.ts'
import type { GeneratedModelSchema } from '@/utils/types.ts'
import { useLocalStorage } from '@vueuse/core'
import type { UsersPermissionsUser } from '@/gql/graphql.ts'

export const useAuthStore = defineStore('auth', () => {
  const user = useLocalStorage<UsersPermissionsUser | null>('user', null)
  const jwt = useLocalStorage<string | null>('jwt', null)

  const loading = ref(false)
  const error = ref<string | null>(null)
  const tokenValidated = ref(false)

  const isAuthenticated = computed(() => !!jwt.value || !!user.value)
  const isLoading = computed(() => loading.value)
  const getUser = computed(() => user.value)
  const getError = computed(() => error.value)

  async function register(registerData: RegisterData) {
    loading.value = true
    error.value = null

    try {
      await authService.register(registerData)
    } catch (err: any) {
      console.error('Registration store error:', err)
      error.value = err.response?.data?.error?.message || err.message || 'Registration failed'
      throw err
    } finally {
      loading.value = false
    }
  }

  async function login(loginData: LoginData) {
    loading.value = true
    error.value = null

    try {
      const response = await authService.login(loginData)

      jwt.value = response.jwt
      user.value = response.user
      localStorage.setItem('user', JSON.stringify(response.user))

      return response
    } catch (err: any) {
      error.value = err.response?.data?.error?.message || err.message || 'Login failed'
      throw err
    } finally {
      loading.value = false
    }
  }

  function logout() {
    jwt.value = null
    user.value = null
    tokenValidated.value = false
    localStorage.removeItem('user')
    localStorage.removeItem('jwt')
  }

  async function validateToken() {
    if (!jwt.value) {
      console.warn('No JWT token found, skipping validation.')
      return false
    }

    try {
      loading.value = true
      const currentUser = await authService.getCurrentUser()
      user.value = currentUser
      localStorage.setItem('user', JSON.stringify(currentUser))
      tokenValidated.value = true
      const models = await getSearchInputFilters()
      useModelStore().setModels(models)
      return true
    } catch (err) {
      console.error('Token validation failed:', err)

      logout()
      return false
    } finally {
      loading.value = false
    }
  }

  return {
    user,
    jwt,
    loading,
    error,
    tokenValidated,
    isAuthenticated,
    isLoading,
    getUser,
    getError,
    register,
    login,
    logout,
    validateToken,
  }
})

async function getSearchInputFilters() {
  const response = await api.get<GeneratedModelSchema>('/search-input-filters')
  return response.data
}
