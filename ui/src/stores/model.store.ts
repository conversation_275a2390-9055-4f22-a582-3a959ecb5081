import type { Ref } from 'vue'
import { ref } from 'vue'
import { defineStore } from 'pinia'
import type { GeneratedModelSchema, Model } from '@/utils/types'
import localforage from 'localforage'

const store = localforage.createInstance({
  driver: localforage.INDEXEDDB,
})

export const useModelStore = defineStore(
  'model',
  () => {
    const models: Ref<GeneratedModelSchema> = ref<GeneratedModelSchema>({})

    function setModels(modelsData: GeneratedModelSchema) {
      models.value = JSON.parse(JSON.stringify(modelsData))
    }

    function addModel(modelData: Model) {
      models.value[modelData.globalId] = modelData
    }

    function removeModel(modelData: Model) {
      delete models.value[modelData.globalId]
    }

    function updateModel(modelData: Model) {
      models.value[modelData.globalId] = JSON.parse(JSON.stringify(modelData))
    }

    function getModel(globalId: string): Model | null {
      return models.value?.[globalId] ? JSON.parse(JSON.stringify(models.value[globalId])) : null
    }

    function getField(modelName: string, fieldName: string) {
      return models.value[modelName].fields.find((f) => f.name === fieldName)
    }

    function getModelFields(modelName: string) {
      return models.value[modelName]?.fields || []
    }

    function clearStore() {
      models.value = {}
    }

    function getManyOperation(modelName: string) {
      return `${models.value[modelName]?.pluralName}_connection`
    }

    return {
      models,
      setModels,
      addModel,
      updateModel,
      removeModel,
      getModel,
      clearStore,
      getField,
      getModelFields,
      getManyOperation,
    }
  },
  {
    persist: {
      active: true,
      persistOptions: [
        {
          storage: store as unknown as Storage,
          key: 'model',
        },
      ],
    },
    share: { active: true },
  },
)
