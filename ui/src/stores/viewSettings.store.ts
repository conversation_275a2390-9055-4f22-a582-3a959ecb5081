import type { Ref } from 'vue'
import { ref } from 'vue'
import { defineStore } from 'pinia'
import type { JSONObject } from '@/utils/types'

import localforage from 'localforage'

const store = localforage.createInstance({
  driver: localforage.INDEXEDDB,
})

export const useViewSettingsStore = defineStore(
  'viewSettings',
  () => {
    const viewSettings: Ref<JSONObject> = ref({})

    function setInitialViewSettings(value: JSONObject) {
      viewSettings.value = value
    }

    function addViewSettings(key: string, value: JSONObject) {
      viewSettings.value = { ...viewSettings.value, [key]: value }
    }

    function updateViewSettings(key: string, value: JSONObject) {
      viewSettings.value = { ...viewSettings.value, [key]: value }
    }

    function removeViewSettings(key: string) {
      delete viewSettings.value[key]
    }

    return { viewSettings, setInitialViewSettings, addViewSettings, updateViewSettings, removeViewSettings }
  },
  {
    persist: {
      active: true,
      persistOptions: [
        {
          storage: store as unknown as Storage,
          key: 'viewSettings',
        },
      ],
    },
    share: { active: true },
  },
)
