@mixin custom-form-style {
  .el-form-item {
    margin-bottom: 0;
    padding-bottom: 18px;

    &.is-error {
      :deep(.el-form-item__content) {
        border-color: var(--color-red-300);
      }
    }
  }

  :deep(.el-form-item__label) {
    background-color: var(--color-slate-100);
    min-height: 50px;
    height: 100%;
    padding-right: 0.75rem;
    font-weight: var(--font-weight-medium);
    color: var(--color-slate-600);
    padding-left: 0.9rem;
    min-width: 7rem;
    align-items: center;
    // add border-radius to left side
    border-radius: 0.4rem 0 0 0.4rem;
  }

  :deep(.el-form-item__content) {
    border: 2px solid var(--color-slate-100);
    width: 100%;
    // add border radius to right side
    border-radius: 0 0.4rem 0.4rem 0;

    .el-input__inner,
    .el-select__selected-item,
    .el-textarea__inner {
      box-shadow: none !important;
      border: none !important;
    }

    .el-input {
      box-shadow: none !important;
      border: none !important;
    }

    .el-input__wrapper {
      background-color: transparent;
      box-shadow: none !important;
      border: none;
    }

    .el-input__wrapper.is-focus {
      background-color: transparent;
      box-shadow: none !important;
    }

    .el-select__wrapper {
      background-color: transparent;
      box-shadow: none !important;
    }

    .el-input__inner {
      text-align: left !important;
    }

    .el-input__inner {
      text-align: left !important;
    }

    .el-select-dropdown__item {
      margin-bottom: 2px !important;
      color: var(--color-slate-700) !important;
    }

    .el-select-dropdown__item.is-selected {
      font-weight: var(--font-weight-normal) !important;
      background: var(--p-primary-50) !important;
      color: var(--color-slate-700) !important;
    }

    .el-select-dropdown__item.is-hovering {
      background: var(--color-slate-100) !important;
    }
  }

  :deep(.el-textarea__inner) {
  }
}

@mixin custom-disabled-form-style {
  :deep(.el-form-item__content:has(.el-input.is-disabled)) {
    background-color: var(--color-slate-100);
  }
  :deep(.el-form-item__content:has(.el-textarea.is-disabled)) {
    background-color: var(--color-slate-100);
  }
  :deep(.el-form-item__content:has(span.is-disabled)) {
    background-color: var(--color-slate-100);
  }
  :deep(.el-form-item__content:has(div.is-disabled)) {
    background-color: var(--color-slate-100);
  }
  :deep(.el-form-item__content:has(.el-select__wrapper.is-disabled)) {
    background-color: var(--color-slate-100);
  }
  :deep(.el-input) {
    &.is-disabled {
      & .el-input__wrapper {
        background-color: transparent;
        box-shadow: unset;
      }

      & .el-input__inner {
        color: var(--color-slate-400);
        -webkit-text-fill-color: var(--color-slate-400);
      }

      & .el-textarea__inner {
        background-color: transparent;
        box-shadow: unset;
      }
    }
  }

  :deep(.el-textarea) {
    &.is-disabled {
      & .el-textarea__inner {
        background-color: transparent;
        box-shadow: unset;
        padding-top: 0 !important;
      }
    }
  }

  :deep(.el-select__wrapper) {
    &.is-disabled {
      background-color: transparent;
      box-shadow: unset;
    }
  }
}

@mixin custom-scrollbar {
  ::-webkit-scrollbar {
    @apply w-1 h-1;
  }

  ::-webkit-scrollbar-track {
    @apply bg-slate-100 rounded-full;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-slate-300 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-slate-400;
  }

  ::-webkit-scrollbar-corner {
    @apply bg-slate-100;
  }
}
