/* src/main.css */
@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');
@import 'tailwindcss';
@import 'primeicons/primeicons.css';

:root {
  /*prime vue colors so we can use them on element plus compoments*/

  --primay-color: #10b981;

  --p-primary-50: #ecfdf5;
  --p-primary-100: #d1fae5;
  --p-primary-200: #a7f3d0;
  --p-primary-300: #6ee7b7;
  --p-primary-400: #34d399;
  --p-primary-600: #059669;
  --p-primary-700: #047857;
  --p-primary-800: #065f46;
  --p-primary-900: #064e3b;
  --p-primary-950: #022c22;

  --el-color-primary: var(--primay-color);
}

@theme {
  --font-display: 'Inter', 'sans-serif';
}

@layer base {
  body {
    min-height: 100vh;
    transition: color 0.5s,
    background-color 0.5s;
    line-height: var(--leading-normal);
    font-family: var(--font-display);
    font-size: var(--text-base);
    background-color: var(--color-slate-50);
    color: var(--color-slate-600);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Global slim scrollbar styles */
  * {
    scrollbar-width: thin;
    scrollbar-color: theme(colors.slate.300) theme(colors.slate.100);
  }

  ::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  ::-webkit-scrollbar-track {
    background: theme(colors.slate.100);
    border-radius: 9999px;
  }

  ::-webkit-scrollbar-thumb {
    background: theme(colors.slate.300);
    border-radius: 9999px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: theme(colors.slate.400);
  }

  ::-webkit-scrollbar-corner {
    background: theme(colors.slate.100);
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p,
  span,
  a,
  li,
  blockquote,
  code {
    color: inherit;
  }

  h1 {
    font-size: var(--text-5xl);
    font-weight: var(--font-extrabold);
    line-height: var(--leading-tight);
  }

  h2 {
    font-size: var(--text-4xl);
    font-weight: var(--font-bold);
    line-height: var(--leading-snug);
  }

  h3 {
    font-size: var(--text-3xl);
    font-weight: var(--font-semibold);
  }

  h4 {
    font-size: var(--text-2xl);
    font-weight: var(--font-medium);
  }

  h5 {
    font-size: var(--text-xl);
    font-weight: var(--font-medium);
  }

  h6 {
    font-size: var(--text-lg);
    font-weight: var(--font-medium);
    text-transform: uppercase;
  }

  /* 4) paragraphs and spans */
  p {
    font-size: var(--text-base);
    margin-bottom: var(--space-4);
  }

  span {
    font-size: var(--text-sm);
  }

  a {
    color: var(--color-blue-500);
    transition: color 0.2s;
  }

  a:hover {
    color: var(--color-blue-600);
  }

  /* 6) optional extras */
  ul,
  ol {
    padding-left: var(--space-5);
    margin-bottom: var(--space-4);
    list-style-position: inside;
  }

  blockquote {
    border-left-width: var(--border-width-4);
    padding-left: var(--space-4);
    font-style: italic;
    color: var(--color-gray-600);
  }

  code {
    font-family: var(--font-mono);
    background-color: var(--color-gray-100);
    padding: 0 var(--space-1);
    border-radius: var(--border-radius-md);
  }

  .text-muted {
    color: var(--color-slate-400);
  }

  .text-primary {
    color: var(--primay-color);
  }

  .first-cell {
    font-size: 0.9rem;
  }

  .placeholder-color {
    color: #a8abb2;
  }
}

.el-date-table td.current:not(.disabled) .el-date-table-cell__text {
  background: var(--primay-color) !important;
}

.el-date-table td.today .el-date-table-cell__text {
  color: var(--primay-color) !important;
}

.el-date-table td.available:hover {
  color: var(--primay-color) !important;
}

.el-date-picker__header-label:hover {
  color: var(--primay-color) !important;
}

.el-picker-panel__icon-btn:hover {
  color: var(--primay-color) !important;
}

.el-year-table td.today .el-date-table-cell__text {
  color: var(--primay-color) !important;
}

.el-year-table td .el-date-table-cell__text:hover {
  color: var(--primay-color) !important;
}

.el-select-dropdown__item {
  margin-bottom: 2px;
  color: var(--color-slate-700) !important;
}

.el-select-dropdown__item.is-selected {
  background: var(--p-primary-50) !important;
  font-weight: var(--font-weight-normal) !important;
  color: var(--primay-color) !important;
}

.el-select-dropdown__item.is-hovering {
  background: var(--color-slate-100) !important;
}

.el-month-table td.current:not(.disabled) .el-date-table-cell__text {
  background: var(--primay-color) !important;
}

.el-year-table td.current:not(.disabled) .el-date-table-cell__text {
  background: var(--primay-color) !important;
  color: #fff !important;
}

.el-time-panel__btn.confirm {
  background: var(--primay-color) !important;
  color: #fff !important;
}

.el-year-table td.current:not(.disabled) .el-date-table-cell__text .el-dialog__headerbtn {
  background-color: var(--primay-color) !important;
}

.el-dialog {
  border-radius: 0.4rem !important;
}

.el-table-v2__header-cell {
  color: unset;
}

.el-table-v2__main {
  @apply bg-transparent gap-2;
}

.el-table-v2__header-wrapper {
  @apply bg-white rounded-r-md rounded-l-md border-none shadow-xs;
}

.el-table-v2__row-cell {
  @apply border-b-[1.5px] border-slate-100
}


.el-table-v2 * {
  box-sizing: border-box;
}

.el-table-v2__row:hover {
  background: unset !important;
}

.el-table-v2__row:hover > .el-table-v2__row-cell {
  background: var(--color-slate-100) !important;
  transition: background-color 0.5s ease;
}

.el-table-v2__row {
  @apply bg-transparent border-none pb-2;
}

.el-table-v2__header-cell {
  @apply bg-transparent;
}

.el-table-v2__header-cell-text {
  @apply truncate;
}

.el-table-v2__cell-text {
  @apply truncate;
}

.el-table-v2__cell-text,
.el-table-v2__header-cell-text {
  @apply truncate;
}

.el-table-v2__header-row {
  border: none !important;
}

.highlight-row > div {
  background-color: var(--color-slate-100) !important;
}

.el-table-v2__overlay {
  z-index: 0 !important;
}

.el-table-v2__empty {
  @apply h-1/2;
}

.el-select-dropdown__header {
  border-color: var(--color-slate-100) !important;
}


.el-loading-spinner .path {
  stroke: var(--primay-color) !important;
}
