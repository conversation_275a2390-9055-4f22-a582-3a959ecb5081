import 'element-plus/dist/index.css'
import './assets/main.css'

import { createApp, h, provide } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import { piniaPersistShared } from '@/utils/pinia-persist-shared'

import { DefaultApolloClient } from '@vue/apollo-composable'
import apolloClient from '@/services/apollo'
import { ElLoading } from 'element-plus'
import PrimeVue from 'primevue/config'
import Aura from '@primeuix/themes/aura'
import Button from 'primevue/button'
import FocusTrap from 'primevue/focustrap'
import Ripple from 'primevue/ripple'
import ToastService from 'primevue/toastservice'
import Tooltip from 'primevue/tooltip'

const app = createApp({
  setup() {
    provide(DefaultApolloClient, apolloClient)
  },
  render: () => h(App),
})

const pinia = createPinia()

pinia.use(piniaPersistShared)

app.use(pinia)
app.use(PrimeVue, {
  theme: {
    size: 'small',
    preset: Aura,
    options: {
      darkModeSelector: '-',
    },
  },
  ripple: true,
})

app.use(ElLoading)
app.use(router)
app.use(ToastService)

app.directive('focustrap', FocusTrap)
app.directive('ripple', Ripple)
app.directive('tooltip', Tooltip)

app.component('MainButton', Button)

router.isReady().then(() => {
  app.mount('#app')
})
