import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth.store'
import { useModelStore } from '@/stores/model.store'
import { useViewSettingsStore } from '@/stores/viewSettings.store.ts'
import { notification } from '@/utils/notifications.ts'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'Welcome',
      component: () => import('@/views/WelcomeView.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/LoginView.vue'),
      meta: { requiresGuest: true },
    },
    {
      path: '/client',
      name: 'Clients',
      component: () => import('@/views/ClientView.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/group',
      name: 'Groups',
      component: () => import('@/views/GroupView.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/patients',
      name: 'Patients',
      component: () => import('@/views/PatientsView.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/test-schedule-help',
      name: 'Test Schedule Help',
      component: () => import('@/views/PhoneAuthDocumentationView.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/client-sites',
      name: 'Client Sites',
      component: () => import('@/views/SiteView.vue'),
      meta: { requiresAuth: true },
    },
  ],
})

router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  useModelStore()
  useViewSettingsStore()
  const isAuthenticated = authStore.isAuthenticated
  document.title = to.name?.toString() || ''

  if (to.meta.requiresAuth && !isAuthenticated) {
    return next('/login')
  }

  if (isAuthenticated && !authStore.tokenValidated && to.meta.requiresAuth) {
    console.log('Token validation in progress...')
    try {
      const isValid = await authStore.validateToken()
      if (!isValid) {
        return next('/login')
      }
    } catch (error) {
      console.error('Token validation error:', error)
      notification('error', error)

      return next('/login')
    }
  }

  next()
})

export default router
