<script lang="ts" setup>
import { type Component, computed, useTemplateRef } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth.store.ts'
import Menubar from 'primevue/menubar'
import Menu from 'primevue/menu'
import type { MenuItem } from 'primevue/menuitem'
import { BuildingOffice2Icon, MapPinIcon, UserGroupIcon, UserIcon, PhoneIcon } from '@heroicons/vue/24/outline'

const avatarMenu = useTemplateRef('avatarMenu')
const router = useRouter()
const authStore = useAuthStore()

function logout() {
  authStore.logout()
  router.push('/login')
}

const avatarMenuItems = [
  {
    label: 'Logout',
    icon: 'pi pi-sign-out',
    command: logout,
  },
]

const routes: { route: string; icon: Component; path: string }[] = [
  { route: 'Clients', icon: BuildingOffice2Icon, path: '/client' },
  { route: 'Groups', icon: UserGroupIcon, path: '/group' },
  { route: 'Patients', icon: UserIcon, path: '/patients' },
  { route: 'Client Sites', icon: MapPinIcon, path: '/client-sites' },
  { route: 'Test Schedule Help', icon: PhoneIcon, path: '/test-schedule-help' },
]

const items = computed<MenuItem[]>(() => {
  const menuItems: MenuItem[] = []

  if (authStore.isAuthenticated) {
    routes.forEach((r) => {
      menuItems.push({
        route: r.route,
        icon: r.icon as any,
      })
    })
  }

  return menuItems
})

const toggleAvatarMenu = (event: Event) => {
  avatarMenu.value?.toggle(event)
}

const avatarUrl = computed(() => {
  const seed = authStore.getUser?.username || authStore.getUser?.email || 'guest'
  const encodedSeed = encodeURIComponent(seed)
  return `https://api.dicebear.com/9.x/initials/svg?seed=${encodedSeed}`
})
</script>

<template>
  <div>
    <Menubar
      :model="items"
      :pt="{
        itemLabel: 'text-base',
        itemIcon: 'text-base',
        root: '!rounded-none !border-none !shadow-none',
      }"
    >
      <template #start>
        <router-link to="/">
          <div class="flex items-center">
            <img alt="logo" class="object-contain h-16 select-none" src="@/assets/image.png" />
          </div>
        </router-link>
      </template>

      <template #item="{ item, props }">
        <router-link v-ripple :to="{ name: item.route }" v-bind="props.action">
          <div
            :class="{
              'font-medium text-primary': router.currentRoute.value.name?.toString() == item.route,
              'text-slate-700': router.currentRoute.value.name !== item.route,
            }"
            class="text-base flex items-center gap-1.5"
          >
            <component :is="item.icon" v-if="typeof item.icon !== 'string'" class="w-5 h-5" />
            {{ item.route }}
          </div>
        </router-link>
      </template>

      <template #end>
        <div class="flex items-center gap-2 pr-2.5">
          <template v-if="authStore.isAuthenticated">
            <div class="flex flex-col items-end mr-2">
              <span class="hidden md:inline">
                {{ authStore.getUser?.username }}
                <span title="Role">({{ authStore.getUser?.role?.name }})</span>
              </span>
              <small class="hidden md:inline text-slate-500">
                {{ authStore.getUser?.email }}
              </small>
            </div>

            <img :src="avatarUrl" alt="User avatar" aria-controls="avatar_menu" aria-haspopup="true" class="w-10 h-10 rounded-full cursor-pointer" @click="toggleAvatarMenu" />
            <Menu id="avatar_menu" ref="avatarMenu" :model="avatarMenuItems" :popup="true" />
          </template>
        </div>
      </template>
    </Menubar>
  </div>
</template>
