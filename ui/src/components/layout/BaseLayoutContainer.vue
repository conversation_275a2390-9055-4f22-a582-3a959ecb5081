<script lang="ts" setup>
import { computed, type Slots, useSlots } from 'vue'

defineProps<{
  removePadding?: boolean
  isManager?: boolean
}>()

const slots: Slots = useSlots()

const hasContentTopSlot = computed(() => !!slots['content-top'])
const hasSearchBarSlot = computed(() => !!slots['search-bar'] || !!slots['switch'] || !!slots['button-top'])
const hasSearchOrSwitchSlot = computed(() => !!slots['search-bar'] || !!slots['switch'])
const hasContentSlot = computed(() => !!slots['content'])
</script>

<template>
  <div :class="{ 'overflow-hidden': !isManager }" class="h-full flex justify-center pt-6 min-h-[90.5dvh]">
    <div
      :class="{
        'px-10': !removePadding,
        'max-h-full': !isManager,
      }"
      class="flex flex-col w-full px-20"
    >
      <!-- Top content slot -->
      <div v-if="hasContentTopSlot" class="content-top grid items-end">
        <slot name="content-top" />
      </div>

      <!-- Search bar and button top section -->
      <div v-if="hasSearchBarSlot" class="bg-white rounded-md p-4 mt-4 flex justify-between shadow-xs">
        <div v-if="hasSearchOrSwitchSlot" class="relative flex gap-2 items-center flex-1">
          <slot name="search-bar" />
          <slot name="switch" />
        </div>
        <div />
        <slot name="button-top" />
      </div>

      <!-- Content slot -->
      <div v-if="hasContentSlot" :class="{ 'min-h-0 max-h-full h-full flex flex-col': !isManager }" class="content mt-3 relative">
        <slot name="content" />
      </div>
    </div>

    <!-- Form Dialog -->
    <slot name="form" />
  </div>
</template>
