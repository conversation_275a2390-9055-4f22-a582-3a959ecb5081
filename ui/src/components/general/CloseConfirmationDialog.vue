<script lang="ts" setup>
import { ElButton, ElDialog } from 'element-plus'
import { ExclamationTriangleIcon } from '@heroicons/vue/24/solid'
import { computed, ref } from 'vue' // Add watch here
import { useEventListener } from '@vueuse/core'

interface Props {
  modelValue: boolean
  text?: string
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  text: undefined,
  class: undefined,
})

const emits = defineEmits<Emits>()

const containerRef = ref<HTMLElement | null>(null)

interface Emits {
  (event: 'update:modelValue', value: boolean): void

  (event: 'confirm'): void
}

const modalText = ref<string>(props.text ?? 'Are you sure you want to close?')
const confirmButtonRef = ref<InstanceType<typeof ElButton> | null>(null)

function onVisibleChange(value: boolean) {
  emits('update:modelValue', value)
}

function onConfirm() {
  onVisibleChange(false)
  emits('confirm')
}

useEventListener(document, 'keydown', (e) => {
  if (e.key !== 'Enter') return
  e.stopImmediatePropagation()
  if (anyButtonFocus.value) return
  onConfirm()
})

const anyButtonFocus = computed(() => {
  const buttons = containerRef.value?.querySelectorAll('button')
  if (!buttons) return false
  return Array.from(buttons).some((button) => button === document.activeElement)
})
</script>

<template>
  <ElDialog
    :append-to-body="true"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :model-value="props.modelValue"
    :show-close="false"
    :trap-focus="true"
    align-center
    width="auto"
    @update:model-value="onVisibleChange($event)"
  >
    <div ref="containerRef" :class="props.class" class="w-sm">
      <div class="flex justify-center">
        <ExclamationTriangleIcon class="h-14 w-14 text-red-300" />
      </div>
      <p class="py-8 pt-4 text-center text-sm">
        {{ modalText }}
      </p>
      <div class="flex justify-end gap-2">
        <MainButton severity="secondary" size="small" variant="outlined" @click="onVisibleChange(false)"> Cancel </MainButton>
        <MainButton ref="confirmButtonRef" size="small" @click="onConfirm"> Confirm</MainButton>
      </div>
    </div>
  </ElDialog>
</template>
