<script lang="ts" setup>
import { ref } from 'vue'
import { FloatLabel, IconField, InputIcon, InputText } from 'primevue'
import { debounce } from 'lodash-es'
import { DateFieldTypes, FieldRelationType, NumericFieldTypes } from '@/utils/constants/fieldTypes.ts'
import { useModelStore } from '@/stores/model.store.ts'
import { HISTORY_FIELDS } from '@/utils/constants/grid.ts'
import type { ModelName } from '@/components/general/form/types.ts'

interface Props {
  modelName: ModelName
  excludeFields?: string[]
  debounceTime?: number
  placeholder?: string
  label?: string
  size?: 'small' | 'medium' | 'large'
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  excludeFields: () => [...(HISTORY_FIELDS as string[])],
  debounceTime: 500,
  placeholder: '',
  label: 'Search',
  size: 'small',
  loading: false,
})

const emit = defineEmits<{
  (e: 'filtersChange', filters: any): void
}>()

const searchValue = ref('')
const modelStore = useModelStore()

const debounceSearch = debounce(async (value: string | undefined) => {
  value = value?.trim()

  if (!value) {
    emit('filtersChange', {})
    return
  }
  const fields = modelStore.getModelFields(props.modelName)
  const allScalarFields = fields.filter(
    (field) =>
      field.relationType === FieldRelationType.Scalar &&
      !props.excludeFields.includes(field.name) &&
      !NumericFieldTypes.includes(field.type) &&
      !DateFieldTypes.includes(field.type),
  )

  const orFilters = allScalarFields.map((field) => {
    return {
      [field.name]: {
        containsi: searchValue.value,
      },
    }
  })

  emit('filtersChange', {
    or: orFilters,
  })
}, props.debounceTime)

function resetSearch() {
  searchValue.value = ''
  emit('filtersChange', {})
}

defineExpose({
  resetSearch,
})
</script>

<template>
  <FloatLabel variant="on">
    <IconField>
      <InputIcon class="pi pi-search" />
      <InputText :id="'search_' + modelName" v-model="searchValue" :placeholder="placeholder" :size="size" autocomplete="off" @value-change="debounceSearch($event)" />
      <InputIcon
        v-show="loading"
        :class="{
          'pi-spin': loading,
        }"
        class="pi pi-spinner"
      />
    </IconField>
    <label :for="'search_' + modelName">{{ label }}</label>
  </FloatLabel>
</template>
