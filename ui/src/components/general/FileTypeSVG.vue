<script lang="ts" setup>
import { computed } from 'vue'

const props = defineProps({
  label: {
    type: String,
    default: '',
  },
})

const svgTitle = computed(() => props.label.slice(0, 4).toUpperCase())
</script>

<template>
  <svg id="Layer_1" viewBox="0 0 76.16 87.32" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
      <radialGradient id="radial-gradient" cx="-.13" cy="83.4" fx="-.13" fy="83.4" gradientTransform="translate(19.36 2842.16) scale(20.57 -33.3)" gradientUnits="userSpaceOnUse" r=".33">
        <stop offset="0" stop-color="#205b24" />
        <stop offset="1" stop-color="#3c782c" />
      </radialGradient>
    </defs>
    <g id="Group_18460">
      <g id="Layer_2">
        <g id="Layer_1-2">
          <path id="Path_8178" class="cls-4" d="m64.35,6.55v74.21c0,3.62-2.93,6.55-6.55,6.55H9.94c-3.62,0-6.55-2.93-6.55-6.55h0V21.22L24.63,0h33.16c3.61,0,6.55,2.91,6.55,6.53,0,0,0,.02,0,.03Z" />
          <path id="Path_8179" class="cls-6" d="m3.41,21.22L24.63,0v14.72c0,3.61-2.92,6.53-6.53,6.53l-14.69-.02Z" />
          <path id="Path_8180" class="cls-6" d="m64.35,56.6v22.25H23.61l-13.74-3.63H3.41v-14.96h6.46v-.02l13.74-3.63h40.74Z" />
          <path id="Path_8181" class="cls-3" d="m23.61,53.97h47.29c2.9,0,5.26,2.36,5.26,5.26h0v11.72c0,2.9-2.36,5.26-5.26,5.26H23.61v-22.24Z" />
          <path id="Path_8182" class="cls-5" d="m23.61,76.21l-13.74-3.66v-14.96l13.74-3.63v22.25Z" />
          <rect id="Rectangle_11572" class="cls-3" height="14.96" width="9.89" y="57.62" />
          <path id="Path_8183" class="cls-3" d="m0,57.62l3.41-2.02v2.02H0Z" />
          <path id="Path_8184" class="cls-3" d="m0,72.56l3.41,2.05v-2.05H0Z" />
        </g>
      </g>
      <g id="PNG" class="cls-1">
        <text class="cls-2" transform="translate(32.59 70.29)">
          <tspan x="0" y="0">{{ svgTitle }}</tspan>
        </text>
      </g>
    </g>
  </svg>
</template>

<style lang="scss" scoped>
.cls-1,
.cls-2 {
  isolation: isolate;
}

.cls-3 {
  fill: #3e7d2f;
}

.cls-3,
.cls-4,
.cls-5,
.cls-6 {
  stroke-width: 0px;
}

.cls-4 {
  fill: #f1f2f2;
}

.cls-5 {
  fill: url(#radial-gradient);
}

.cls-6 {
  fill: #d0d3d2;
}

.cls-2 {
  fill: #fff;
  font-family: Arial-BoldMT, Arial;
  font-size: 15.37px;
  font-weight: 700;
}
</style>
