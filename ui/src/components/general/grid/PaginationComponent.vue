<script lang="ts" setup>
import { onBeforeMount, type Ref, ref, watch } from 'vue'
import { cloneDeep, isEqual, isNumber } from 'lodash-es'
import { useModelStore } from '@/stores/model.store'
import type { JSONObject, Model } from '@/utils/types'

// PrimeVue
import Paginator, { type PageState, type PaginatorPassThroughOptions } from 'primevue/paginator'

// ElementPlus
// HeroIcons
// GraphQL
import { useLazyQuery, useMutation } from '@vue/apollo-composable'
import type { FetchResult } from '@apollo/client'
import type { Mutation, MutationCreateViewSettingArgs, MutationUpdateViewSettingArgs } from '@/gql/graphql'
import { notification } from '@/utils/notifications'
import { GetMany, type ReturnFieldProp } from '@/utils/queryMethods'

// ViewSettings
import { useViewSettingsStore } from '@/stores/viewSettings.store.ts'
import type { DocumentNode } from 'graphql/language'
import { storeToRefs } from 'pinia'
import gql from 'graphql-tag'
import type { ModelName } from '@/components/general/form/types.ts'
import type { PassThrough } from '@primevue/core'
import { CREATE_VIEW_SETTINGS, UPDATE_VIEW_SETTINGS } from '@/utils/mutations/viewSettings.ts'

const TEMPLATE_FETCH = gql`
  query template {
    __typename
  }
`

// Props
interface Props {
  modelName: ModelName
  settingsId: string
  fields?: ReturnFieldProp
  filters?: any
  sort?: string
  rowsPerPageOptions?: number[]
  template?: string
  filtered?: boolean // Indicates the data is being filtered from the search bar
  favourite?: boolean // If true will check for favourites in view settings and sort the data accordingly
  noQuery?: boolean // If true, will not run the query
  totalRows?: number // Total records for the paginator (Used only for noQuery prop)
}

const props = withDefaults(defineProps<Props>(), {
  filters: null,
  sort: 'createdAt:desc',
  fields: undefined,
  rowsPerPageOptions: () => [10, 20, 30, 50],
  template: () => 'RowsPerPageDropdown PrevPageLink PageLinks NextPageLink',
  favourite: false,
  fullTextSearchOptions: null,
  noQuery: false,
  totalRows: 0,
})

// Emits
interface Emits {
  (event: 'pageChange', data: any, pagination: PageState): void

  (event: 'loading', loading: boolean): void

  (event: 'error', error: Error): void

  (event: 'onTotalRecordsChange', totalRecords: number): void
}

const emits = defineEmits<Emits>()

const { getModel } = useModelStore()

const paginatorStyle: PassThrough<PaginatorPassThroughOptions> = {
  root: {
    class: ['!bg-transparent !text-base !p-0'],
  },
  previousPageButton: () => ({
    class: ['min-w-6 size-9'],
  }),
  pageButton: () => ({
    class: ['min-w-6 size-9'],
  }),
  nextPageButton: () => ({
    class: ['min-w-6 size-9'],
  }),
}

const { load, fetchMore } = useLazyQuery(TEMPLATE_FETCH, {}, { fetchPolicy: 'network-only' })

const { viewSettings } = storeToRefs(useViewSettingsStore())
const { addViewSettings, updateViewSettings } = useViewSettingsStore()
const viewSettingsKey = `${props.settingsId}_pagination`
const { mutate: createViewSetting } = useMutation(CREATE_VIEW_SETTINGS)
const { mutate: updateViewSetting } = useMutation(UPDATE_VIEW_SETTINGS)

async function saveViewSettings(rowsPerPage: number) {
  try {
    if (viewSettings.value?.[viewSettingsKey]) {
      const result: FetchResult<Mutation> | null = await updateVS(rowsPerPage)
      const rowPerPageResult = result?.data?.updateViewSetting?.settings?.[viewSettingsKey]
      if (rowPerPageResult) {
        updateViewSettings(viewSettingsKey, rowPerPageResult as JSONObject)
      }
    } else {
      const result: FetchResult<Mutation> | null = await createVS(rowsPerPage)
      console.log(result)
      const rowPerPageResult = result?.data?.createViewSetting?.settings?.[viewSettingsKey]
      if (rowPerPageResult) {
        addViewSettings(viewSettingsKey, rowPerPageResult as JSONObject)
      }
    }
  } catch (e) {
    console.log(e)
    notification('error', e as string)
  }
}

async function createVS(rowsPerPage: number) {
  try {
    const variables: MutationCreateViewSettingArgs = {
      data: {
        name: viewSettingsKey,
        settings: {
          rowsPerPage,
        },
      },
    }
    return await createViewSetting(variables)
  } catch (e: any) {
    throw new Error(e)
  }
}

async function updateVS(rowsPerPage: number) {
  try {
    const variables: MutationUpdateViewSettingArgs = {
      data: {
        name: viewSettingsKey,
        settings: { rowsPerPage },
      },
    }
    return await updateViewSetting(variables)
  } catch (e: any) {
    throw new Error(e)
  }
}

// Data
const query = ref<DocumentNode>(TEMPLATE_FETCH)
const data = ref([])

interface ReturnFields {
  data: Array<string | ReturnFieldsAttributes>
  meta?: [{ pagination: string[] }]
}

interface ReturnFieldsAttributes {
  attributes: Array<string | ReturnFieldsData>
}

interface ReturnFieldsData {
  [key: string]: Array<string | ReturnFields>
}

async function fetchData(model: Model, pagination: PageState): Promise<number> {
  const variables = {
    pagination: {
      start: pagination.page * pagination.rows,
      limit: pagination.rows,
    },
    filters: props.filters,
    sort: props.sort ?? 'createdAt:desc',
  }

  // No favourite functionality enabled
  const result = (await load(query.value, { ...variables }, { fetchPolicy: 'network-only' })) || (await fetchMore({ variables }))
  const operation = `${model.pluralName}_connection`

  data.value = cloneDeep(result?.[operation]?.nodes ?? result?.data?.[operation]?.nodes ?? [])
  return result?.[operation]?.pageInfo?.total ?? result?.data?.[operation]?.pageInfo?.total ?? 0
}

onBeforeMount(() => {
  console.log(viewSettings.value?.[viewSettingsKey])
  pagination.value.rows = Number((viewSettings.value?.[viewSettingsKey] as JSONObject)?.rowsPerPage) || Number(props.rowsPerPageOptions) || 10

  if (!props.noQuery) {
    query.value = GetMany(props.modelName, props.fields)
    changePaginator(pagination.value)
  } else {
    emits('pageChange', [], pagination.value)
  }
})

const pagination: Ref<PageState> = ref({
  page: 0,
  rows: 10,
  first: 0,
  pageCount: 1,
})

const totalRecords = ref(props.totalRows)

async function changePaginator(value: PageState) {
  pagination.value = value

  try {
    const currentViewSetting = viewSettings.value?.[viewSettingsKey] as JSONObject
    if (value.rows !== currentViewSetting?.rowsPerPage) {
      await saveViewSettings(value.rows)
    }

    if (props.noQuery) {
      return
    }

    emits('loading', true)
    data.value = []

    const model = getModel(props.modelName)
    if (!model) {
      emits('error', new Error(`Model ${props.modelName} not found`))
      return
    }

    // No favourites
    const resultCount = await fetchData(model, pagination.value)

    console.log('resultCount', resultCount)

    totalRecords.value = resultCount
    emits('pageChange', data.value.slice(0, pagination.value.rows), value)
  } catch (error) {
    emits('error', error as Error)
  } finally {
    emits('loading', false)
  }
}

watch(totalRecords, () => {
  emits('onTotalRecordsChange', totalRecords.value)
})

// Watchers
watch(
  () => props.filters,
  async () => {
    await refresh()
  },
)

watch(
  () => props.sort,
  async () => {
    await refresh()
  },
)

watch(
  () => pagination.value,
  (current, old) => {
    if (!props.noQuery || isEqual(current, old)) {
      return
    }
    // only emit for noQuery, otherwise it will emit twice for noQuery false
    emits('pageChange', [], cloneDeep(pagination.value))
  },
)

watch(
  () => props.totalRows,
  () => {
    if (isNumber(props.totalRows)) {
      totalRecords.value = props.totalRows
    }
  },
  { immediate: true, deep: true },
)

// Keeping the current pagination state
async function refresh() {
  if (props.noQuery) {
    return
  }

  await changePaginator(pagination.value)
}

defineExpose({
  refresh,
})
</script>

<template>
  <Paginator
    :first="pagination.page * pagination.rows"
    :pt="paginatorStyle"
    :rows="pagination?.rows"
    :rows-per-page-options="rowsPerPageOptions"
    :total-records="totalRecords"
    always-show
    current-page-report-template="Showing {first} to {last} of {totalRecords}"
    template="FirstPageLink PrevPageLink CurrentPageReport NextPageLink LastPageLink RowsPerPageDropdown "
    @page="changePaginator"
  ></Paginator>
</template>

<style lang="scss" scoped></style>
