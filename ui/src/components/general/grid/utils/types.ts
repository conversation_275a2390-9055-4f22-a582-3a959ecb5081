import type { Maybe, <PERSON>ala<PERSON> } from '@/gql/graphql.ts'
import type { CheckboxValueType, RowExpandParams } from 'element-plus'
import type { Ali<PERSON>ment, CellRenderer, ClassNameGetter, FixedDirection, HeaderCellRenderer, HeaderClassGetter, KeyType } from 'element-plus/es/components/table-v2/src/types.mjs'
import type { CSSProperties } from 'vue'

export interface ColumnType<T> {
  align?: Alignment
  class?: string | ClassNameGetter<T>
  key: KeyType
  dataKey?: KeyType
  fixed?: true | FixedDirection
  flexGrow?: CSSProperties['flexGrow']
  flexShrink?: CSSProperties['flexShrink']
  title?: string
  hidden?: boolean
  headerClass?: HeaderClassGetter<T> | string
  maxWidth?: number
  minWidth?: number
  style?: CSSProperties
  sortable?: boolean
  width?: number
  field?: string
  /**
   * Renderers
   */
  cellRenderer?: CellRenderer<T>
  headerCellRenderer?: HeaderCellRenderer<T>
}

export interface SelectionCellProps {
  value: boolean
  intermediate?: boolean
  onChange: (value: CheckboxValueType) => void
}

export interface RowData {
  id?: Maybe<Scalars['ID']['output']>
  parentId?: string | null
  children?: RowData[]

  [key: string]: any
}

export type GridStyle = 'default' | 'border'

export interface Emits {
  (event: 'update:data', value: any[]): void

  (event: 'selectedRows', value: string[]): void

  (event: 'rowExpand', params: RowExpandParams): void

  (event: 'rowsRendered', params: RowsRenderedParams): void

  (event: 'scroll', params: ScrollParams): void

  (event: 'endReached'): void

  (event: 'expandedRowsChange', value: KeyType[]): void
}

export interface RowsRenderedParams {
  rowCacheStart: number
  rowCacheEnd: number
  rowVisibleStart: number
  rowVisibleEnd: number
}

export interface ScrollParams {
  xAxisScrollDir: 'forward' | 'backward'
  scrollLeft: number
  yAxisScrollDir: 'forward' | 'backward'
  scrollTop: number
}
