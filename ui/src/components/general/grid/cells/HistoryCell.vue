<script generic="T extends BaseHistoryData" lang="ts" setup>
import type { BaseHistoryData } from '@/utils/types.ts'

const props = defineProps<{
  rowData: T
}>()

const formatDate = (date: Date | string | null | undefined) =>
  date
    ? new Date(date).toLocaleString(undefined, {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      })
    : 'N/A'
</script>

<template>
  <div class="flex flex-col gap-1">
    <div class="flex flex-col w-full">
      <div v-tooltip.bottom="'Created At ' + formatDate(rowData.createdAt)" class="truncate w-full">
        {{ formatDate(rowData.createdAt) }}
      </div>
      <div v-tooltip.bottom="'Created By ' + rowData.CreatedByUser?.username" class="text-xs truncate text-slate-500">
        by
        {{ rowData.CreatedByUser?.username || 'Unknown' }}
      </div>
    </div>
    <div class="flex flex-col w-full">
      <div v-tooltip.bottom="'Created At ' + formatDate(rowData.updatedAt)" class="truncate">
        {{ formatDate(rowData.updatedAt) }}
      </div>
      <div v-tooltip.bottom="'Updated By ' + rowData.UpdatedByUser?.username || 'Unknown'" class="text-xs text-slate-500 truncate">
        by
        {{ rowData.UpdatedByUser?.username || 'Unknown' }}
      </div>
    </div>
  </div>
</template>
