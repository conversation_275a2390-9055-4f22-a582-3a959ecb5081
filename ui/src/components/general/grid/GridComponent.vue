<script generic="T extends RowData" lang="ts" setup>
import { computed, h, onBeforeUnmount, onMounted, ref, watch } from 'vue'
import { cloneDeep, debounce, has } from 'lodash-es'
import type { CheckboxValueType, Column, RowEventHandlers, RowExpandParams } from 'element-plus'
import { TableV2 } from 'element-plus'
import { twMerge } from 'tailwind-merge'
import type { Alignment as ScrollStrategy } from 'element-plus/es/components/virtual-list'
import type { ColumnType, Emits, GridStyle, RowData, RowsRenderedParams, ScrollParams } from '@/components/general/grid/utils/types.ts'
import { SelectionCell } from '@/components/general/grid/utils/cell-renderers.ts'
import { ArchiveBoxIcon, XMarkIcon } from '@heroicons/vue/24/outline'
import type { KeyType } from 'element-plus/lib/components/table-v2/src/types.js'
import type { Maybe, Scalars } from '@/gql/graphql.ts'
import Skeleton from 'primevue/skeleton'

const componentsHeightDimensions = {
  paginator: 45,
  actionBar: 55,
}

interface Props {
  loading?: boolean
  columns?: ColumnType<T>[]
  data: T[]
  gridStyle?: GridStyle
  selectedRowId?: Maybe<Scalars['ID']['output']> // to show the row highlighted
  showRowSelect?: boolean // show row select checkbox
  showRowSelectAll?: boolean // show row select to children rows too
  rowEventHandlers?: RowEventHandlers // row event handlers
  expandColumnKey?: string // The column key indicates which row is expandable
  expandedRowKeys?: string[] // An array of keys for expanded rows
  defaultExpandedRowKeys?: string[] // An array of keys for default expanded rows, NON REACTIVE
  estimatedRowHeight?: number // estimated height for the row
  markParent?: boolean // mark parent with a different color
  rowClass?: (data: T) => string // row class
  hideAnimation?: boolean // remove rows animation
  cachedRows?: number // cached rows
  width?: number // width of the grid
}

const emits = defineEmits<Emits>()

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  showRowSelect: false,
  showRowSelectAll: true,
  expandColumnKey: undefined,
  selectedRowId: undefined,
  gridStyle: 'default',
  estimatedRowHeight: 80,
  columns: () => [],
  rowEventHandlers: () => ({}),
  defaultExpandedRowKeys: () => [],
  expandedRowKeys: () => [],
  markParent: false,
  cachedRows: 20,
  rowClass: () => '',
  width: undefined,
})

const slots = defineSlots<{
  [key: `cell-${string}`]: (props: { rowData: T; rowIndex: number; columnIndex: number }) => any
  'expanded-row'?: (props: { rowData: T }) => any
  pagination?: () => any
  export?: () => any
  footer?: () => any
  actionsButtons?: () => any
}>()

const containerRef = ref<HTMLElement | null>(null)

const localExpandedRowKeys = ref<string[]>(cloneDeep(props.expandedRowKeys) || [])
const selectedRowsIds = ref<Set<string>>(new Set())

const tableDimensions = ref({ width: 0, height: 0 })

const isTableResizing = ref(true)
const tableRef = ref()

let resizeObserver: ResizeObserver

onMounted(() => {
  resizeObserver = new ResizeObserver((entries) => {
    const entry = entries[0]
    if (entry.target === containerRef.value) {
      resizeTable(entry.target.clientHeight, entry.target.clientWidth)
    }
  })

  if (containerRef.value) {
    resizeObserver.observe(containerRef.value, { box: 'border-box' })
  }
})

onBeforeUnmount(() => {
  resizeObserver.disconnect()
})

watch(
  () => selectedRowsIds.value.size,
  () => {
    emits('selectedRows', Array.from(selectedRowsIds.value))
  },
)

const height = computed(() => {
  let baseHeight = tableDimensions.value.height - componentsHeightDimensions.paginator
  const offSet = 35
  if (selectedRowsIds.value.size) {
    baseHeight -= componentsHeightDimensions.actionBar
  }

  if (!has(slots, 'pagination') && !has(slots, 'export')) {
    baseHeight += offSet
  }

  return baseHeight
})

const tableColumns = computed(() => {
  const totalTableWidth = tableDimensions.value.width
  const selectionCellKey = 'selection'
  const selectCellWidth = props.expandColumnKey === selectionCellKey ? 50 : 30

  const selectionColumn: ColumnType<any> = {
    key: selectionCellKey,
    width: selectCellWidth,
    cellRenderer: ({ rowData }: { rowData: RowData }) => {
      if (!props.showRowSelectAll && rowData.parentId) {
        return h('div', { class: 'hidden' })
      }

      function onChange(value: CheckboxValueType) {
        if (value) selectedRowsIds.value.add(rowData.documentId)
        else selectedRowsIds.value.delete(rowData.documentId)
      }

      return SelectionCell({ value: selectedRowsIds.value.has(rowData.documentId), onChange })
    },
    headerCellRenderer: () => {
      function onChange(value: CheckboxValueType) {
        for (const row of props.data) {
          if (value) selectedRowsIds.value.add(row.documentId)
          else selectedRowsIds.value.delete(row.documentId)
        }
      }

      const allSelected = props.data.every((r) => selectedRowsIds.value.has(r.documentId))
      const someSelected = props.data.some((r) => selectedRowsIds.value.has(r.documentId))
      return SelectionCell({
        value: allSelected,
        intermediate: someSelected && !allSelected,
        onChange,
      })
    },
  }

  const rawColumns = props.showRowSelect ? [selectionColumn, ...props.columns] : [...props.columns]

  // 2) Figure out fixed vs. flexible widths
  let totalFixedWidth = 10 + (props.showRowSelect ? selectCellWidth : 0)
  let flexibleCount = 0

  for (const col of rawColumns) {
    if (col.width != null) totalFixedWidth += col.width
    else flexibleCount++
  }

  const remainingWidth = totalTableWidth - totalFixedWidth
  const flexWidth = flexibleCount > 0 ? remainingWidth / flexibleCount : 0

  // 3) Map into final ColumnType[], adding classes and widths
  const headerBase = '!text-[1.0625rem] uppercase !text-slate-700 !font-medium truncate'
  const rowBase = 'bg-white'
  const columns: ColumnType<any>[] = rawColumns.map((col, i) => {
    const isFirst = i === 0
    const isLast = i === rawColumns.length - 1
    const width = col.width != null ? col.width : flexWidth

    const borderCls = twMerge(
      props.gridStyle === 'border' ? 'border-slate-100 border-y' : '',
      isFirst && props.gridStyle === 'border' ? 'border-l' : '',
      isLast && props.gridStyle === 'border' ? 'border-r' : '',
    )
    const roundCls = isFirst ? 'rounded-l-lg' : isLast ? 'rounded-r-lg' : ''

    return {
      ...col,
      width,
      minWidth: 100,
      headerClass: twMerge((col as any).headerClass || '', roundCls, headerBase, borderCls),
      class: twMerge((col as any).class || '', roundCls, rowBase, borderCls),
    }
  })

  return columns
})
const debounceResize = debounce(() => {
  isTableResizing.value = false
}, 100)

function resizeTable(height: number, width: number) {
  if (tableDimensions.value.height === height && tableDimensions.value.width === width) {
    return
  }

  if (Math.abs(tableDimensions.value.width - width) <= 10 && tableDimensions.value.height === height) {
    return
  }

  isTableResizing.value = true

  tableDimensions.value = {
    width: width,
    height: height,
  }
  debounceResize()
}

function clearSelectedRows() {
  selectedRowsIds.value.clear()
}

function onRowExpanded(params: RowExpandParams) {
  emits('rowExpand', params)
}

function onExpandedRowsChanged(expandedRowKeys: KeyType[]) {
  emits('expandedRowsChange', expandedRowKeys)
}

function onEndReached() {
  emits('endReached')
}

function onScroll(params: ScrollParams) {
  emits('scroll', params)
}

function onRowsRendered(params: RowsRenderedParams) {
  emits('rowsRendered', params)
}

function rowClass({ rowData }: { rowData: T }) {
  let baseClass = ''

  if (rowData?.documentId === props.selectedRowId) {
    baseClass += ' highlight-row'
  }

  if (props.rowClass && typeof props.rowClass === 'function') {
    baseClass += ` ${props.rowClass(rowData)}`
  }

  return baseClass
}

function isExpandedDetailRow(id: string | undefined) {
  if (!id) return false
  return props.expandedRowKeys?.includes(id)
}

function scrollToRow(rowNumber: number, strategy: ScrollStrategy = 'auto') {
  if (tableRef.value) {
    tableRef.value.scrollToRow(rowNumber, strategy)
  }
}

defineExpose({
  clearSelectedRows,
  scrollToRow,
})
</script>

<template>
  <div ref="containerRef" class="flex-1 min-h-0 max-h-full h-full">
    <div v-if="selectedRowsIds.size > 0" class="bg-white rounded-md mb-2 p-2">
      <div class="flex items-center gap-2 flex-wrap">
        <MainButton size="small" @click="clearSelectedRows">
          <XMarkIcon class="size-4" />
          {{ selectedRowsIds.size }}
          <span class="lowercase"> Selected </span>
        </MainButton>
        <slot name="actionsButtons" />
      </div>
    </div>
    <TableV2
      v-if="!isTableResizing"
      ref="tableRef"
      v-model:expanded-row-keys="localExpandedRowKeys"
      :cache="props.cachedRows"
      :columns="tableColumns as Column[]"
      :data="props.data"
      :default-expanded-row-keys="props.defaultExpandedRowKeys"
      :estimated-row-height="estimatedRowHeight"
      :expand-column-key="props.expandColumnKey"
      :header-class="props.gridStyle === 'border' ? '!border-none' : ''"
      :header-height="45"
      :height="height"
      :indent-size="20"
      :row-class="rowClass"
      :row-event-handlers="rowEventHandlers"
      :width="tableDimensions.width"
      fixed
      row-key="documentId"
      scrollbar-always-on
      @scoll="onScroll"
      @update:expanded-row-keys="onExpandedRowsChanged"
      @end-reached="onEndReached"
      @expanded-rows-change="onExpandedRowsChanged"
      @row-expand="onRowExpanded"
      @rows-rendered="onRowsRendered"
    >
      <template #cell="{ columnIndex, rowIndex, rowData }: { columnIndex: number; rowIndex: number; rowData: T }">
        <div :class="{ 'text-sm': columnIndex > 1, 'text-[15px]': columnIndex === 1 }" class="w-full h-full py-2 truncate">
          <template v-for="(column, index) in tableColumns" :key="column.key">
            <slot v-if="index === columnIndex" :column-index="columnIndex" :name="`cell-${column.key as string}`" :row-data="rowData" :row-index="rowIndex" />
          </template>
        </div>
      </template>
      <template v-if="!!slots['expanded-row']" #row="{ cells, rowData }: { cells: any[]; rowData: T }">
        <template v-if="isExpandedDetailRow(rowData.documentId)">
          <div class="w-full mx-5">
            <slot :row-data="rowData" name="expanded-row" />
          </div>
        </template>
        <template v-else>
          <component :is="cell" v-for="(cell, index) in cells" :key="index" />
        </template>
      </template>

      <template #empty>
        <div v-if="!props.loading && props.data.length <= 0" class="flex items-center gap-x-2 w-full justify-center self-center h-full text-sm pt-10">
          <ArchiveBoxIcon class="size-5 text-primary" />
          <span class="text-slate-500">No data available</span>
        </div>
        <div v-else />
      </template>

      <template v-if="props.loading" #overlay>
        <Skeleton class="grid grid-cols-3 rounded-md mb-[.375rem]" height="3.7rem" />
        <div v-for="index in 6" :key="index" class="col-span-2">
          <Skeleton class="grid grid-cols-3 rounded-md mb-[.375rem]" height="5.7rem" />
        </div>
      </template>
      <template v-if="!!slots['footer']" #footer />
    </TableV2>

    <div v-if="slots.pagination || slots.export" class="flex items-center justify-between">
      <div v-if="slots.export">
        <slot name="export" />
      </div>
      <div class="flex justify-center flex-grow overflow-hidden pt-1">
        <slot name="pagination" />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
