<script lang="ts" setup>
import { onMounted, onUnmounted, ref } from 'vue'
// Element Plus
import { ElDatePicker } from 'element-plus'
import type { DatePickerProps } from '@/components/general/form/types.ts'

const props = defineProps<DatePickerProps>()

interface Emits {
  (event: 'visible-change', visible: boolean): void

  (event: 'update:modelValue', value: any): void

  (event: 'change', value: any): void

  (event: 'onEnterKeyPress'): void
}

const emits = defineEmits<Emits>()

function visibleChange(visible: boolean) {
  isSelectVisible.value = visible
  if (!visible) return
  emits('visible-change', visible)
}

const datePickerRef = ref()
const isSelectVisible = ref(false)

onMounted(() => {
  document.addEventListener('keydown', handleEnterKeydown)
})

// remove event listener on unmount
onUnmounted(() => {
  document.removeEventListener('keydown', handleEnterKeydown)
})

function handleEnterKeydown(event: KeyboardEvent) {
  if (event.key === 'Enter') {
    emits('onEnterKeyPress')
  }
}

const defaultShortcuts = [
  {
    text: 'Today',
    value: new Date(),
  },
  {
    text: 'Yesterday',
    value: () => {
      const date = new Date()
      date.setTime(date.getTime() - 3600 * 1000 * 24)
      return date
    },
  },
  {
    text: 'A week ago',
    value: () => {
      const date = new Date()
      date.setTime(date.getTime() - 3600 * 1000 * 24 * 7)
      return date
    },
  },
]

defineExpose({
  focus: () => {
    datePickerRef?.value?.focus()
  },
})
</script>

<template>
  <ElDatePicker
    ref="datePickerRef"
    :clearable="false"
    :default-time="props.defaultTime ?? undefined"
    :disabled="props.disabled"
    :disabled-date="props.disabledDate"
    :format="format ?? 'YYYY-MM-DD'"
    :model-value="props.modelValue"
    :placeholder="props.placeholder ?? 'Pick a day'"
    :popper-options="props.popperOptions ?? {}"
    :shortcuts="props.shortcuts ?? defaultShortcuts"
    :type="props.type ?? 'date'"
    :value-format="props.valueFormat ?? undefined"
    class="no-border transparent"
    size="default"
    @change="emits('change', $event)"
    @clear="emits('update:modelValue', null)"
    @visible-change="visibleChange"
    @update:model-value="emits('update:modelValue', $event)"
  />
</template>

<style lang="scss" scoped>
.el-date-table td.current:not(.disabled) .el-date-table-cell__text {
  background: var(--primay-color) !important;
}

.el-date-table td.today .el-date-table-cell__text {
  color: red !important;
}
</style>
