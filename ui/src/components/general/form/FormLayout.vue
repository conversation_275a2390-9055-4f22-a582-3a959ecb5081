<script lang="ts" setup>
import { ElDialog } from 'element-plus'

interface Props {
  modelValue: boolean
  showConfirmCloseForm: boolean
  isModal: boolean
  title?: string
  unSavedChanges: boolean
}

const props = defineProps<Props>()
const emits = defineEmits<{
  (e: 'update:modelValue', v: boolean): void
  (e: 'update:showConfirmCloseForm', v: boolean): void
}>()

function hasOverlays() {
  return Array.from(document.querySelectorAll<HTMLElement>('.p-dialog-mask')).length > 0
}

function beforeClose(done: () => void) {
  if (hasOverlays()) {
    return
  }

  if (props.unSavedChanges) {
    emits('update:showConfirmCloseForm', true)
  } else {
    done()
  }
}

function onCloseClick() {
  if (props.unSavedChanges) {
    emits('update:showConfirmCloseForm', true)
  } else {
    emits('update:modelValue', false)
  }
}
</script>

<template>
  <ElDialog
    v-if="isModal"
    :before-close="beforeClose"
    :model-value="modelValue"
    :show-close="false"
    :title="title"
    align-center
    width="auto"
    @update:model-value="emits('update:modelValue', $event)"
  >
    <div class="relative">
      <MainButton aria-label="Cancel" class="!absolute -top-5 right-0" icon="pi pi-times" rounded severity="secondary" size="small" variant="text" @click="onCloseClick" />
      <slot />
    </div>
  </ElDialog>

  <template v-else>
    <slot />
  </template>
</template>
