import type { Address, GenericMorph, Maybe } from '@/gql/graphql'
import type { FieldModel } from '@/utils/types'
import type { FormItemRule } from 'element-plus'
import type { IconName } from '../AsyncIcon.vue'
import type { ReturnFieldProp } from '@/utils/queryMethods.ts'
import type { DatePickerProps as ElDatePickerProps } from 'element-plus/es/components/date-picker/src/props/date-picker'

type RequireTypename<T> = T extends { __typename?: infer U }
  ? U extends string
    ? {
        __typename: U
      } & T
    : never
  : T

type NonNullableGenericMorph = RequireTypename<GenericMorph>

export type ModelName = NonNullableGenericMorph['__typename'] // all models name

export type Rules = Record<string, Array<FormItemRule>>

// called when the update/create was successful to refresh the form data states
export type SuccessCallback = () => void

export interface Emits {
  (event: 'close'): void

  (event: 'onSubmit', data: any, successCallBack: SuccessCallback): void

  (event: 'onClear'): void

  (event: 'onFieldChange', field: any, value: any): void

  (event: 'onRefresh', modelData: any): void
}

interface RemoteSelectSections {
  name: string
  filters: any
}

export interface RemoteSelectProps {
  modelValue: any // The current value of the select input
  modelName: ModelName // The name of the model to query
  labelField?: string // The field to use for the option labels
  labelFunction?: (item: any) => string // A function to generate the option labels
  filters?: Maybe<any> // Filters to apply to the query
  multiple?: boolean // Whether multiple selections are allowed
  returnFields?: ReturnFieldProp // The fields to return from the query
  uniqueByKey?: string // A key to ensure uniqueness of options
  placeholder?: Maybe<string> // Placeholder text for the select input
  clearable?: boolean // Whether the select input is clearable
  valueKey?: string // The key to use for the option values
  disabled?: boolean // Whether the select input is disabled
  fitInputWidth?: boolean // Whether the input should fit the width of its content
  suffixIcon?: string // The icon to display in the input suffix
  sections?: RemoteSelectSections[] // custom sections to display above all section
  showSetFavorites?: boolean // Whether to show the set favorites button
  skipEntityStateFilter?: boolean
  sorter?: string // The field to sort the options by
  placement?: string // placement for select
  optionDisabled?: (option: any) => boolean // Function to determine if an option is disabled
}

export interface AddressProps {
  modelValue?: Address // to populate the form
  customClass?: string
  disabled?: boolean
  disabledMessage?: string
  readOnly?: boolean
  label?: string
}

export interface DatePickerProps {
  modelValue: any
  type?: ElDatePickerProps['type']
  disabled?: boolean
  disabledDate?: (data: Date) => boolean
  placeholder?: Maybe<string>
  format?: string
  valueFormat?: string
  // eslint-disable-next-line @typescript-eslint/no-unsafe-function-type
  shortcuts?: Array<{ text: string; value: Date | Function }>
  popperOptions?: any
  defaultTime?: Date
}

interface RemoteOptions extends Omit<RemoteSelectProps, 'modelName' | 'modelValue' | 'returnAll' | 'filters'> {
  filters?: Record<string, any> | ((formData: any, allModelData: any) => Record<string, any>)
}

type AddressOptions = Omit<AddressProps, 'modelValue' | 'disabled' | 'readOnly' | 'label'>
type DatePickerOptions = Omit<DatePickerProps, 'modelValue' | 'disabled'>

interface FieldConfig {
  rules?: FormItemRule[]
  label?: string
  icon?: IconName
  placeholder?: string
  disabled?: boolean | ((formData: any, allModelData: any) => boolean)
  addressOptions?: AddressOptions
  remoteOptions?: RemoteOptions
  datePickerOptions?: DatePickerOptions
  excludedValues?: string[] // Filter Enum Values
  showRequired?: boolean // it only shows the '*', without validation. MAKE SURE YOU DON'T SEND THE REQUIRED RULES FOR THIS TO WORK!!!
  visibleMethod?: (formData: any, allModelData: any) => boolean
  copyMethod?: (formData: any, allModelData: any) => any
}

export type FieldsConfig<T> = {
  [K in keyof T]?: FieldConfig
}

export type Layouts = 'default' | 'grid' | 'inline' | 'group' | 'group-modal'

export interface BuildFormFieldsParams<T> {
  modelName: ModelName
  fields: string[]
  fieldsConfig?: FieldsConfig<T>
  recordId: string | undefined
}

export interface FormField extends Omit<FieldModel, 'filters' | 'required'>, FieldConfig {}

export interface ExtraSlotField extends Omit<FieldConfig, 'rules' | 'remoteOptions'> {
  name: string
  label?: string
}

export interface GroupConfig {
  label?: string
  class: string
  fields: string[]
  visibleMethod?: (formData: Record<string, any>, allModelData: Record<string, any>) => boolean
}

export interface ProcessedGroup extends Omit<GroupConfig, 'fields'> {
  fields: FormField[]
}

export interface DynamicFormFileStorageInput {
  name: File
  description: string
}

/**
 * Defines the form data type for dynamic forms.
 *
 * @template T - The model name type imported from the GraphQL schema.
 * @example
 * import type { Disclaimer } from '@/gql/graphql.ts';
 * type FormData = DynamicFormData<Disclaimer>;
 */
export type DynamicFormData<T> = (T & { id?: Maybe<string> | undefined }) | undefined
