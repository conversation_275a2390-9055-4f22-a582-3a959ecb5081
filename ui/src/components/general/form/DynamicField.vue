<script lang="ts" setup>
// Used only in the dynamic form component not for other components

// external imports
import { computed, defineAsyncComponent, ref, watch, watchEffect } from 'vue'
import { cloneDeep, isEmpty, isEqual, startCase } from 'lodash-es'
import { ElFormItem, ElInput, ElInputNumber, ElOption, ElPopover, ElSelect } from 'element-plus'

import { notification } from '@/utils/notifications'

// local imports
import { isOutsideClick } from '@/utils/dynamicForm'
import type { FormField, ModelName } from './types'
import { onClickOutside } from '@vueuse/core'
import Checkbox from 'primevue/checkbox'
import { DateFieldTypes, FieldRelationType, FieldType, ManyRelationFieldTypes, NumericFieldTypes, OneRelationFieldTypes, TextFieldTypes } from '@/utils/constants/fieldTypes.ts'

const FileInput = defineAsyncComponent(() => import('@/components/general/form/FileInput.vue'))
const DatePicker = defineAsyncComponent(() => import('@/components/general/form/DatePicker.vue'))
const RemoteSelect = defineAsyncComponent(() => import('@/components/general/form/RemoteSelect.vue'))

const props = defineProps<{
  modelName: ModelName
  field: FormField
  modelValue: any
  disabled: boolean
  formData: Record<string, any>
  allModelData: Record<string, any>
  relationData?: any
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: any): void
  (e: 'onChange', value: any): void
  (e: 'onEnterKeyPress', field: string): void
  (e: 'onEscKeyPress'): void
}>()

const descriptionPopoverVisible = ref(false)

const formItemRef = ref<InstanceType<typeof ElFormItem>>()

const fieldRef = ref()
const textAreaRef = ref()
const buttonRef = ref<HTMLElement>()
const popoverRef = ref()

const isFieldFocused = ref(false)

const initialRelationData = ref(cloneDeep(props.relationData))

watchEffect(() => {
  if (isEmpty(props.modelValue) && !isEmpty(initialRelationData.value) && [...OneRelationFieldTypes, ...ManyRelationFieldTypes].includes(props.field.relationType)) {
    initialRelationData.value = null
  }
})

watch(
  () => props.relationData,
  () => {
    if (!isEqual(props.relationData, initialRelationData.value)) {
      initialRelationData.value = cloneDeep(props.relationData)
    }
  },
  { immediate: false },
)

const currentItemWidth = computed(() => formItemRef.value?.$el?.clientWidth)

function onFieldChange(value: any) {
  emit('update:modelValue', value)
  if ([...OneRelationFieldTypes, ...ManyRelationFieldTypes].includes(props.field.relationType)) {
    return
  }
  emit('onChange', value)
}

function onRemoteChange(data: any) {
  // Many relations
  if (Array.isArray(data)) {
    onFieldChange(data.map((item) => item.documentId))
    emit('onChange', cloneDeep(data))
    return
  }

  // One relation
  if (data?.documentId) {
    initialRelationData.value = data
    onFieldChange(data.documentId)
    emit('onChange', cloneDeep(data))
    return
  }

  onFieldChange(null)
  emit('onChange', null)
}

function getFilters() {
  if (props?.field.name === 'GenderIdentity') {
    return {
      Category: {
        name: {
          eq: 'Gender Identity',
        },
      },
    }
  }
  if (typeof props.field.remoteOptions?.filters === 'function') {
    return props.field.remoteOptions?.filters(props.formData, props.allModelData)
  }

  return props.field.remoteOptions?.filters ?? {}
}

function onPopoverShow() {
  textAreaRef.value?.focus()
}

function closePopover() {
  descriptionPopoverVisible.value = false
}

function onInputFocus(event: FocusEvent) {
  const input = event.target as HTMLInputElement
  if (TextFieldTypes.includes(props.field.type)) {
    input?.setSelectionRange(props.modelValue.length, props.modelValue.length)
  }
  isFieldFocused.value = true
}

onClickOutside(textAreaRef, (event) => {
  if (!isOutsideClick(event)) return
  closePopover()
})

defineExpose({
  closePopover,
  focus: () => {
    if (props.field.type === FieldType.Text) {
      buttonRef?.value?.focus()
      return
    }

    fieldRef?.value?.focus()
    textAreaRef?.value?.focus()
  },
})

function copyToClipboard() {
  if (!props.disabled) return
  if (![...OneRelationFieldTypes, ...ManyRelationFieldTypes].includes(props.field.relationType)) {
    if (!props.modelValue) return
    navigator.clipboard.writeText(props.modelValue)
    notification('success', `Copied ${props.modelValue} to clipboard`)
  } else {
    let value: any = props.modelValue
    if (props.field?.remoteOptions?.labelFunction) {
      value = props.field.remoteOptions.labelFunction(initialRelationData)
    }
    if (props.field?.remoteOptions?.labelField) {
      value = initialRelationData.value?.attributes?.[props.field.remoteOptions.labelField] ?? props.modelValue
    }
    if (!value) return
    navigator.clipboard.writeText(value)
    notification('success', `Copied ${value} to clipboard`)
  }
}

const hovered = ref(false)
</script>

<template>
  <ElFormItem ref="formItemRef" :prop="field.name">
    <template #label>
      <div class="flex items-center" @click="copyToClipboard" @mouseenter="hovered = true" @mouseleave="hovered = false">
        <div class="flex-shrink-0">{{ field.label ?? startCase(field.name) }}</div>
      </div>
    </template>

    <!-- If the model is FileStorage, we show the FileInput component under the name field -->
    <template v-if="field.name === 'name' && modelName === 'UploadFile'">
      <FileInput :id="field.name" :disabled="disabled" :model-value="modelValue" class="w-full" file-preview @update:model-value="onFieldChange($event)" />
    </template>

    <!-- Scalar Fields -->
    <template v-else-if="field.relationType === FieldRelationType.Scalar">
      <!-- String or Email -->
      <div v-if="[FieldType.Email, FieldType.String].includes(field.type)" class="relative w-full">
        <ElInput
          :id="field.name"
          ref="fieldRef"
          :disabled="disabled"
          :model-value="modelValue"
          :name="field.name"
          :placeholder="field.placeholder || 'Enter ' + (field.label ?? startCase(field.name))"
          class="hover:box-shadow-none focus:box-shadow-none box-shadow-none no-border"
          size="default"
          @blur="isFieldFocused = false"
          @focus="onInputFocus"
          @update:model-value="onFieldChange($event)"
          @keyup.enter.prevent="emit('onEnterKeyPress', field.name)"
        />
      </div>

      <template v-else-if="field.type === FieldType.Boolean">
        <Checkbox
          :id="field.name"
          ref="fieldRef"
          :disabled="disabled"
          :model-value="modelValue"
          binary
          class="ml-3"
          size="small"
          @update:model-value="onFieldChange($event)"
          @keyup.enter="emit('onEnterKeyPress', field.name)"
        />
      </template>

      <!-- Text > LongText -->
      <template v-else-if="field.type === FieldType.Text">
        <span
          ref="buttonRef"
          :class="{
            'is-disabled select-none': disabled,
            'text-muted': disabled || modelValue?.length <= 0,
            'text-black hover:cursor-pointer': !disabled && modelValue?.length > 0,
          }"
          class="w-full focus:outline-main-color truncate"
          role="contentinfo"
          tabindex="0"
          @keyup.enter="descriptionPopoverVisible = !descriptionPopoverVisible"
        >
          {{ modelValue || field.placeholder || 'Enter ' + (field.label ?? startCase(field.name)) }}
        </span>
        <ElPopover
          ref="popoverRef"
          :hide-after="0"
          :virtual-ref="buttonRef"
          :visible="descriptionPopoverVisible"
          :width="currentItemWidth"
          placement="bottom-end"
          popper-class="mt-2 !z-[20000]"
          @show="onPopoverShow"
        >
          <ElInput
            ref="textAreaRef"
            :disabled="disabled"
            :model-value="modelValue"
            :placeholder="'Enter ' + (field.label ?? startCase(field.name))"
            autosize
            type="textarea"
            @update:model-value="onFieldChange($event)"
          />
        </ElPopover>
      </template>
      <!-- Number -->
      <template v-else-if="NumericFieldTypes.includes(field.type)">
        <ElInputNumber
          :id="field.name"
          ref="fieldRef"
          :controls="false"
          :disabled="disabled"
          :model-value="modelValue"
          :name="field.name"
          :placeholder="field.placeholder || 'Enter ' + (field.label ?? startCase(field.name))"
          size="default"
          @focus="onInputFocus"
          @update:model-value="onFieldChange($event)"
          @keyup.enter="emit('onEnterKeyPress', field.name)"
        />
      </template>

      <!-- Date or DateTime -->
      <template v-else-if="DateFieldTypes.includes(field.type)">
        <DatePicker
          :id="field.name"
          ref="fieldRef"
          :default-time="field.datePickerOptions?.defaultTime"
          :disabled="disabled"
          :disabled-date="field.datePickerOptions?.disabledDate"
          :format="(field.datePickerOptions?.format ?? field.type === FieldType.Date) ? 'YYYY/MM/DD' : 'YYYY/MM/DD HH:mm:ss'"
          :model-value="modelValue"
          :placeholder="field.placeholder || 'Select ' + (field.label ?? startCase(field.name))"
          :type="(field.datePickerOptions?.type ?? field.type === FieldType.Date) ? 'date' : 'datetime'"
          :value-format="(field.datePickerOptions?.valueFormat ?? field.type === FieldType.Date) ? 'YYYY-MM-DD' : 'YYYY-MM-DD HH:mm:ss'"
          @update:model-value="onFieldChange($event)"
          @on-enter-key-press="emit('onEnterKeyPress', field.name)"
        />
      </template>

      <!--  Enum -->
      <template v-else-if="field.type === FieldType.Enumeration && field.enumValues">
        <ElSelect
          :id="field.name"
          ref="fieldRef"
          :default-first-option="true"
          :disabled="disabled"
          :model-value="startCase(modelValue)"
          :placeholder="'Select ' + (field.label ?? startCase(field.name))"
          class="no-border w-full"
          clearable
          filterable
          size="default"
          @clear="onFieldChange('')"
          @update:model-value="onFieldChange($event)"
          @keyup.enter="emit('onEnterKeyPress', field.name)"
        >
          <ElOption v-for="option in field.enumValues" :key="option" :label="startCase(option)" :value="option" />
        </ElSelect>
      </template>
    </template>

    <!-- One-to-One | manyToOne Relation -->
    <template v-else-if="[...OneRelationFieldTypes, ...ManyRelationFieldTypes].includes(field.relationType)">
      <RemoteSelect
        ref="fieldRef"
        v-model="initialRelationData"
        :disabled="disabled"
        :filters="getFilters()"
        :label-field="field.remoteOptions?.labelField"
        :label-function="field.remoteOptions?.labelFunction"
        :model-name="field.target as ModelName"
        :multiple="ManyRelationFieldTypes.includes(field.relationType)"
        :option-disabled="field.remoteOptions?.optionDisabled"
        :placeholder="field.remoteOptions?.placeholder || 'Select ' + (field.label ?? startCase(field.name))"
        :return-fields="field.remoteOptions?.returnFields"
        :sections="field.remoteOptions?.sections"
        :sorter="field.remoteOptions?.sorter"
        :unique-by-key="field.remoteOptions?.uniqueByKey"
        :value-key="field.remoteOptions?.valueKey"
        class="w-full"
        clearable
        @blur="isFieldFocused = false"
        @clear="emit('update:modelValue', null)"
        @error="console.error('error', $event)"
        @focus="onInputFocus"
        @update:model-value="onRemoteChange($event)"
        @on-enter-key-press="emit('onEnterKeyPress', field.name)"
        @on-esc-key-press="emit('onEscKeyPress')"
      />
    </template>
  </ElFormItem>
</template>
