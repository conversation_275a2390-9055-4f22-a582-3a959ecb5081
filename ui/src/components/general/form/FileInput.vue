<script lang="ts" setup>
import { computed, onBeforeUnmount, ref } from 'vue'

// HeroIcons
import { ArrowUpOnSquareIcon } from '@heroicons/vue/24/outline'
import { XCircleIcon } from '@heroicons/vue/24/solid'

// Components
import { notification } from '@/utils/notifications'
import FileTypeSVG from '@/components/general/FileTypeSVG.vue'
import { ElTooltip } from 'element-plus'

// GraphQL
import { downloadOrPreviewFile } from '@/utils/dataExportMethods.ts'

const props = defineProps({
  modelValue: {
    type: Object as () => object | null,
    default: null,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  filePreview: {
    type: Boolean,
    default: false,
  },
  accept: {
    type: String,
    default: undefined,
  },
})

const emits = defineEmits(['update:modelValue', 'change', 'clear', 'preview-file'])

const fileIsOver = ref(false)
const fileUpload = ref<InstanceType<typeof HTMLInputElement> | null>(null)

const isEmptyModelValue = computed(() => {
  if (isFile(props.modelValue)) return false
  if (!props.modelValue) return true
  if (typeof props.modelValue !== 'object') return true
  return Object.values(props.modelValue).length === 0
})

onBeforeUnmount(() => {
  if (!fileUpload.value) return
  fileUpload.value.value = ''
})

function browseFiles() {
  if (props.multiple && fileUpload.value?.files && fileUpload.value.files.length > 0 && (!props.modelValue || (Array.isArray(props.modelValue) && props.modelValue.filter((file) => isFile(file)).length < 1))) {
    fileUpload.value.value = ''
  }
  if (!props.multiple && fileUpload.value?.files && fileUpload.value.files.length > 0 && !props.modelValue) {
    fileUpload.value.value = ''
  }
  fileUpload.value?.click()
}

function isNewFile(file: unknown): boolean {
  return isFile(file)
}

function previewFile(index: number, files: any) {
  if (props.filePreview) {
    downloadOrPreviewFile(files[index])
  }
}

function removeFile(file: File | any | (File | any)[] | null, index: number) {
  if (props.multiple) {
    const dt = new DataTransfer()
    const inputFiles = fileUpload.value?.files ? Array.from(fileUpload.value.files) : []
    inputFiles.forEach((f) => {
      const fileName = file instanceof File ? file.name : file && 'attributes' in file ? file.attributes?.name : ''
      const lastModified = file instanceof File ? file.lastModified : null
      if (!(f.name === fileName && f.lastModified === lastModified)) {
        dt.items.add(f)
      }
    })

    if (fileUpload.value) fileUpload.value.files = dt.files

    const files = Array.isArray(props.modelValue) ? [...props.modelValue] : [props.modelValue]
    files.splice(Number(index), 1)
    emits('update:modelValue', files)
    emits('change', files)
  } else {
    emits('update:modelValue', null)
    emits('change', null)
    emits('clear')
    if (fileUpload.value) fileUpload.value.value = ''
  }
}

function handleFileSelect(ev: Event): void {
  if (!ev) return
  const target = ev.target as HTMLInputElement
  const files = target?.files || []
  const value = [...(Array.isArray(props.modelValue) ? props.modelValue : []), ...files]
  const fileOnlyValue = value.filter(isFile) as File[]
  if (!validateFiles(fileOnlyValue)) {
    return
  }
  if (props.multiple) {
    emits('update:modelValue', value)
    emits('change', value)
    return
  }
  emits('update:modelValue', files[0])
  emits('change', files[0])
}

function isFile(value: any): boolean {
  return value instanceof File
}

function generateExtension(text: string | null | undefined) {
  if (text === undefined || text === null) return ''
  if (!text.includes('.')) return ''
  return text.split('.').pop() || ''
}

function onDrop(ev: DragEvent) {
  fileIsOver.value = false
  if (!ev?.dataTransfer?.files?.length) return
  const files = [...(Array.isArray(props.modelValue) ? props.modelValue : []), ...ev.dataTransfer.files]
  const fileOnlyValue = files.filter(isFile) as File[]
  if (!validateFiles(fileOnlyValue)) {
    return
  }
  if (props.multiple) {
    emits('update:modelValue', files)
    emits('change', files)
    return
  }
  emits('update:modelValue', files[0])
  emits('change', files[0])
}

function onDragOver() {
  fileIsOver.value = true
}

function onDragLeave() {
  fileIsOver.value = false
}

function onDragEnd() {
  fileIsOver.value = false
}

function getFileName(fileItem: File | any | (File | any)[] | null): string {
  if (!fileItem) return ''
  if (fileItem instanceof File) return fileItem?.name
  if (Array.isArray(fileItem)) return ''
  if (fileItem?.attributes) return (fileItem as any)?.attributes?.caption || ''
  return ''
}

function validateFiles(files: File[]): boolean {
  return files.some((file) => {
    if (!isFile(file) || file.size > 200000000) {
      notification('error', 'File size exceeds 200MB')
      return false
    } else if (file.size === 0) {
      notification('error', 'File size is 0')
      return false
    } else {
      return true
    }
  })
}
</script>

<template>
  <input ref="fileUpload" :accept="accept" :disabled="disabled" :multiple="multiple" name="fileUploader" style="display: none" type="file" @change="handleFileSelect" />
  <div class="flex items-center gap-2 max-w-full min-w-0 w-full h-full max-h-full min-h-0">
    <ElTooltip :content="props.multiple ? 'Upload Files' : 'Replace File'" :disabled="isEmptyModelValue" :hide-after="0" :show-after="300" effect="light">
      <div
        :class="{
          'border-emerald-600 bg-[#16a34a1a]	': fileIsOver,
          'w-full': isEmptyModelValue,
        }"
        :style="{ borderRight: !isEmptyModelValue ? '1px solid #d9d9d9' : '' }"
        class="avatar-uploader"
        @click="browseFiles"
      >
        <div class="avatar-uploader-icon" tabindex="0">
          <div class="flex flex-col" @dragend="onDragEnd" @dragover.prevent="onDragOver" @dragleave.prevent="onDragLeave" @drop.prevent="onDrop">
            <div
              :class="{
                'cursor-not-allowed': disabled,
              }"
              class="flex text-base"
            >
              <div v-if="isEmptyModelValue">Choose File</div>
              <ArrowUpOnSquareIcon class="h-[20px] w-[20px]" />
            </div>
            <div v-if="isEmptyModelValue" class="text-xs">Choose File 2</div>
          </div>
        </div>
      </div>
    </ElTooltip>
    <div v-if="!isEmptyModelValue" class="flex-1 flex flex-col min-w-0 max-w-full max-h-full min-h-0 overflow-y-auto" style="max-height: 150px">
      <div v-if="!multiple" class="max-w-full flex justify-center items-center gap-3">
        <FileTypeSVG :label="generateExtension(getFileName(modelValue))" class="size-8" />
        <div :class="[{ 'link-text-style clickable': !isNewFile(modelValue) }]" class="max-w-full truncate overflow-x-hidden" @click="!isNewFile(modelValue) && previewFile(0, [modelValue])">
          <ElTooltip :content="getFileName(modelValue)" />
        </div>
        <XCircleIcon v-if="!disabled" class="w-[20px] h-[20px] mr-1 cursor-pointer" @click.stop="removeFile(modelValue, 0)" />
      </div>
      <template v-else>
        <div v-for="(fileItem, index) in modelValue" :key="getFileName(fileItem) + index" class="max-w-full flex justify-between items-center gap-2">
          <div :class="[{ 'link-text-style clickable': !isNewFile(fileItem) }]" class="truncate overflow-x-hidden" @click="!isNewFile(fileItem) && previewFile(index, modelValue)">
            <ElTooltip :content="getFileName(fileItem)" />
          </div>
          <XCircleIcon v-if="!disabled" class="w-[20px] h-[20px] min-w-[20px] min-h-[20px] mr-1 cursor-pointer" @click.stop="removeFile(fileItem, index)" />
        </div>
      </template>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.avatar-uploader {
  @apply flex justify-center items-center min-w-[50px] cursor-pointer;
  .avatar-uploader-icon {
    @apply px-2 text-[28px] text-center;
    color: #8c939d;
  }
}
</style>
