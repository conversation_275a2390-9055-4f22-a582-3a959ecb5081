<script lang="ts" setup>
import type { Address, Country, CountryFiltersInput, Pagination, Query, QueryCountriesArgs, QueryStatesArgs, State, StateFiltersInput } from '@/gql/graphql'
import { ApolloError } from '@apollo/client'
import { useQuery } from '@vue/apollo-composable'
import { ElFormItem, ElInput, ElOption, ElPopover, ElSelect } from 'element-plus'
import { computed, nextTick, onBeforeMount, ref, watch } from 'vue'

import { cloneDeep, isEmpty, isEqual, set } from 'lodash-es'
import { DocumentDuplicateIcon } from '@heroicons/vue/24/solid'
import { getFormattedAddress } from '@/utils/helpers'
import type { AddressJsonType } from '@/utils/types'
import { notification } from '@/utils/notifications'
import { GetMany } from '@/utils/queryMethods'
import { onClickOutside } from '@vueuse/core'
import { isOutsideClick } from '@/utils/dynamicForm.ts'
import type { AddressProps } from './types.ts'

// will come from backend eventually
const possibleAddressFields: AddressJsonType = {
  Country: 'Country',
  'Street Address 1': 'Street Address 1',
  'Address 2': 'Address 2',
  City: 'City',
  State: 'State',
  'Postal Code': 'Postal Code',
}

const rules: Rules = {
  default: [
    possibleAddressFields['Country'],
    possibleAddressFields['Street Address 1'],
    possibleAddressFields['Address 2'],
    possibleAddressFields['City'],
    possibleAddressFields['State'],
    possibleAddressFields['Postal Code'],
  ],
  'United Kingdom': [
    possibleAddressFields['Country'],
    possibleAddressFields['Street Address 1'],
    possibleAddressFields['Address 2'],
    possibleAddressFields['City'],
    possibleAddressFields['Postal Code'],
  ],
}

interface FieldsRefs {
  [key: string]: InstanceType<typeof ElInput> | InstanceType<typeof ElSelect>
}

const props = withDefaults(defineProps<AddressProps>(), {
  customClass: '',
  label: undefined,
  modelValue: undefined,
  disabled: false,
  disabledMessage: '',
})

const emits = defineEmits<{
  (event: 'change', value: Address): void
  (event: 'update:modelValue', value: Address): void
  (event: 'onEnterKeyPress'): void
}>()

interface Rules {
  [key: string]: string[]
}

const enableCountries = ref(false)
const enableStates = ref(false)
const countriesPagination = ref<Partial<Pagination>>({ page: 1, pageSize: 25 })

const fieldsRefs = ref<FieldsRefs>(
  Object.keys(possibleAddressFields).reduce(
    (
      acc: {
        [key: string]: any
      },
      key,
    ) => {
      acc[key] = null
      return acc
    },
    {},
  ),
)

const countriesFilter = ref<CountryFiltersInput>({
  Status: { Name: { eq: 'Active' } },
  name: { notIn: ['Canada', 'United States'] },
})

const statesFilter = ref<StateFiltersInput>({})

const { onResult: onCountriesResultCaUs } = useQuery<Query, QueryCountriesArgs>(
  GetMany('Country'),
  {
    filters: {
      name: {
        in: ['United States'],
      },
    },
    sort: ['name:desc'],
  },
  () => ({
    enabled: enableCountries.value,
  }),
)

const {
  onResult: onCountriesResult,
  loading: countriesLoading,
  fetchMore: fetchMoreCountries,
} = useQuery<Query, QueryCountriesArgs>(
  GetMany('Country'),
  {
    filters: countriesFilter.value,
    pagination: { page: countriesPagination.value.page, pageSize: countriesPagination.value.pageSize },
    sort: ['name:asc'],
  },
  () => ({
    enabled: enableCountries.value,
  }),
)

const statePagination = ref<Partial<Pagination>>({ page: 1, pageSize: 25 })

const stateVariables = ref<{ filters: StateFiltersInput; pagination: Partial<Pagination> }>({
  filters: statesFilter.value,
  pagination: { page: statePagination.value.page, pageSize: statePagination.value.pageSize },
})

const {
  onResult: onStatesResult,
  loading: statesLoading,
  fetchMore: fetchMoreStates,
} = useQuery<Query, QueryStatesArgs>(GetMany('State'), stateVariables.value, () => ({
  enabled: enableStates.value,
}))

const formData = ref<Partial<AddressJsonType>>(getFormFields(''))
const tempFormData = ref<Partial<AddressJsonType>>(getFormFields(''))

const countries = ref<Country[]>([])
const countriesUsCa = ref<Country[]>([])
const states = ref<State[]>([])

const formItemRef = ref<InstanceType<typeof ElFormItem>>()
const popoverRef = ref()
const buttonRef = ref<HTMLElement>()
const addressContentRef = ref<HTMLElement>()
const dynamicTooltipRef = ref()

const popoverVisible = ref(false)

const displayAddress = computed(() => {
  const hasValues = Object.values(formData.value).filter((v) => v?.length > 0)
  if (hasValues.length === 0) {
    return ''
  }
  return getFormattedAddress(formData.value)
})

onBeforeMount(() => {
  // initialize the AddressEntity if it is empty
  if (isEmpty(props.modelValue) || isEmpty(props.modelValue)) {
    const addressEntity: Address = {
      address: getFormFields(''),
      documentId: props.modelValue?.documentId || '',
    }

    emits('update:modelValue', addressEntity)
  }
  setFormData()
})

onCountriesResultCaUs((result) => {
  if (!result.data) return
  if (countriesUsCa.value.length !== 0) return
  const data = result.data?.countries_connection?.nodes || []
  countriesUsCa.value = cloneDeep(data)
})

onCountriesResult((result) => {
  if (!result.data) return
  if (countries.value.length !== 0) return
  const data = result.data?.countries_connection?.nodes || []
  countries.value = cloneDeep(data)
  countriesPagination.value = cloneDeep(result.data.countries_connection?.pageInfo || {})
})

onStatesResult((result) => {
  if (!result.data) return
  if (states.value.length !== 0) return
  const data = result.data.states_connection?.nodes || []
  states.value = cloneDeep(data)
  statePagination.value = cloneDeep(result.data.states_connection?.pageInfo || {})
})

onClickOutside(addressContentRef, (event) => {
  if (!isOutsideClick(event)) return
  closePopover()
})

watch(
  () => props.modelValue,
  () => {
    if (isEqual(props.modelValue?.address, formData.value)) return
    if (props.modelValue?.address) {
      setFormData()
    } else {
      formData.value = getFormFields('')
      tempFormData.value = getFormFields('')
    }
  },
  { deep: true },
)

const hasAddressChanges = computed(() => !isEqual(formData.value, tempFormData.value))

const currentItemWidth = computed(() => formItemRef.value?.$el?.clientWidth)

const isCountrySelected = computed(() => !!formData.value.Country)
const isUsOrCanadaSelected = computed(() => formData.value.Country === 'United States' || formData.value.Country === 'Canada')

watch(
  () => formData.value,
  (value) => {
    const addressValue = cloneDeep(value)
    const addressData = set(cloneDeep(props?.modelValue || {}), 'address', addressValue) as Address
    emits('update:modelValue', addressData || {})
    emits('change', addressData || {})
  },
  { deep: true },
)

function setFormData() {
  if (isEmpty(props?.modelValue?.address)) {
    formData.value = getFormFields('')
    tempFormData.value = getFormFields('')
  } else {
    const data = props.modelValue?.address as AddressJsonType
    formData.value = cloneDeep(data)
    tempFormData.value = cloneDeep(data)
    if (data.Country) {
      stateVariables.value.filters = {
        Country: { name: { eq: data.Country } },
      }
      enableStates.value = true
    }
  }
}

function getFormFields(country: string): Partial<AddressJsonType> {
  const addressFields: Partial<AddressJsonType> = {}
  const fields = rules[country] ? rules[country] : rules['default']
  for (const field in possibleAddressFields) {
    if (fields.includes(field)) {
      const f = field as keyof AddressJsonType
      addressFields[f] = ''
    }
  }

  return addressFields
}

async function onCountrySelect(name: string) {
  if (!name) return
  try {
    stateVariables.value.filters = {
      Country: { name: { eq: name } },
    }
    formData.value.State = ''
    states.value = []
    enableStates.value = true
    formData.value = getFormFields(name || '')
    formData.value.Country = name
  } catch (error) {
    const err = error as ApolloError
    notification('error', err.message)
  }
}

async function fetchMoreCountriesMethod(queryString: string, loadMore = false, clear = false) {
  if (countriesLoading.value) return
  if (!queryString && !loadMore && !clear) return

  try {
    if (queryString.length > 0) {
      countriesFilter.value = {
        ...countriesFilter.value,
        name: { containsi: queryString },
      }
    } else {
      countriesFilter.value = {
        Status: { Name: { eq: 'Active' } },
      }
    }

    const page = loadMore ? countriesPagination.value.page! + 1 : 1
    const results = await fetchMoreCountries({
      variables: {
        filters: countriesFilter.value,
        pagination: { page, pageSize: countriesPagination.value.pageSize },
      },
    })

    const data = cloneDeep(results?.data.countries_connection?.nodes || [])
    if (loadMore && results) {
      countries.value.push(...data)
      countriesPagination.value = cloneDeep(results?.data.countries_connection?.pageInfo || {})
      return
    }
    if (results) {
      countries.value = data
      countriesPagination.value = cloneDeep(results?.data.countries_connection?.pageInfo || {})
    }
  } catch (error) {
    const err = error as ApolloError
    notification('error', err.message)
  }
}

async function fetchMoreStatesMethod(queryString: string, loadMore = false, clear = false) {
  if (!queryString && !loadMore && !clear) return
  try {
    statesFilter.value = {
      Country: { name: { eq: formData.value.Country } },
    }

    if (queryString.length > 0) {
      statesFilter.value = {
        ...statesFilter.value,
        name: { containsi: queryString },
      }
    }

    const page = loadMore ? statePagination.value.page! + 1 : 1
    const results = await fetchMoreStates({
      variables: {
        filters: statesFilter.value,
        pagination: { page, pageSize: statePagination.value.pageSize },
      },
    })
    const data = cloneDeep(results?.data.states_connection?.nodes || [])
    if (loadMore && results) {
      states.value.push(...data)
      statePagination.value = cloneDeep(results?.data.states_connection?.pageInfo || {})
      return
    }

    if (results) {
      states.value = data
      statePagination.value = cloneDeep(results?.data.states_connection?.pageInfo || {})
    }
  } catch (error) {
    const err = error as ApolloError
    notification('error', err.message)
  }
}

function isFieldInForm(field: string) {
  const fields = Object.keys(formData.value)
  return fields.includes(field)
}

function clearForm() {
  formData.value = getFormFields('')
  states.value = []
}

function onClearCountries() {
  countries.value.length ? fetchMoreCountriesMethod('', false, true) : (enableCountries.value = true)
  clearForm()
}

function saveAddressChanges() {
  tempFormData.value = cloneDeep(formData.value)
  closePopover()
}

function closePopover() {
  popoverVisible.value = false
}

function getNextField(field: keyof AddressJsonType) {
  const fields = Object.keys(possibleAddressFields)
  const index = fields.indexOf(field)
  if (index === fields.length - 1) {
    return fields[0]
  }
  return fields[index + 1]
}

async function jumpToNextField(field: keyof AddressJsonType) {
  const isLastField = field === 'Postal Code'
  if (isLastField) {
    closePopover()
    // wait for popover to close
    await nextTick()
    emits('onEnterKeyPress')
    return
  }

  const nextField = getNextField(field)
  fieldsRefs?.value[nextField]?.focus()
}

function onFieldEnter(field: keyof AddressJsonType) {
  if (formData.value[field] !== tempFormData.value[field]) {
    jumpToNextField(field)
  }
}

function onPopoverVisible() {
  fieldsRefs?.value['Country']?.focus()
}

function showPopOver() {
  if (props.disabled) return
  if (!props.modelValue?.documentId && !formData.value.Country) {
    formData.value.Country = 'United States'
    onCountrySelect(formData.value.Country)
  }

  popoverVisible.value = !popoverVisible.value
}

defineExpose({
  hasAddressChanges,
  saveAddressChanges,
  closePopover,
  focus: () => buttonRef.value?.focus(),
})

function copyToClipboard() {
  if (!displayAddress.value) return
  navigator.clipboard.writeText(displayAddress.value)
  notification('success', `Copied ${displayAddress.value} to clipboard`)
}

const hovered = ref(false)
</script>

<template>
  <ElFormItem ref="formItemRef" :class="[readOnly || disabled ? 'cursor-not-allowed ' : 'cursor-pointer']" label="Address" placeholder="Enter Address" prop="Address" size="small">
    <template #label>
      <div class="flex items-center" @click="copyToClipboard" @mouseenter="hovered = true" @mouseleave="hovered = false">
        <div class="flex-shrink-0 text-sm">Address</div>
      </div>
    </template>

    <div
      v-if="currentItemWidth"
      ref="dynamicTooltipRef"
      :class="[readOnly || disabled ? 'is-disabled cursor-not-allowed' : 'cursor-pointer']"
      :style="{ maxWidth: `${currentItemWidth * 0.8}px` }"
      class="w-full truncate pl-3"
      @click="showPopOver"
    >
      <p v-if="readOnly && currentItemWidth" :class="{ 'inactive-color': disabled }" class="w-full h-full truncate" role="contentinfo" @click="showPopOver">
        {{ displayAddress }}
      </p>
      <p
        v-if="!readOnly && currentItemWidth"
        ref="buttonRef"
        :class="[{ 'placeholder-color': !displayAddress || disabled }, customClass]"
        class="hover:cursor-pointer w-full truncate text-sm"
        role="contentinfo"
        tabindex="0"
        @keyup.enter="popoverVisible = !popoverVisible"
      >
        {{ displayAddress ? displayAddress : 'Enter Address' }}
      </p>
    </div>
    <DocumentDuplicateIcon v-if="hovered && disabled" class="size-5 main-icon-color absolute right-0" />
  </ElFormItem>
  <ElPopover
    ref="popoverRef"
    :hide-after="0"
    :virtual-ref="dynamicTooltipRef"
    :visible="popoverVisible && !disabled"
    :width="currentItemWidth"
    placement="bottom-end"
    popper-class="mt-2"
    @show="onPopoverVisible"
  >
    <div ref="addressContentRef" v-focustrap class="py-1 space-y-1 w-full">
      <div class="w-full">
        <ElSelect
          :ref="(ref: any) => (fieldsRefs['Country'] = ref)"
          v-model="formData.Country"
          :default-first-option="true"
          :remote-method="(queryString: string) => fetchMoreCountriesMethod(queryString)"
          filterable
          placeholder="Select Country"
          remote
          size="small"
          @change="onCountrySelect"
          @clear="onClearCountries"
          @visible-change="enableCountries = true"
          @keyup.enter="onFieldEnter('Country')"
        >
          <template #header>
            <span class="secondary-color text-sm pl-2 mb-1"> Selected </span>
            <br />
            <p v-if="formData['Country']" class="text-sm truncate blue-color pl-2">
              {{ formData['Country'] }}
            </p>
          </template>
          <ElOption v-for="item in countriesUsCa.concat(countries)" :key="item?.name" :label="item?.name!" :value="item?.name!" />

          <template v-if="countries.length < countriesPagination.total!" #footer>
            <MainButton :loading="countriesLoading" label="Load More" severity="info" variant="text" @click="fetchMoreCountriesMethod('', true)" />
          </template>
        </ElSelect>
      </div>
      <div class="w-full space-y-2">
        <ElInput
          :ref="(ref: any) => (fieldsRefs['Street Address 1'] = ref)"
          v-model="formData['Street Address 1']"
          :disabled="!isCountrySelected"
          placeholder="Street Address 1"
          size="small"
          @keyup.enter="onFieldEnter('Street Address 1')"
        />
        <ElInput
          :ref="(ref: any) => (fieldsRefs['Address 2'] = ref)"
          v-model="formData['Address 2']"
          :disabled="!isCountrySelected"
          placeholder="Street Address 2"
          size="small"
          @keyup.enter="onFieldEnter('Address 2')"
        />
      </div>
      <div class="w-full flex items-center justify-between space-x-2">
        <div class="w-1/2">
          <ElInput
            :ref="(ref: any) => (fieldsRefs['City'] = ref)"
            v-model="formData.City"
            :disabled="!isCountrySelected"
            placeholder="City"
            size="small"
            @keyup.enter="onFieldEnter('City')"
          />
        </div>
        <ElSelect
          v-if="isUsOrCanadaSelected"
          :ref="(ref: any) => (fieldsRefs['State'] = ref)"
          v-model="formData.State"
          :default-first-option="true"
          :disabled="!isCountrySelected"
          :loading="statesLoading"
          :remote-method="(queryString: string) => fetchMoreStatesMethod(queryString)"
          clearable
          filterable
          placeholder="Select State"
          remote
          size="small"
          @clear="
            () => {
              fetchMoreStatesMethod('', false, true)
            }
          "
          @keyup.enter="onFieldEnter('State')"
        >
          <template #header>
            <span class="secondary-color text-sm pl-2 mb-1"> Selected </span>
            <br />
            <p v-if="formData['State']" class="text-sm truncate blue-color pl-2">
              {{ formData['State'] }}
            </p>
          </template>
          <ElOption v-for="item in states" :key="item?.name!" :label="item?.name!" :value="item?.name!" />
          <template v-if="states.length < statePagination.total!" #footer>
            <MainButton :loading="statesLoading" label="Load More" severity="info" variant="text" @click="fetchMoreStatesMethod('', true)" />
          </template>
        </ElSelect>
        <ElInput
          v-else
          :ref="(ref: any) => (fieldsRefs['State'] = ref)"
          v-model="formData.State"
          :disabled="!isCountrySelected"
          placeholder="State"
          size="small"
          @keyup.enter="onFieldEnter('State')"
        />
        <div class="w-[55%]">
          <ElInput
            v-if="isFieldInForm(possibleAddressFields['Postal Code'])"
            :ref="(ref: any) => (fieldsRefs['Postal Code'] = ref)"
            v-model="formData['Postal Code']"
            :disabled="!isCountrySelected"
            placeholder="Zip Code"
            size="small"
            type="number"
            @keyup.enter="onFieldEnter('Postal Code')"
          />
        </div>
      </div>
    </div>
  </ElPopover>
</template>

<style lang="scss" scoped>
@use '@/assets/mixins' as m;

@include m.custom-form-style();

:deep(#address) {
  cursor: pointer;
}

.blue-color {
  color: var(--text-blue-500);
}

.secondary-color {
  color: var(--text-slate-500);
}
</style>
