<script generic="T extends { documentId?: string }" lang="ts" setup>
import { ElButton, ElForm, ElFormItem, type FormItemRule } from 'element-plus'
import { computed, defineAsyncComponent, nextTick, onBeforeMount, onBeforeUnmount, ref, watch } from 'vue'
import { cloneDeep } from '@apollo/client/utilities'
import { camelCase, debounce, has, isBoolean, isDate, isEmpty, isEqual, isNumber, startCase, upperFirst } from 'lodash-es' // local imports
import { getFieldClasses, getFormClasses, getInitialFormData, getModelFormFields, setFormFieldsColor } from '@/utils/dynamicForm'
import { findDifferences } from '@/utils/helpers'
import { useModelStore } from '@/stores/model.store'

import type { Emits, ExtraSlotField, FieldsConfig, FormField, GroupConfig, Layouts, ModelName, ProcessedGroup, Rules } from './types'
import FormTitle from './FormTitle.vue'
import DynamicField from './DynamicField.vue'
import AddressComponent from '@/components/general/form/AddressComponent.vue'
import { twMerge } from 'tailwind-merge'
import { notification } from '@/utils/notifications'
import { FieldType, ManyRelationFieldTypes, OneRelationFieldTypes, ScalarFieldTypes } from '@/utils/constants/fieldTypes'
import { GetOne, type ReturnFieldProp } from '@/utils/queryMethods'
import { useLazyQuery } from '@vue/apollo-composable'
import { DocumentDuplicateIcon } from '@heroicons/vue/24/solid'
import FormLayout from '@/components/general/form/FormLayout.vue'
import type { Maybe } from '@/gql/graphql.ts'

const CloseFormConfirmationDialog = defineAsyncComponent(() => import('@/components/general/CloseConfirmationDialog.vue'))

interface Props {
  data: T // initial data for the form
  fields: string[]
  modelName: ModelName

  saveButtonRef?: InstanceType<typeof ElButton> | null // if you choose to use your save button and not the default one
  class?: string
  groupsConfig?: GroupConfig[]
  showActionButtons?: boolean
  showCloseButton?: boolean
  showEditButton?: boolean
  loading?: boolean
  disabled?: boolean
  isModal?: boolean
  isCollapsed?: boolean
  labelWidth?: string
  title?: Maybe<string>
  fieldsConfig?: FieldsConfig<T>
  layout?: Layouts
  columns?: number // for grid layout or inline
  labelPosition?: 'left' | 'right' | 'top'
  requireAsteriskPosition?: 'left' | 'right'
  size?: 'default' | 'small' | 'large'
}

const props = withDefaults(defineProps<Props>(), {
  labelPosition: 'left',
  requireAsteriskPosition: 'right',
  labelWidth: '100px',
  size: 'large',
  loading: false,
  disabled: false,
  title: undefined,
  isModal: false,
  showCloseButton: true,
  showActionButtons: true,
  columns: undefined,
  showEditButton: false,
  isCollapsed: false,
  class: '',
  saveButtonRef: undefined,
  layout: 'default',
  rules: () => ({}),
  value: () => ({}),
  returnFields: undefined,
  groupsConfig: () => [],
  fieldsConfig: () => ({}) as FieldsConfig<T>,
})

const emits = defineEmits<Emits>()

type FieldSlot = (props: {
  formData: Record<string, any>
  allModelData: T
  disabled: boolean
  fieldName: string
  fieldsRefs: Record<string, any>
  handleEnter: (field: string) => void
}) => any

const slots = defineSlots<{
  [slot: `field-${string}`]: FieldSlot

  [slot: string]: any
  'field-selectExisting': FieldSlot
  footer?: () => any
  deleteButton?: () => any
  confirmationCheckbox?: () => any
  nextAction?: () => any
  headerLeft?: () => any
  headerRight?: () => any
  title?: () => any
}>()

// all model data and slots initial data
const initialAllModelData = ref<T>(cloneDeep(props.data) || {})
const allModelData = ref<T>(cloneDeep(props.data) || {})

const missingFieldsData = ref<T>()

// element plus form data key value(id | scalar)
const initialFormData = ref<Record<string, any>>({})
const formData = ref<Record<string, any>>({})

const formRules = ref<Rules>({})
const formFields = ref<FormField[]>([])
const groupedFields = ref<ProcessedGroup[]>([])

const saveButtonRef = ref<InstanceType<typeof ElButton> | null>(null)

const formRef = ref<InstanceType<typeof ElForm>>()
const addressRefs = ref<InstanceType<typeof AddressComponent>[]>([])
const fieldsRefs = ref<Record<string, InstanceType<typeof DynamicField> | null>>({})

const showConfirmCloseModal = ref(false)
const editButtonVisible = ref(false)
const formDataLoading = ref(false)

const formVisible = ref(true)

const fieldsLoading = ref<Map<string, boolean>>(new Map<string, boolean>())

const isUpdate = computed(() => !!props.data.documentId)
const isDisabled = computed(() => props.disabled || editButtonVisible.value || props.loading)

// Slots named field-*
const fieldSlotNames = computed(() =>
  Object.keys(slots)
    .filter((slotName) => slotName.startsWith('field-'))
    .map((slotName) => slotName.substring(6)),
)

onBeforeMount(() => {
  buildForm()
})

onBeforeUnmount(() => {
  closePopovers()
})

watch(
  () => props.fieldsConfig,
  () => {
    setupFormFields()
    // to make the changed fields gray if the edit button is still visible
    if (editButtonVisible.value) {
      toggleFormItemsEditModeStyle(true)
    }
  },
  {
    deep: true,
    immediate: false,
  },
)

watch(
  () => props.groupsConfig,
  () => {
    setupFormFields()
    // to make the changed fields gray if the edit button is still visible
    if (editButtonVisible.value) {
      toggleFormItemsEditModeStyle(true)
    }
  },
  {
    deep: true,
    immediate: false,
  },
)

watch(
  () => formRef.value,
  () => {
    toggleFormItemsEditModeStyle(true)
  },
)

watch(
  () => editButtonVisible.value,
  () => {
    toggleFormItemsEditModeStyle(editButtonVisible.value)
  },
)

watch(
  () => formRef.value?.fields,
  () => {
    focusFirstField()
  },
  { once: true, deep: true },
)

watch(
  () => props.saveButtonRef,
  () => {
    if (props.saveButtonRef) {
      saveButtonRef.value = props.saveButtonRef
    }
  },
)

const isInlineOrGroupLayout = computed(() => ['inline', 'group'].includes(props.layout))

async function toggleFormItemsEditModeStyle(toggleGrayStyle: boolean) {
  if (!isInlineOrGroupLayout.value || !isUpdate.value) return
  await nextTick()
  const formEl = formRef.value?.$el
  setFormFieldsColor(props.layout, formEl, toggleGrayStyle)
}

const unSavedChanges = computed(() => addressRefs?.value?.some((component) => component.hasAddressChanges) || !isEqual(initialFormData.value, formData.value))
const hasEntityStateField = computed(() => {
  const model = useModelStore().getModel(props.modelName)
  return !!model?.fields.find((field) => field.name === 'EntityState')
})

const shouldShowEditButton = computed(() => {
  // in case the model does not have EntityState field
  if (((isInlineOrGroupLayout.value && !hasEntityStateField.value) || props.showEditButton) && editButtonVisible.value) {
    return true
  }

  return !!(isInlineOrGroupLayout.value && props.showEditButton && editButtonVisible.value)
})

const extraFieldsSlots = computed<ExtraSlotField[]>(() => {
  const fieldNames = formFields.value.map((field) => field.name)
  return fieldSlotNames.value
    .filter((slotFieldName) => !fieldNames.includes(slotFieldName))
    .map((name) => ({
      name,
      icon: props?.fieldsConfig?.[name as keyof T]?.icon,
      label: props?.fieldsConfig?.[name as keyof T]?.label,
      disabled: props?.fieldsConfig?.[name as keyof T]?.disabled || false,
      visibleMethod: props?.fieldsConfig?.[name as keyof T]?.visibleMethod,
    }))
})

const selectExistingSlot = computed(() => extraFieldsSlots.value.find((slot) => slot.name === 'selectExisting'))

const isValid = computed(() => formRef.value?.fields.every((field) => field.validateState !== 'error'))

async function setFormData(): Promise<void> {
  if (isInlineOrGroupLayout.value && isUpdate.value) {
    editButtonVisible.value = true
  }
  initialFormData.value = getInitialFormData(initialAllModelData.value, formFields.value, fieldSlotNames.value)
  formData.value = cloneDeep(initialFormData.value)

  allModelData.value = cloneDeep(initialAllModelData.value)

  if (!isUpdate.value || !isEmpty(missingFieldsData.value)) return

  // check if there is any field with missing data and populate it
  const missingData = (await getMissingFieldsData()) as T
  if (missingData && Object.keys(missingData).length > 0) {
    missingFieldsData.value = missingData
    initialAllModelData.value = cloneDeep({ ...allModelData.value, ...missingData })
    allModelData.value = cloneDeep(initialAllModelData.value)

    initialFormData.value = getInitialFormData(allModelData.value, formFields.value, fieldSlotNames.value)
    formData.value = cloneDeep(initialFormData.value)
  }
}

function setupFormFields() {
  const { rules, fields } = getModelFormFields<T>({
    modelName: props.modelName,
    fields: props.fields,
    fieldsConfig: props.fieldsConfig,
    // recordId is used to validate unique fields
    recordId: props.data?.documentId,
  })

  // Add custom rules from fieldsConfig
  for (const slotField of fieldSlotNames.value) {
    const fieldsConfigKey = slotField as keyof T
    if (props?.fieldsConfig && Array.isArray(props.fieldsConfig[fieldsConfigKey]?.rules)) {
      rules[slotField] = [...(props.fieldsConfig[fieldsConfigKey].rules as FormItemRule[])]
    }
  }

  formRules.value = rules
  formFields.value = fields

  setGroupedFields(fields)
}

function setGroupedFields(fields: FormField[]) {
  if (['group', 'group-modal'].includes(props.layout) && props.groupsConfig && props.groupsConfig.length > 0) {
    groupedFields.value = props.groupsConfig.map((group) => {
      const groupFields = group.fields
        .map((fieldName) => {
          const field = fields.find((f) => f.name === fieldName)
          if (field) return field
          if (fieldSlotNames.value.includes(fieldName)) {
            const fieldConfigKey = fieldName as keyof T

            return {
              name: fieldName,
              label: props?.fieldsConfig?.[fieldConfigKey]?.label || upperFirst(fieldName),
              icon: props?.fieldsConfig?.[fieldConfigKey]?.icon,
              visibleMethod: props?.fieldsConfig?.[fieldConfigKey]?.visibleMethod,
              disabled: props?.fieldsConfig?.[fieldConfigKey]?.disabled || false,
              showRequired: props?.fieldsConfig?.[fieldConfigKey]?.showRequired,
            } as FormField
          } else {
            return null
          }
        })
        .filter((f) => f !== null) as FormField[]

      return {
        ...group,
        class: group.class || 'col-span-full',
        fields: groupFields,
      }
    })
  }
}

function closeForm() {
  if (unSavedChanges.value) {
    showConfirmCloseModal.value = true
  } else {
    emits('close')
  }
}

function resetFields() {
  formRef.value?.resetFields()
}

const validatingData = ref(false)

async function submitForm() {
  validatingData.value = true
  await formRef.value?.validate((valid) => {
    if (valid) {
      const data = isUpdate.value ? findDifferences(formData.value, initialFormData.value) : sanitizeFormData(formData.value)
      for (const field of formFields.value) {
        // sets the address fields to be AddressEntity Object
        if (field.target === 'Address' && !isEqual(allModelData.value[field.name]?.address, initialAllModelData.value[field.name]?.address)) {
          if (!allModelData.value[field.name]?.documentId) {
            const addressData = allModelData.value[field.name]?.address || {}
            const allValuesEmpty = Object.values(addressData).every((value) => !value)

            if (!allValuesEmpty) {
              data[field.name] = allModelData.value[field.name]
            }
          } else {
            data[field.name] = allModelData.value[field.name]
          }
        }
      }

      emits('onSubmit', data, successCallBack)
    } else {
      const formFields = formRef.value?.fields
      if (!formFields) return

      const firstErrorField = formFields.find((field) => field.validateState === 'error')
      focusField(firstErrorField?.prop as string)
    }
  })
  validatingData.value = false
}

function successCallBack() {
  if (isInlineOrGroupLayout.value) {
    editButtonVisible.value = true
  }
  initialFormData.value = cloneDeep(formData.value)
  initialAllModelData.value = cloneDeep(allModelData.value)
  if (addressRefs.value) {
    // save the address changes so the address component will not show the unsaved changes
    for (const addressRef of addressRefs.value) {
      addressRef.saveAddressChanges()
    }
  }
  formRef.value?.clearValidate()
  closePopovers()
}

function onConfirmClose() {
  if (!editButtonVisible.value && isInlineOrGroupLayout.value) {
    if (isUpdate.value) {
      editButtonVisible.value = true
    }
    //reset form to initial state
    formRef.value?.clearValidate()
    formRef.value?.resetFields()
    initialAllModelData.value = cloneDeep({ ...props.data, ...missingFieldsData.value })
    setFormData()

    closePopovers()
    emits('onClear')
    if (!isUpdate.value) {
      emits('close')
    }
  } else {
    emits('close')
  }
}

function toggleCancelEdit() {
  if (unSavedChanges.value) {
    showConfirmCloseModal.value = true
  } else {
    editButtonVisible.value = true
    closePopovers()
  }
}

function beforeEnter(el: HTMLElement) {
  el.style.height = '0'
  el.style.opacity = '0'
  el.style.overflow = 'hidden'
}

function enter(el: HTMLElement) {
  el.style.transition = 'height 0.3s ease-out, opacity 0.3s ease-out'
  el.style.height = `${el.scrollHeight}px`
  el.style.opacity = '1'
}

function afterEnter(el: HTMLElement) {
  el.style.height = 'auto'
  el.style.overflow = 'visible'
}

function beforeLeave(el: HTMLElement) {
  el.style.height = `${el.scrollHeight}px`
  el.style.opacity = '1'
  el.style.overflow = 'hidden'
}

function leave(el: HTMLElement) {
  el.style.transition = 'height 0.3s ease-in, opacity 0.3s ease-in'
  el.style.height = '0'
  el.style.opacity = '0'
}

function toggleEdit() {
  editButtonVisible.value = !editButtonVisible.value
  focusFirstField()
}

function clearValidate(fields: string[] = []) {
  formRef.value?.clearValidate(fields)
}

async function validate() {
  await formRef.value?.validate()
}

// validates the formObject and returns a new object with only the valid fields
function sanitizeFormData(data: Record<string, any>): Record<string, any> {
  return Object.entries(data).reduce(
    (newData, [key, value]) => {
      if (isNumber(value) || isDate(value) || isBoolean(value) || !isEmpty(value) || value instanceof File) {
        newData[key] = value
      }
      return newData
    },
    {} as Record<string, any>,
  )
}

async function buildForm() {
  if (props.saveButtonRef) {
    saveButtonRef.value = props.saveButtonRef
  }
  setupFormFields()
  await setFormData()
}

function onFieldChange(field: FormField, data: any) {
  allModelData.value[field.name] = data

  if (field.name === 'Address') {
    formData.value[field.name] = data?.documentId || data.address?.Country || null
  }

  emits('onFieldChange', field.name, data)
}

async function getMissingFieldsData(): Promise<Record<string, any> | null> {
  const returnFields: ReturnFieldProp = buildReturnFields(true)
  if (returnFields.length === 0) return null

  try {
    const query = GetOne(props.modelName, returnFields)
    const { load } = useLazyQuery(query, { documentId: initialAllModelData.value.documentId }, { fetchPolicy: 'network-only' })
    const response = await load()
    const modelName = camelCase(props.modelName)

    if (response && response[modelName]) {
      return response[modelName]
    }

    return null
  } catch (error) {
    console.error(error)
    const err = error as Error
    notification('error', err?.message)
  } finally {
    fieldsLoading.value.clear()
    formDataLoading.value = false
  }

  return null
}

/**
 * Rebuilds the form with new data.
 * based on the new data provided.
 */
async function rebuildForm(data: any) {
  initialAllModelData.value = cloneDeep(data)
  allModelData.value = cloneDeep(initialAllModelData.value)
  await buildForm()
  await nextTick()
  clearValidate()
  closePopovers()
}

function closePopovers() {
  if (addressRefs.value) {
    for (const addressRef of addressRefs.value) {
      addressRef?.closePopover()
    }
  }

  if (fieldsRefs.value) {
    for (const fieldRef of Object.values(fieldsRefs.value)) {
      if (fieldRef && fieldRef?.closePopover) {
        fieldRef?.closePopover()
      }
    }
  }
}

function isFieldEditable(fieldName: keyof T): boolean {
  const config = props.fieldsConfig?.[fieldName]
  if (!config) return true

  if (typeof config?.disabled === 'function') {
    return !config.disabled(formData.value, allModelData.value)
  }

  if (has(config, 'disabled')) {
    return !config.disabled
  }

  if (typeof config.visibleMethod === 'function') {
    return config.visibleMethod(formData.value, allModelData.value)
  }

  return true
}

function jumpToNextField(fieldName: string) {
  if (isInlineOrGroupLayout.value && isUpdate.value) {
    focusSaveButton()
    return
  }

  const fieldRelationType = formFields.value.find((field) => field.name === fieldName)?.relationType
  if (!fieldRelationType) {
    return
  }
  if (ManyRelationFieldTypes.includes(fieldRelationType)) {
    return
  }

  const slotFieldIndex = extraFieldsSlots.value.findIndex((field) => field.name === fieldName)
  if (slotFieldIndex !== -1) {
    const nextField = extraFieldsSlots.value[slotFieldIndex + 1]
    if (!nextField) {
      focusSaveButton()
      return
    }
    const isEditable = isFieldEditable(nextField.name as keyof T)

    if (isEditable) {
      focusField(nextField.name)
    } else {
      jumpToNextField(nextField.name)
    }

    return
  }

  const index = formFields.value.findIndex((field) => field.name === fieldName)
  const nextField = formFields.value[index + 1]
  if (!nextField) {
    focusSaveButton()
    return
  }

  const isEditable = isFieldEditable(nextField.name as keyof T)
  if (!isEditable) {
    jumpToNextField(nextField.name)
    return
  }

  if (nextField?.target === 'Address') {
    addressRefs.value[0]?.focus()
    return
  }
  if (nextField?.type === FieldType.Boolean) {
    jumpToNextField(nextField.name)
    return
  }

  focusField(nextField.name)
}

function onFieldEnterPress(field: string) {
  if (field in formData.value && field in initialFormData.value) {
    if (formData.value[field] !== initialFormData.value[field]) {
      jumpToNextField(field)
    }
  }
}

function focusField(name: string) {
  if (fieldsRefs?.value[name]) {
    fieldsRefs?.value[name]?.focus()
  }
}

const focusFirstField = debounce(() => {
  const field = formFields.value.find((field) => isFieldEditable(field.name as keyof T))
  if (field) {
    focusField(field.name as string)
  }
}, 50)

async function focusSaveButton() {
  await nextTick()
  if (saveButtonRef.value) {
    const el = saveButtonRef?.value?.ref as unknown as HTMLButtonElement
    el?.focus()
  }
}

// only used in form fields slots to handle enter key press
async function handleEnterKeyPress(field: string) {
  onFieldEnterPress(field)
}

// checks if the relation field that was passed to the form is missing the entityState data
function isMissingEntityStateData(field: FormField, data: any): boolean {
  const targetFields = useModelStore().getModel(field?.target || '')?.fields || []
  const hasEntityStateField = targetFields?.find((field) => field.name === 'EntityState')
  if (!hasEntityStateField) return false

  return Array.isArray(data) ? data.some((item) => !item.attributes?.EntityState?.value) : !data?.attributes?.EntityState?.value
}

function buildReturnFields(checkForData = false): ReturnFieldProp {
  const returnFields: ReturnFieldProp = []
  for (const field of formFields.value) {
    if (
      [...OneRelationFieldTypes, ...ManyRelationFieldTypes].includes(field.relationType) &&
      (!checkForData || isMissingEntityStateData(field, initialAllModelData.value[field.name]) || !initialAllModelData.value[field.name])
    ) {
      if (field.remoteOptions?.labelField || field.remoteOptions?.returnFields) {
        const relationReturnFields: ReturnFieldProp = []

        if (Array.isArray(field?.remoteOptions?.returnFields)) {
          relationReturnFields.push(...(field.remoteOptions.returnFields as string[]))
        }
        if (field.remoteOptions?.labelField) {
          relationReturnFields.push(field.remoteOptions.labelField)
        }

        const hasEntityStateField = field.target
          ? useModelStore()
              .getModel(field.target)
              ?.fields?.find((field) => field.name === 'EntityState')
          : false
        if (hasEntityStateField) {
          relationReturnFields.push('EntityState')
        }

        returnFields.push({ [field.name]: relationReturnFields })
      } else if (field.target === 'Address') {
        returnFields.push({ [field.name]: ['address'] })
      } else {
        returnFields.push({ [field.name]: ['documentId'] })
      }
      fieldsLoading.value.set(field.name, true)
    } else if (ScalarFieldTypes.includes(field.type) && (!checkForData || !initialAllModelData.value[field.name])) {
      if (field.type === FieldType.Boolean && has(initialAllModelData.value, field.name)) continue
      returnFields.push(field.name)
      fieldsLoading.value.set(field.name, true)
    }
  }

  return returnFields
}

async function refreshFormData() {
  formDataLoading.value = true

  try {
    if (!props.data.documentId) return
    const returnFields: ReturnFieldProp = buildReturnFields()
    if (hasEntityStateField.value) {
      returnFields.push({ EntityState: ['value'] })
    }
    const query = GetOne(props.modelName, returnFields)
    const { load } = useLazyQuery(query, { id: props.data.documentId }, { fetchPolicy: 'network-only' })
    const response = await load()
    const modelName = camelCase(props.modelName)

    if (response && response[modelName]) {
      const responseData = cloneDeep(response[modelName].data)
      await rebuildForm(responseData?.attributes)
      emits('onRefresh', responseData)
    }
  } catch (error) {
    const err = error as Error
    notification('error', err?.message)
  } finally {
    formDataLoading.value = false
    fieldsLoading.value.clear()
  }
}

function setFieldValue(fieldName: string, value: any) {
  if (!has(formData.value, fieldName)) {
    notification('error', `Field ${fieldName} does not exist in the form data`)
    return
  }
  const relationType = formFields.value.find((field) => field.name === fieldName)?.relationType
  if (relationType && [...OneRelationFieldTypes, ...ManyRelationFieldTypes].includes(relationType)) {
    formData.value[fieldName] = isEmpty(value)
      ? ManyRelationFieldTypes.includes(relationType)
        ? []
        : null
      : Array.isArray(value)
        ? value.map((val: any) => val.documentId)
        : value.documentId

    const newValue = isEmpty(value) ? (ManyRelationFieldTypes.includes(relationType) ? [] : null) : value

    allModelData.value[fieldName] = {
      data: newValue,
    }

    emits('onFieldChange', fieldName, newValue)
    return
  }

  formData.value[fieldName] = value
  emits('onFieldChange', fieldName, value)
}

function getFieldValue(fieldName: string): any {
  return formData.value[fieldName]
}

function getFormData(): Record<string, any> {
  return formData.value
}

function getAllModelData(): any {
  return allModelData.value
}

function setFieldsValues(data: Record<string, any>) {
  formData.value = {
    ...formData.value,
    ...data,
  }
}

function copyToClipboard(field: FormField) {
  if (field.copyMethod) {
    const value = field.copyMethod(formData.value, allModelData.value)
    if (!value) return
    navigator.clipboard.writeText(value)
    notification('success', `Copied ${value} to clipboard`)
    return
  }
  const value = formData.value[field.name]
  if (!value) return
  navigator.clipboard.writeText(value)
  notification('success', `Copied ${value} to clipboard`)
}

const hovered = ref('')

function onFormClose(value: boolean) {
  formVisible.value = value
  onConfirmClose()
}

defineExpose({
  unSavedChanges,
  formData,
  shouldShowEditButton,
  editButtonVisible,
  allModelData,
  isValid,
  resetFields,
  isDisabled,
  closeForm,
  submitForm,
  toggleEdit,
  toggleCancelEdit,
  clearValidate,
  validate,
  rebuildForm,
  closePopovers,
  focusSaveButton,
  refreshFormData,
  getFieldValue,
  getFormData,
  setFieldsValues,
  setFieldValue,
  getAllModelData,
})
</script>

<template>
  <div v-focustrap>
    <FormLayout
      v-model:show-confirm-close-form="showConfirmCloseModal"
      :is-modal="isModal"
      :model-value="formVisible"
      :un-saved-changes="unSavedChanges"
      @update:model-value="onFormClose"
    >
      <CloseFormConfirmationDialog
        v-if="showConfirmCloseModal"
        v-model="showConfirmCloseModal"
        text="You have unsaved changes. Are you sure you want to close?"
        @confirm="onConfirmClose"
      />

      <div :class="{ '  rounded-md overflow-auto': isModal }">
        <!-- Inline | group layout header part -->
        <div
          v-if="isInlineOrGroupLayout"
          :class="[!!slots['headerLeft'] ? 'justify-between' : 'justify-end', 'flex items-center', isCollapsed ? '' : 'mb-4']"
          class="col-span-full"
        >
          <div :class="{ 'pt-6': props.isCollapsed }" class="truncate">
            <slot name="headerLeft" />
          </div>

          <div v-if="!props.isCollapsed" class="flex items-center gap-2">
            <MainButton
              v-if="props.showActionButtons"
              :loading="formDataLoading"
              aria-label="refresh"
              icon="pi pi-refresh"
              severity="secondary"
              size="small"
              @click="refreshFormData"
            />
            <slot name="headerRight" />
            <MainButton
              v-if="shouldShowEditButton && props.showActionButtons"
              :disabled="props.disabled"
              :loading="props.loading"
              class="w-[100px]"
              severity="secondary"
              size="small"
              variant="outlined"
              @click="toggleEdit"
            >
              Edit
            </MainButton>
            <MainButton
              v-if="!editButtonVisible && props.showActionButtons"
              :loading="props.loading"
              class="w-[100px]"
              severity="danger"
              size="small"
              variant="outlined"
              @click="toggleCancelEdit"
            >
              Cancel
            </MainButton>
            <MainButton
              v-if="!editButtonVisible && props.showActionButtons"
              ref="saveButtonRef"
              :disabled="isDisabled || !unSavedChanges"
              :loading="props.loading"
              class="w-[100px]"
              severity="success"
              size="small"
              @click.prevent="submitForm"
            >
              Save
            </MainButton>
          </div>
        </div>

        <div v-if="!isInlineOrGroupLayout" :class="{ 'col-span-full': props?.layout === 'grid' }" class="px-6 truncate">
          <div class="flex items-center max-w-fit mb-5">
            <slot name="title">
              <FormTitle :action="isUpdate ? 'Edit' : 'Create'" :title="props.title || startCase(props.modelName)" />
            </slot>
          </div>
        </div>

        <Transition
          name="collapse"
          @enter="enter($event as HTMLElement)"
          @leave="leave($event as HTMLElement)"
          @before-enter="beforeEnter($event as HTMLElement)"
          @before-leave="beforeLeave($event as HTMLElement)"
          @after-enter="afterEnter($event as HTMLElement)"
        >
          <ElForm
            v-show="!props.isCollapsed"
            ref="formRef"
            :class="[twMerge('min-w-xl', !isInlineOrGroupLayout ? 'pb-3' : '', getFormClasses(props.layout, props?.columns || 0), props.class)]"
            :disabled="isDisabled"
            :label-position="props.labelPosition"
            :label-width="props.labelWidth"
            :model="formData"
            :require-asterisk-position="props.requireAsteriskPosition"
            :rules="formRules"
            :scroll-to-error="true"
            :size="props.size"
            :validate-on-rule-change="false"
            @submit.prevent=""
          >
            <TransitionGroup enter-active-class="animate__animated animate__fadeIn animate__faster" leave-active-class="animate__animated animate__fadeOut animate__faster">
              <template v-if="['group', 'group-modal'].includes(props.layout)">
                <template v-for="(group, index) in groupedFields" :key="index">
                  <template v-if="group.visibleMethod ? group.visibleMethod(formData, allModelData) : true">
                    <div :class="twMerge(group.class, 'bg-white rounded-md shadow-sm relative')">
                      <!-- Group Header -->
                      <div class="flex items-center col-span-full h-fit">
                        <div class="text-lg text-slate-600 mb-2 text-pretty">
                          {{ group.label }}
                        </div>
                      </div>

                      <!-- Group Fields -->
                      <template v-for="field in group.fields" :key="field.name">
                        <div
                          v-if="field.visibleMethod ? field.visibleMethod(formData, allModelData) : true"
                          v-loading="fieldsLoading.has(field.name)"
                          :class="[getFieldClasses(props.layout, props?.columns || 0), 'h-fit']"
                        >
                          <AddressComponent
                            v-if="field.target === 'Address'"
                            ref="addressRefs"
                            :custom-class="field.addressOptions?.customClass"
                            :disabled="isDisabled || (typeof field.disabled === 'function' ? field.disabled(formData, allModelData) : field?.disabled)"
                            :disabled-message="field.addressOptions?.disabledMessage"
                            :model-value="allModelData[field.name] || {}"
                            @update:model-value="onFieldChange(field, $event)"
                            @on-enter-key-press="jumpToNextField(field.name)"
                          />
                          <DynamicField
                            v-else-if="field.target !== 'Address' && !fieldSlotNames.includes(field.name)"
                            :ref="(el: any) => (fieldsRefs[field.name] = el)"
                            v-model="formData[field.name]"
                            :all-model-data="allModelData"
                            :disabled="isDisabled || (typeof field.disabled === 'function' ? field.disabled(formData, allModelData) : field?.disabled) || false"
                            :field="field"
                            :form-data="formData"
                            :model-name="props.modelName"
                            :relation-data="[...OneRelationFieldTypes, ...ManyRelationFieldTypes].includes(field.relationType) ? allModelData[field.name] : undefined"
                            @on-change="onFieldChange(field, $event)"
                            @on-enter-key-press="onFieldEnterPress"
                            @on-esc-key-press="closeForm()"
                          />
                          <ElFormItem v-else :prop="field.name">
                            <template #label>
                              <div class="flex items-center" @click="copyToClipboard(field)" @mouseenter="hovered = field.name" @mouseleave="hovered = ''">
                                <!--                                <div v-if="field.icon" class="w-5">-->
                                <!--                                  <AsyncIcon v-if="field.icon" :name="field.icon" class="size-4 text-slate-500" />-->
                                <!--                                </div>-->
                                <div class="truncate flex-shrink-0">
                                  {{ field.label ?? upperFirst(field.name) }}
                                </div>
                                <span v-if="field.showRequired" class="ml-1 danger-color">*</span>
                              </div>
                            </template>
                            <slot
                              :all-model-data="allModelData"
                              :disabled="isDisabled || (typeof field.disabled === 'function' ? field.disabled(formData, allModelData) : field?.disabled) || false"
                              :field-name="field.name"
                              :fields-refs="fieldsRefs"
                              :form-data="formData"
                              :handle-enter="handleEnterKeyPress"
                              :name="'field-' + field.name"
                            />
                            <DocumentDuplicateIcon v-if="hovered === field.name && isDisabled" class="size-5 main-icon-color absolute right-0" />
                          </ElFormItem>
                        </div>
                      </template>
                    </div>
                  </template>
                </template>
              </template>
              <template v-else>
                <template v-if="selectExistingSlot">
                  <ElFormItem v-if="selectExistingSlot.visibleMethod ? selectExistingSlot.visibleMethod(formData, allModelData) : true" class="!mb-2" prop="selectExisting">
                    <template #label>
                      <div class="flex items-center">
                        <!--                        <div class="w-5">-->
                        <!--                          <AsyncIcon :name="selectExistingSlot.icon ? selectExistingSlot.icon : 'TableCellsIcon'" class="size-4 text-slate-500" />-->
                        <!--                        </div>-->
                        <div class="truncate flex-shrink-0">
                          {{ selectExistingSlot.label ?? 'Select Existing' }}
                        </div>
                      </div>
                    </template>
                    <slot
                      :all-model-data="allModelData as T"
                      :disabled="
                        isDisabled ||
                        (typeof selectExistingSlot.disabled === 'function' ? selectExistingSlot.disabled(formData, allModelData) : selectExistingSlot?.disabled) ||
                        false
                      "
                      :field-name="selectExistingSlot.name"
                      :fields-refs="fieldsRefs"
                      :form-data="formData"
                      :handle-enter="handleEnterKeyPress"
                      name="field-selectExisting"
                    />
                  </ElFormItem>
                </template>
                <template v-for="field in formFields" :key="field.name">
                  <div
                    v-if="field.visibleMethod ? field.visibleMethod(formData, allModelData) : true"
                    v-loading="fieldsLoading.has(field.name)"
                    :class="[getFieldClasses(props.layout, props?.columns || 0)]"
                  >
                    <!-- we render address here so it will be an easy access to the hasAddressChanges prop -->
                    <AddressComponent
                      v-if="field.target === 'Address'"
                      ref="addressRefs"
                      :custom-class="field.addressOptions?.customClass"
                      :disabled="isDisabled || (typeof field.disabled === 'function' ? field.disabled(formData, allModelData) : field?.disabled) || false"
                      :disabled-message="field.addressOptions?.disabledMessage"
                      :model-value="allModelData[field.name] || {}"
                      @update:model-value="onFieldChange(field, $event)"
                      @on-enter-key-press="jumpToNextField(field.name)"
                    />

                    <ElFormItem v-else-if="fieldSlotNames.includes(field.name)" :prop="field.name">
                      <template #label>
                        <div class="flex items-center" @click="copyToClipboard(field)" @mouseenter="hovered = field.name" @mouseleave="hovered = ''">
                          <!--                          <div v-if="field.icon" class="w-5">-->
                          <!--                            <AsyncIcon v-if="field.icon" :name="field.icon" class="size-4 text-slate-500" />-->
                          <!--                          </div>-->
                          <div class="truncate flex-shrink-0">
                            {{ field.label ?? startCase(field.name) }}
                          </div>
                          <span v-if="field.showRequired" class="ml-1 danger-color">*</span>
                        </div>
                      </template>

                      <template v-if="fieldSlotNames.includes(field.name)">
                        <slot
                          :all-model-data="allModelData"
                          :disabled="isDisabled || (typeof field.disabled === 'function' ? field.disabled(formData, allModelData) : field?.disabled) || false"
                          :field-name="field.name"
                          :fields-refs="fieldsRefs"
                          :form-data="formData"
                          :handle-enter="handleEnterKeyPress"
                          :name="'field-' + field.name"
                        />
                        <DocumentDuplicateIcon v-if="hovered && disabled" class="size-5 main-icon-color absolute right-0" />
                      </template>
                    </ElFormItem>
                    <DynamicField
                      v-else-if="field.target !== 'Address'"
                      :ref="(el: any) => (fieldsRefs[field.name] = el)"
                      v-model="formData[field.name]"
                      :all-model-data="allModelData"
                      :disabled="isDisabled || (typeof field.disabled === 'function' ? field.disabled(formData, allModelData) : field?.disabled) || false"
                      :field="field"
                      :form-data="formData"
                      :model-name="props.modelName"
                      :relation-data="[...OneRelationFieldTypes, ...ManyRelationFieldTypes].includes(field.relationType) ? allModelData[field.name] : undefined"
                      @on-change="onFieldChange(field, $event)"
                      @on-enter-key-press="onFieldEnterPress"
                      @on-esc-key-press="closeForm()"
                    />
                  </div>
                </template>

                <!-- other formFields from slosts  -->
                <template v-for="field in extraFieldsSlots.filter((slot) => slot.name !== 'selectExisting')" :key="field.name">
                  <div v-if="field.visibleMethod ? field.visibleMethod(formData, allModelData) : true" :class="[getFieldClasses(props.layout, props?.columns || 0)]">
                    <ElFormItem :prop="field.name">
                      <template #label>
                        <div class="flex items-center">
                          <!--                          <div v-if="field.icon" class="w-5">-->
                          <!--                            <AsyncIcon v-if="field.icon" :name="field.icon" class="size-4 text-slate-500" />-->
                          <!--                          </div>-->
                          <div>{{ field.label ?? upperFirst(field.name) }}</div>
                          <span v-if="field.showRequired" class="ml-1 danger-color">*</span>
                        </div>
                      </template>
                      <slot
                        :all-model-data="allModelData"
                        :disabled="isDisabled || (typeof field.disabled === 'function' ? field.disabled(formData, allModelData) : field?.disabled) || false"
                        :field-name="field.name"
                        :fields-refs="fieldsRefs"
                        :form-data="formData"
                        :handle-enter="handleEnterKeyPress"
                        :name="'field-' + field.name"
                      />
                    </ElFormItem>
                  </div>
                </template>
              </template>
            </TransitionGroup>
          </ElForm>
        </Transition>
        <div v-if="!isInlineOrGroupLayout" :class="{ ' px-10 flex justify-end gap-2': !!slots['nextAction'] }">
          <slot name="nextAction" />
        </div>
        <div
          v-if="!isInlineOrGroupLayout && !props.isCollapsed"
          :class="[!!slots['deleteButton'] || !!slots['confirmationCheckbox'] ? 'justify-between  px-10' : 'justify-end', props.layout === 'grid' ? 'col-span-full' : '']"
          class="flex items-center px-6 mb-2"
        >
          <slot name="deleteButton" />
          <slot name="confirmationCheckbox" />
          <MainButton
            v-if="props.showActionButtons"
            ref="saveButtonRef"
            :disabled="isDisabled || !unSavedChanges"
            :loading="props.loading || validatingData"
            size="small"
            @click.prevent="submitForm"
          >
            Save
          </MainButton>
        </div>
        <slot v-if="!props.isCollapsed" name="footer" />
      </div>
    </FormLayout>
  </div>
</template>

<style lang="scss" scoped>
@use '@/assets/mixins' as *;

@include custom-form-style();
@include custom-disabled-form-style();
@include custom-scrollbar();

.collapse-enter-active,
.collapse-leave-active {
  transition:
    height 0.3s ease,
    opacity 0.3s ease;
  will-change: height, opacity;
}

.collapse-enter,
.collapse-leave-to {
  height: 0;
  opacity: 0;
  overflow: hidden;
}
</style>
