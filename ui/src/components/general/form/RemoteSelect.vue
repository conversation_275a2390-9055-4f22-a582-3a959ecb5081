<script lang="ts" setup>
import { computed, type Ref, ref } from 'vue'
// Lodash
import { cloneDeep, debounce, isEmpty, isNaN, uniqBy } from 'lodash-es'

// Store
import { useModelStore } from '@/stores/model.store'
// Utils
import { buildConnectionReturnFields, type ReturnFieldProp } from '@/utils/queryMethods'

// Element Plus
import { ElOption, ElPopover, ElSelect } from 'element-plus'
import Skeleton from 'primevue/skeleton'

// GraphQL
import gql from 'graphql-tag'
import { type Pagination, type PaginationArg } from '@/gql/graphql'
import { useLazyQuery } from '@vue/apollo-composable'
import type { DocumentNode } from 'graphql/language'
import * as gqlBuilder from 'gql-query-builder'
import type { RemoteSelectProps } from '@/utils/types.ts'
import { notification } from '@/utils/notifications.ts'
import { FieldRelationType, FieldType, NumericFieldTypes } from '@/utils/constants/fieldTypes.ts'

const modelStore = useModelStore()

type PaginationType = Omit<Pagination, 'pageCount'>

const props = withDefaults(defineProps<RemoteSelectProps>(), {
  labelField: undefined,
  filters: undefined,
  placeholder: undefined,
  valueKey: undefined,
  labelFunction: undefined,
  uniqueByKey: undefined,
  suffixIcon: undefined,
  returnFields: undefined,
  showSetFavorites: undefined,
  skipEntityStateFilter: false,
  fitInputWidth: true,
  sections: () => [],
  placement: undefined,
})

const model = modelStore.getModel(props.modelName)
if (!model) throw new Error('model not found')
const operation = `${model.pluralName}_connection`

const defaultPagination: PaginationType = {
  page: 1,
  pageSize: 25,
  total: 0,
}

const emits = defineEmits(['error', 'update:modelValue', 'clear', 'onEnterKeyPress', 'onEscKeyPress', 'change'])

const data: Ref<any[]> = ref([])

function remoteSearchQuery(returnFields?: ReturnFieldProp): DocumentNode {
  const options = {
    operation: operation,
    variables: {
      filters: { type: `${props.modelName}FiltersInput` },
      pagination: { type: 'PaginationArg' },
      sort: { type: '[String]' },
    },
    fields: [buildConnectionReturnFields(model!, returnFields)],
  }

  return gql`
    ${gqlBuilder.query(options).query}
  `
}

const options = computed(() => {
  let values = JSON.parse(JSON.stringify(data.value))

  // Add the modelValue to the options if it's not already included,
  // We use getValue to get the value of the item in case we have valueKey
  if (
    props.multiple &&
    props.modelValue?.length > 0 &&
    !data.value.some((item, index) => props.modelValue.map((val: any) => val.documentId).includes(getValue(item, index).documentId))
  ) {
    values = [...data.value, ...props.modelValue]
  }

  // Add the modelValue to the options if it's not already included,
  // We use getValue to get the value of the item in case we have valueKey
  if (!props.multiple && props.modelValue?.documentId && data.value.findIndex((item, index) => getValue(item, index).documentId === props.modelValue.documentId) === -1) {
    values = [...data.value, props.modelValue]
  }

  return props.uniqueByKey ? uniqBy(values, props.uniqueByKey) : values
})

const selectedOptions = computed<null | any[] | any>(() => {
  if (!options.value || !props.modelValue) return null
  if (props.multiple) {
    return options.value.filter((item: any) => props.modelValue.includes(item.documentId) || props.modelValue.find((val: any) => val.documentId === item.documentId))
  } else {
    return options.value.find((item: any) => props.modelValue === item.documentId || props.modelValue.documentId === item.documentId) || null
  }
})

// TODO no need to be computed, should be a function
const query = computed<DocumentNode>(() => {
  const updatedReturnFields = props.returnFields ? [...props.returnFields] : []

  if (props.labelField) {
    if (!updatedReturnFields.includes(props.labelField)) {
      updatedReturnFields.push(props.labelField)
    }
  }

  return remoteSearchQuery(updatedReturnFields)
})

const FETCH_DATA = query.value
const { load, refetch, loading } = useLazyQuery(FETCH_DATA, {}, { fetchPolicy: 'network-only' })

const { load: loadSections, refetch: refetchSections, loading: loadingSections } = useLazyQuery(FETCH_DATA, {}, { fetchPolicy: 'network-only' })

const sectionsData: Ref<Map<string, any[]>> = ref(new Map())

const pagination = ref<PaginationType>({ ...defaultPagination })

const searchValue = ref<string | null>(null)

const noDataMessageVisible = ref<boolean>(false)

const elSelectRef = ref<InstanceType<typeof ElSelect>>()

const isLoading = computed(() => loading.value || loadingSections.value)

const sectionsDataIds = computed<string[]>(() =>
  props.sections.length ? props.sections.map((section) => sectionsData.value.get(section.name)?.map((item: any) => item.documentId)).flat() : [],
)

async function fetchDataWithVariables(operationName: string, filters: any, paginationArgs: PaginationArg, sort: string[]) {
  const variables = filters
    ? {
        filters,
        pagination: paginationArgs,
        sort,
      }
    : { pagination: paginationArgs, filters: {}, sort }
  const response = (await load(FETCH_DATA, variables)) || (await refetch(variables))
  console.log('response', response)
  const total = response[operationName]?.meta?.pagination?.total || response?.data?.[operationName]?.meta?.pagination?.total
  const responseData = cloneDeep(response[operationName]?.nodes || response?.data?.[operationName]?.nodes || [])
  return { total, responseData }
}

async function handleResponseData(responseData: any, loadMore: boolean, searchFilters: any) {
  if (loadMore) {
    data.value = [...data.value, ...responseData]
  } else {
    data.value = responseData
  }

  // Show no data message when the dropdown opens with no data
  if (!loadMore && isEmpty(searchFilters) && data.value.length === 0 && sectionsDataIds.value.length === 0 && options.value.length === 0) {
    noDataMessageVisible.value = true
  }
}

async function remoteSearch(visible: boolean, loadMore = false, searchFilters = {}) {
  noDataMessageVisible.value = false
  if (!visible) {
    pagination.value.page = 1
    return
  }
  const model = modelStore.getModel(props.modelName)
  if (!model) return
  if (!loadMore) data.value = []
  try {
    const operationName = operation
    let filters = cloneDeep(props.filters)

    const paginationArgs: PaginationArg = {
      page: pagination.value.page,
      pageSize: pagination.value.pageSize,
    }

    if (!isEmpty(searchFilters)) {
      filters = !filters ? searchFilters : { ...filters, ...searchFilters }
    }

    // Load sections data
    if (props.sections.length) {
      for (const section of props.sections) {
        const response = (await loadSections(FETCH_DATA, { filters: section.filters })) || (await refetchSections({ filters: section.filters }))
        const data = response[operationName]?.nodes || response?.data?.[operationName]?.nodes || []
        sectionsData.value.set(section.name, data)
      }
    }

    // Exclude custom sections and favorites from the search results
    if (sectionsDataIds.value.length) {
      const excludedIds = sectionsDataIds.value

      if (filters['documentId']?.notIn) {
        filters['documentId'].notIn.push(...excludedIds)
      } else if (filters['documentId']) {
        filters['documentId'] = { ...filters['documentId'], notIn: excludedIds }
      } else {
        filters['documentId'] = { notIn: excludedIds }
      }
    }
    // First data fetch
    let initialSort = ['createdAt:desc']

    // override the default sort with the one passed in props
    if (props.sorter) {
      initialSort = [props.sorter]
    }

    const { total, responseData } = await fetchDataWithVariables(operationName, filters, paginationArgs, initialSort)

    pagination.value.total = total

    // Process the response data
    await handleResponseData(responseData, loadMore, searchFilters)
  } catch (e) {
    emits('error', e)
    notification('error', e)
  }
}

function getValue(item: any, index: number) {
  if (!props.valueKey) return item ?? index
  let val: any
  for (const key of props.valueKey.split('.')) {
    val = val ? val[key] : item[key]
  }
  // if valueKey is set, the modelValue is an object of that type
  if (props.modelValue?.documentId && !val) {
    return item
  }
  return val
}

function getLabel(item: any) {
  if (props.labelFunction) return props.labelFunction(item)
  // getFormattedAddress(item?.attributes?.[props.labelField])

  return props?.labelField === 'address' ? 'address not found' : props.labelField ? item?.[props.labelField] : item?.documentId
}

function onClear() {
  emits('update:modelValue', null)
  emits('clear')
}

function getSearchFilters(query: string | null): { or: any[] } {
  const filters: any[] = []

  const modelFields = modelStore.getModel(props.modelName)?.fields
  if (!modelFields) return { or: filters }

  const isQueryNonNumeric = isNaN(Number(query))

  const searchableFields = modelFields
    .filter((field) => field.relationType === FieldRelationType.Scalar && field.name !== 'documentId')
    .filter((field) => [FieldType.Text, FieldType.String, ...NumericFieldTypes].includes(field.type))

  searchableFields.forEach((field) => {
    const isFieldNumeric = NumericFieldTypes.includes(field.type)

    if (!isFieldNumeric) {
      filters.push({
        [field.name]: {
          containsi: query,
        },
      })
      return
    }

    if (isFieldNumeric && isQueryNonNumeric) return

    filters.push({
      [field.name]: {
        eq: Number(query),
      },
    })
  })

  return { or: filters }
}

function onSearch(query: string) {
  if (!query && !searchValue.value) return
  if (!query && searchValue.value) {
    resetSearch()
    return
  }

  searchValue.value = query
  const filters = getSearchFilters(query)

  pagination.value.page = 1
  remoteSearch(true, false, filters)
}

const debounceSearch = debounce(onSearch, 200)

function onLoadMore() {
  const filters = searchValue.value ? getSearchFilters(searchValue.value) : null

  pagination.value.page += 1
  remoteSearch(true, true, filters || {})
}

function unSelectItem(documentId: string) {
  const selectedItems = cloneDeep([...props.modelValue]).filter((item) => item.documentId !== documentId)
  emits('update:modelValue', selectedItems)
}

function selectItem(item: any) {
  if (props.multiple) {
    const selectedItems = cloneDeep([...props.modelValue])
    const isAlreadySelected = selectedItems.some((val: any) => val.documentId === item.documentId)
    if (isAlreadySelected) {
      const index = selectedItems.findIndex((val: any) => val.documentId === item.documentId)
      selectedItems.splice(index, 1)
    } else {
      selectedItems.push(item)
    }
    emits('update:modelValue', selectedItems)
  } else {
    emits('update:modelValue', cloneDeep(item))
    elSelectRef?.value?.toggleMenu()
  }
}

function resetSearch() {
  pagination.value.page = 1
  searchValue.value = null
  remoteSearch(true, false)
}

function shouldBeHidden(item: any) {
  return sectionsDataIds.value.includes(item.documentId)
}

function isSelected(item: any) {
  return props.multiple ? props.modelValue.map((val: any) => val.documentId).includes(item.documentId) : props.modelValue?.documentId === item.documentId
}

defineExpose({
  focus: () => {
    elSelectRef?.value?.focus()
  },
})
</script>
<template>
  <div>
    <ElPopover :visible="noDataMessageVisible" class="w-full flex items-center justify-center" placement="bottom" trigger="click" width="auto">
      <span class="text-xs w-full items-center justify-center flex text-muted">No Data</span>
      <template #reference>
        <ElSelect
          ref="elSelectRef"
          :clearable="props.clearable"
          :collapse-tags="multiple"
          :default-first-option="true"
          :disabled="disabled"
          :fit-input-width="fitInputWidth"
          :loading="isLoading"
          :model-value="modelValue"
          :multiple="multiple"
          :placeholder="placeholder ?? 'Select ' + props.modelName"
          :placement="props.placement"
          :remote-method="debounceSearch"
          :suffix-icon="suffixIcon"
          filterable
          no-data-text="No data"
          no-match-text="No matching data"
          remote
          remote-show-suffix
          required
          size="default"
          value-key="documentId"
          @change="emits('change', $event)"
          @clear="onClear"
          @visible-change="remoteSearch"
          @update:model-value="emits('update:modelValue', $event)"
          @keyup.enter="emits('onEnterKeyPress')"
          @keyup.esc="emits('onEscKeyPress')"
        >
          <template #header>
            <div class="flex flex-col gap-1">
              <template v-if="sections.length">
                <div v-for="section in props.sections" :key="section.name">
                  <span class="pl-2 mb-1">{{ section.name }}</span>
                  <div
                    v-for="item in sectionsData.get(section.name) || []"
                    :key="item.documentId"
                    :class="{ 'main-color font-bold': isSelected(item) }"
                    class="flex items-center hover:bg-gray-100/90 p-1.5 cursor-pointer pl-2"
                    @click="selectItem(item)"
                  >
                    <slot :data="item">
                      <p class="truncate max-w-sm" role="option">
                        {{ getLabel(item) }}
                      </p>
                    </slot>
                  </div>
                </div>
              </template>

              <span class="text-muted pl-2 mb-1">Selected</span>
              <template v-if="props.multiple && selectedOptions">
                <div
                  v-for="item in selectedOptions"
                  :key="item.documentId"
                  class="blue-color hover:bg-gray-100/90 p-1.5 cursor-pointer pl-2"
                  @click="unSelectItem(item.documentId)"
                >
                  <slot :data="item">
                    <p :content="getLabel(item)" class="max-w-sm truncate text-blue-500" role="option">
                      {{ getLabel(item) }}
                    </p>
                  </slot>
                </div>
              </template>
              <div v-else-if="selectedOptions" class="w-full px-2">
                <slot :data="selectedOptions">
                  <p v-if="selectedOptions" :content="getLabel(selectedOptions)" class="max-w-sm truncate text-blue-500">
                    {{ getLabel(selectedOptions) }}
                  </p>
                </slot>
              </div>
            </div>
          </template>

          <span class="text-muted py-1 pl-5">All</span>
          <ElOption
            v-for="(option, index) in options"
            :key="option.documentId!"
            v-ripple
            :class="{ hidden: shouldBeHidden(option) }"
            :disabled="optionDisabled?.(option)"
            :label="getLabel(option)"
            :value="getValue(option, index)"
            class="group"
          >
            <div class="flex items-center justify-between h-full min-h-sm">
              <slot :data="option">
                <p class="truncate max-w-sm" role="option">
                  {{ getLabel(option) }}
                </p>
              </slot>
            </div>
          </ElOption>

          <template v-if="data.length < pagination.total" #footer>
            <MainButton :loading="isLoading" @click="onLoadMore" />
          </template>
          <template #loading>
            <div v-for="index in 3" :key="index" class="col-span-1 px-2 mb-2">
              <Skeleton class="col-span-1 p-4 rounded-md" height="0.4rem" />
            </div>
          </template>
        </ElSelect>
      </template>
    </ElPopover>
  </div>
</template>
