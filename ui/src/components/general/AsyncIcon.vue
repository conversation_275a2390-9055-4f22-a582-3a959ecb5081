<script lang="ts" setup>
import { type Component, defineAsyncComponent } from 'vue'
import solidIcons, { ArrowPathIcon, ExclamationCircleIcon } from '@heroicons/vue/24/solid'

type SolidIconName = keyof typeof solidIcons

export type IconName = SolidIconName | undefined
export type IconType = 'solid' | 'outline'

interface Props {
  name?: IconName
  type?: IconType
}

const props = withDefaults(defineProps<Props>(), {
  name: 'ExclamationCircleIcon',
  type: 'solid',
})

const IconAsyncComponent = defineAsyncComponent({
  loader: async () => {
    try {
      let iconComponent: Component | undefined

      if (props.type === 'outline') {
        const module = await import('@heroicons/vue/24/outline')
        iconComponent = module[props.name] as Component
      } else {
        const module = await import('@heroicons/vue/24/solid')
        iconComponent = module[props.name] as Component
      }

      return iconComponent
    } catch (error) {
      const err = error as { message: string }
      throw new Error(err.message)
    }
  },
  loadingComponent: ArrowPathIcon,
  delay: 200,
  errorComponent: ExclamationCircleIcon,
  timeout: 4000,
})
</script>

<template>
  <IconAsyncComponent />
</template>
