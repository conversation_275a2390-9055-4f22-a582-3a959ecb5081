<script lang="ts" setup>
import type { Mutation, MutationCreatePatientArgs, MutationUpdatePatientArgs, Patient, PatientInput } from '@/gql/graphql.ts'
import type { FieldsConfig, GroupConfig } from '@/components/general/form/types.ts'
import DynamicForm from '@/components/general/form/DynamicForm.vue'
import { notification } from '@/utils/notifications.ts'
import { useMutation } from '@vue/apollo-composable'
import { Create, Update } from '@/utils/queryMethods.ts'
import { HISTORY_FIELDS } from '@/utils/constants/grid.ts'

const fullName: Array<keyof PatientInput> = ['namePrefix', 'firstName', 'lastName', 'nameSuffix']
const personalDetails: Array<keyof PatientInput> = ['dob', 'Sex', 'Race']
const patientIdentifiers: Array<keyof PatientInput> = ['pid', 'ssn', 'Group']
const contactInformation: Array<keyof PatientInput> = ['contact', 'phone']

const formFields: Array<keyof PatientInput> = [...fullName, ...personalDetails, ...patientIdentifiers, ...contactInformation]

const groupsConfig: GroupConfig[] = [
  {
    label: 'Full Name',
    fields: fullName,
    class: 'col-span-full lg:col-span-6 grid grid-cols-1 self-start  !shadow-none',
  },
  {
    label: 'Personal Details',
    fields: personalDetails,
    class: 'col-span-full lg:col-span-6 grid grid-cols-1 self-start  !shadow-none',
  },
  {
    label: 'Patient Identifiers',
    fields: patientIdentifiers,
    class: 'col-span-full lg:col-span-6 grid grid-cols-1 self-start  !shadow-none',
  },
  {
    label: 'Contact Information',
    fields: contactInformation,
    class: 'col-span-full lg:col-span-6 grid grid-cols-1 self-start  !shadow-none',
  },
]

const fieldsConfig: FieldsConfig<Patient> = {
  namePrefix: {
    label: 'Prefix',
    placeholder: 'Enter prefix (e.g., Mr., Mrs., Dr.)',
  },
  nameSuffix: {
    label: 'Suffix',
    placeholder: 'Enter suffix (e.g., Jr., Sr., III)',
  },
  pid: {
    label: 'Patient ID',
    icon: 'IdentificationIcon',
  },
  dob: {
    label: 'Date of Birth',
  },
  ssn: {
    label: 'Social Security Number',
    rules: [
      {
        pattern: /^\d{3}-\d{2}-\d{4}$/,
        message: 'SSN must be in the format XXX-XX-XXXX',
      },
    ],
  },
  Group: {
    remoteOptions: {
      labelField: 'name',
    },
  },
  Race: {
    remoteOptions: {
      labelField: 'value',
      sorter: 'value:asc',
      filters: {
        Category: {
          name: {
            eq: 'Race',
          },
        },
      },
    },
  },
  Sex: {
    remoteOptions: {
      sorter: 'value:asc',
      labelField: 'value',
      filters: {
        Category: {
          name: {
            eq: 'Sex',
          },
        },
      },
    },
  },
}

interface Props {
  rowData?: Patient
}

interface Emits {
  (event: 'create', value: Patient): void

  (event: 'update', value: Patient): void

  (event: 'close'): void
}

const props = withDefaults(defineProps<Props>(), {
  rowData: undefined,
})

const emits = defineEmits<Emits>()

const { mutate: create, loading: loadingCreate } = useMutation<Mutation, MutationCreatePatientArgs>(Create('Patient', ['documentId']))
const { mutate: update, loading: loadingUpdate } = useMutation<Mutation, MutationUpdatePatientArgs>(Update('Patient', [...formFields, ...HISTORY_FIELDS]))

async function onSubmit(data: PatientInput) {
  try {
    if (props.rowData?.documentId) {
      const response = await update({
        documentId: props.rowData.documentId,
        data,
      })

      if (response?.data?.updatePatient) emits('update', response?.data?.updatePatient)
    } else {
      const response = await create({
        data,
      })
      if (response?.data?.createPatient) emits('create', response?.data?.createPatient)
    }

    notification('success', props.rowData?.documentId ? 'Patient updated successfully' : 'Patient created successfully')
  } catch (error) {
    notification('error', (error as Error).message)
  }
}
</script>

<template>
  <DynamicForm
    :data="props.rowData ?? {}"
    :fields="formFields"
    :fields-config="fieldsConfig"
    :groups-config="groupsConfig"
    :loading="loadingCreate || loadingUpdate"
    is-modal
    label-width="200px"
    layout="group-modal"
    model-name="Patient"
    @close="emits('close')"
    @on-submit="onSubmit"
  />
</template>

<style lang="scss" scoped></style>
