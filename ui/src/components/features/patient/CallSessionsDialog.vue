<script lang="ts" setup>
import { ref, watch } from 'vue'
import Dialog from 'primevue/dialog'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import Badge from 'primevue/badge'
import Popover from 'primevue/popover'
import { useLazyQuery } from '@vue/apollo-composable'
import type { ReturnFieldProp } from '@/utils/queryMethods.ts'
import { GetMany } from '@/utils/queryMethods.ts'
import { notification } from '@/utils/notifications.ts'
import { formatDate } from '@/utils/dateMethods.ts'
import { formatPatientName } from '@/utils/formatters.ts'
import type { CallSession, Patient, Query, QueryCallSessionsArgs } from '@/gql/graphql.ts'
import { cloneDeep } from 'lodash-es'
import { ArrowPathIcon, GlobeAltIcon, InformationCircleIcon, KeyIcon, PhoneIcon } from '@heroicons/vue/24/outline'

interface Props {
  visible: boolean
  patient?: Patient
}

interface Emits {
  (e: 'update:visible', value: boolean): void

  (e: 'close'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const callSessions = ref<CallSession[]>([])
const twilioPopovers = ref<{ [key: string]: any }>({})

const callSessionsFields: ReturnFieldProp = ['documentId', 'callSid', 'callerNumber', 'attempts', 'language', 'createdAt', 'updatedAt', 'twilioBody']

const {
  load: loadCallSessions,
  refetch: refetchCallSessions,
  onResult,
  onError,
  loading,
} = useLazyQuery<Query, QueryCallSessionsArgs>(GetMany('CallSession', callSessionsFields), () => ({
  filters: {
    Patient: {
      documentId: {
        eq: props.patient?.documentId,
      },
    },
  },
  sort: ['createdAt:desc'],
}))

onResult((result) => {
  if (result.data?.callSessions_connection?.nodes) {
    callSessions.value = cloneDeep(result.data.callSessions_connection.nodes)
  } else {
    callSessions.value = []
  }
})

onError((error) => {
  console.error('Error fetching call sessions:', error)
  notification('error', 'Failed to load call sessions')
})

watch(
  () => props.visible,
  async (newVisible) => {
    if (newVisible && props.patient) {
      await fetchCallSessions()
    }
  },
)

async function fetchCallSessions() {
  if (!props.patient) return

  console.log('Fetching call sessions for patient:', props.patient.documentId)
  try {
    ;(await loadCallSessions()) || (await refetchCallSessions())
  } catch (error) {
    console.error('Error loading call sessions:', error)
    notification('error', 'Failed to load call sessions')
  }
}

function closeDialog() {
  emit('update:visible', false)
  callSessions.value = []
}

function getCallOutcome(session: CallSession): { label: string; severity: string } {
  if (session.attempts && session.attempts >= 3) {
    return { label: 'Failed - Max Attempts', severity: 'danger' }
  }

  return { label: 'Successful', severity: 'success' }
}

function formatLanguage(language: string | null | undefined): string {
  if (!language) return 'English'
  return language === 'es' ? 'Spanish' : 'English'
}

function formatTwilioBody(twilioBody: any): string {
  if (!twilioBody) return 'No data'
  try {
    return JSON.stringify(twilioBody, null, 2)
  } catch (error) {
    return 'Invalid JSON data'
  }
}

function toggleTwilioPopover(event: Event, sessionId: string) {
  const popover = twilioPopovers.value[sessionId]
  if (popover) {
    popover.toggle(event)
  }
}

function setTwilioPopoverRef(el: any, sessionId: string) {
  if (el) {
    twilioPopovers.value[sessionId] = el
  }
}
</script>

<template>
  <Dialog :style="{ width: '80vw' }" :visible="props.visible" modal @update:visible="closeDialog" @after-hide="emit('close')">
    <template #header>
      <div class="flex items-center gap-2">
        <PhoneIcon class="size-4 text-slate-600" />
        <span class="text-lg font-semibold">Call Sessions</span>
        <span v-if="props.patient" class="text-sm text-slate-600"> - {{ formatPatientName(props.patient) }} </span>
      </div>
    </template>

    <div v-if="loading" class="flex justify-center items-center py-8">
      <ArrowPathIcon class="size-4 text-slate-600 animate-spin" />
      <span class="ml-2">Loading call sessions...</span>
    </div>

    <div v-else-if="callSessions.length === 0" class="text-center py-8">
      <PhoneIcon class="size-16 text-slate-400 mb-4 mx-auto" />
      <p class="text-slate-600">No call sessions found for this patient.</p>
    </div>

    <div v-else>
      <DataTable :value="callSessions" class="p-datatable-sm" responsive-layout="scroll">
        <Column field="createdAt" header="Call Date/Time" sortable>
          <template #body="{ data }">
            <div class="flex flex-col">
              <span>{{ formatDate(data.createdAt) }}</span>
              <span class="text-sm">{{ new Date(data.createdAt).toLocaleTimeString() }}</span>
            </div>
          </template>
        </Column>

        <Column field="callerNumber" header="Phone Number" sortable>
          <template #body="{ data }">
            <div class="flex items-center gap-2">
              <PhoneIcon class="size-4 text-slate-600" />
              <span>{{ data.callerNumber || 'N/A' }}</span>
            </div>
          </template>
        </Column>

        <Column field="language" header="Language" sortable>
          <template #body="{ data }">
            <div class="flex items-center gap-2">
              <GlobeAltIcon class="size-4 text-slate-600" />
              <span>{{ formatLanguage(data.language) }}</span>
            </div>
          </template>
        </Column>

        <Column field="attempts" header="PIN Attempts" sortable>
          <template #body="{ data }">
            <div class="flex items-center gap-2">
              <KeyIcon class="size-4 text-slate-600" />
              <span>{{ data.attempts || 0 }}</span>
            </div>
          </template>
        </Column>

        <Column field="outcome" header="Call Outcome">
          <template #body="{ data }">
            <Badge :severity="getCallOutcome(data).severity" :value="getCallOutcome(data).label" />
          </template>
        </Column>

        <Column field="callSid" header="Call ID">
          <template #body="{ data }">
            <span class="text-xs font-mono text-slate-600">
              {{ data.callSid }}
            </span>
          </template>
        </Column>

        <Column field="twilioBody" header="Twilio Data">
          <template #body="{ data }">
            <button v-if="data.twilioBody" aria-label="View Twilio data" class="p-1 hover:bg-slate-100 rounded" @click="toggleTwilioPopover($event, data.documentId)">
              <InformationCircleIcon class="size-4 text-slate-600" />
            </button>
            <span v-else class="text-slate-400 text-sm">No data</span>

            <Popover :ref="(el) => setTwilioPopoverRef(el, data.documentId)">
              <div class="p-4 max-w-lg">
                <h6 class="font-semibold mb-2 text-slate-800">Twilio Request Data</h6>
                <pre class="text-xs bg-slate-50 p-3 rounded overflow-auto max-h-80 whitespace-pre-wrap">{{ formatTwilioBody(data.twilioBody) }}</pre>
              </div>
            </Popover>
          </template>
        </Column>
      </DataTable>
    </div>

    <template #footer>
      <div class="flex justify-end">
        <MainButton label="Close" severity="secondary" variant="outlined" @click="closeDialog" />
      </div>
    </template>
  </Dialog>
</template>

<style lang="scss" scoped></style>
