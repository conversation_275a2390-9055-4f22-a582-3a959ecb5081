<script lang="ts" setup>
import type { Client, ClientInput, Mutation, MutationCreateClientArgs, MutationUpdateClientArgs } from '@/gql/graphql.ts'
import type { FieldsConfig } from '@/components/general/form/types.ts'
import DynamicForm from '@/components/general/form/DynamicForm.vue'
import { notification } from '@/utils/notifications.ts'
import { useMutation } from '@vue/apollo-composable'
import { Create, Update } from '@/utils/queryMethods.ts'
import { HISTORY_FIELDS } from '@/utils/constants/grid.ts'

interface Props {
  rowData?: Client
}

interface Emits {
  (event: 'create', value: Client): void

  (event: 'update', value: Client): void

  (event: 'close'): void
}

const props = withDefaults(defineProps<Props>(), {
  rowData: undefined,
})

const emits = defineEmits<Emits>()

const formFields: Array<keyof ClientInput> = ['name', 'contact', 'phone', 'Groups', 'Sites']

const formConfigs: FieldsConfig<Client> = {
  Groups: {
    remoteOptions: {
      labelField: 'name',
      filters: {
        Client: {
          or: [
            {
              documentId: {
                eq: props.rowData?.documentId || '-1',
              },
            },
            {
              documentId: {
                null: true,
              },
            },
          ],
        },
      },
    },
  },
  Sites: {
    remoteOptions: {
      labelField: 'name',
      filters: {
        Client: {
          or: [
            {
              documentId: {
                eq: props.rowData?.documentId || '-1',
              },
            },
            {
              documentId: {
                null: true,
              },
            },
          ],
        },
      },
    },
  },
}

const { mutate: create, loading: loadingCreate } = useMutation<Mutation, MutationCreateClientArgs>(Create('Client', ['documentId']))
const { mutate: update, loading: loadingUpdate } = useMutation<Mutation, MutationUpdateClientArgs>(Update('Client', [...formFields, ...HISTORY_FIELDS]))

async function onSubmit(data: ClientInput) {
  try {
    console.log(props.rowData)
    if (props.rowData?.documentId) {
      const response = await update({
        documentId: props.rowData.documentId,
        data,
      })

      if (response?.data?.updateClient) emits('update', response?.data?.updateClient)
    } else {
      const response = await create({
        data,
      })
      if (response?.data?.createClient) emits('create', response?.data?.createClient)
    }

    notification('success', props.rowData?.documentId ? 'Client updated successfully' : 'Client created successfully')
  } catch (error) {
    notification('error', (error as Error).message)
  }
}
</script>

<template>
  <DynamicForm
    :data="props.rowData ?? {}"
    :fields="formFields"
    :fields-config="formConfigs"
    :loading="loadingCreate || loadingUpdate"
    is-modal
    model-name="Client"
    @close="emits('close')"
    @on-submit="onSubmit"
  />
</template>

<style lang="scss" scoped></style>
