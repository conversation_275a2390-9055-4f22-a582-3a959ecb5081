<script lang="ts" setup>
import type { Group, GroupInput, Mutation, MutationCreateGroupArgs, MutationUpdateGroupArgs, Patient } from '@/gql/graphql.ts'
import type { FieldsConfig } from '@/components/general/form/types.ts'
import DynamicForm from '@/components/general/form/DynamicForm.vue'
import { notification } from '@/utils/notifications.ts'
import { useMutation } from '@vue/apollo-composable'
import { Create, Update } from '@/utils/queryMethods.ts'
import { HISTORY_FIELDS } from '@/utils/constants/grid.ts'
import { formatPatientName } from '@/utils/formatters.ts'

interface Props {
  rowData?: Group
}

interface Emits {
  (event: 'create', value: Group): void

  (event: 'update', value: Group): void

  (event: 'close'): void
}

const props = withDefaults(defineProps<Props>(), {
  rowData: undefined,
})

const emits = defineEmits<Emits>()

const formFields: Array<keyof GroupInput> = ['name', 'Client', 'Patients']

const formConfigs: FieldsConfig<GroupInput> = {
  Client: {
    remoteOptions: {
      labelField: 'name',
    },
    rules: [
      {
        required: true,
        message: 'Client is required',
      },
    ],
  },
  Patients: {
    remoteOptions: {
      returnFields: ['firstName', 'lastName', 'namePrefix', 'nameSuffix'],
      filters: {
        Group: {
          or: [
            {
              documentId: {
                eq: props.rowData?.documentId || '-1',
              },
            },
            {
              documentId: {
                null: true,
              },
            },
          ],
        },
      },
      labelFunction(patient: Patient) {
        return formatPatientName(patient)
      },
    },
  },
}

const { mutate: create, loading: loadingCreate } = useMutation<Mutation, MutationCreateGroupArgs>(Create('Group', ['documentId']))
const { mutate: update, loading: loadingUpdate } = useMutation<Mutation, MutationUpdateGroupArgs>(Update('Group', [...formFields, ...HISTORY_FIELDS]))

async function onSubmit(data: GroupInput) {
  try {
    if (props.rowData?.documentId) {
      const response = await update({
        documentId: props.rowData.documentId,
        data,
      })

      if (response?.data?.updateGroup) emits('update', response?.data?.updateGroup)
    } else {
      const response = await create({
        data,
      })
      if (response?.data?.createGroup) emits('create', response?.data?.createGroup)
    }

    notification('success', props.rowData?.documentId ? 'Group updated successfully' : 'Group created successfully')
  } catch (error) {
    notification('error', (error as Error).message)
  }
}
</script>

<template>
  <DynamicForm
    :data="props.rowData!"
    :fields="formFields"
    :fields-config="formConfigs"
    :loading="loadingCreate || loadingUpdate"
    is-modal
    model-name="Group"
    @close="emits('close')"
    @on-submit="onSubmit"
  />
</template>

<style lang="scss" scoped></style>
