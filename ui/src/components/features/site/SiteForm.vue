<script lang="ts" setup>
import type { Address, Client, Mutation, MutationCreateSiteArgs, MutationUpdateSiteArgs, Site, SiteFiltersInput, SiteInput } from '@/gql/graphql.ts'
import type { FieldsConfig } from '@/components/general/form/types.ts'
import DynamicForm from '@/components/general/form/DynamicForm.vue'
import { notification } from '@/utils/notifications.ts'
import { useMutation } from '@vue/apollo-composable'
import { Create, Update } from '@/utils/queryMethods.ts'
import { HISTORY_FIELDS } from '@/utils/constants/grid.ts'
import useAddress from '@/composables/useAddress.ts'
import { computed, ref, useTemplateRef } from 'vue'
import { getUniqueValidationRule } from '@/utils/dynamicForm.ts'
import { useModelStore } from '@/stores/model.store.ts'
import { cloneDeep } from 'lodash-es'

import BusinessHours from '@/components/features/site/BusinessHours.vue'
import BlockoutDates, { type BlockoutDate } from '@/components/features/site/BlockoutDates.vue'

interface Props {
  rowData?: Site
}

interface Emits {
  (event: 'create', value: Site): void

  (event: 'update', value: Site): void

  (event: 'close'): void
}

const props = withDefaults(defineProps<Props>(), {
  rowData: undefined,
})

const emits = defineEmits<Emits>()

const formFields: Array<keyof SiteInput> = ['name', 'contact', 'phone', 'Address', 'Client', 'businessHours', 'blockoutDates']

const formRef = useTemplateRef('formRef')

const businessHoursJSON = ref<Record<string, string>>(props.rowData?.businessHours || {})
const blockoutDatesJSON = ref<BlockoutDate[]>(props.rowData?.blockoutDates || {})

const formConfigs = computed(() => {
  const nameField = useModelStore().getField('Client', 'name')

  const uniquePerClient: SiteFiltersInput = {
    Client: {
      documentId: {
        eq: formRef.value?.getFieldValue('Client') || '-1',
      },
    },
    name: {
      eq: formRef.value?.getFieldValue('name') || '',
    },
  }

  const formConfigs: FieldsConfig<Site> = {
    name: {
      rules: [
        {
          ...getUniqueValidationRule('Site', nameField!, props.rowData?.documentId || '', uniquePerClient, 'Name must be unique per Client'),
        },
      ],
    },
    Address: {
      rules: [
        {
          required: true,
          message: 'Address is required',
          trigger: 'change',
        },
      ],
    },
    Client: {
      icon: 'BuildingOffice2Icon',
      rules: [
        {
          required: true,
          message: 'Client is required',
          trigger: 'change',
        },
      ],
      remoteOptions: {
        labelField: 'name',
      },
    },
  }

  return formConfigs
})

const { saveAddress } = useAddress()

const { mutate: create, loading: loadingCreate } = useMutation<Mutation, MutationCreateSiteArgs>(Create('Site', ['documentId']))
const { mutate: update, loading: loadingUpdate } = useMutation<Mutation, MutationUpdateSiteArgs>(Update('Site', [...formFields, ...HISTORY_FIELDS]))

async function onSubmit(data: SiteInput & { Address?: Address | string }) {
  try {
    if (typeof data.Address === 'object') {
      const response = await saveAddress(data.Address)
      data.Address = response?.documentId || ''
    }

    data.businessHours = businessHoursJSON.value
    data.blockoutDates = blockoutDatesJSON.value

    if (props.rowData?.documentId) {
      const response = await update({
        documentId: props.rowData.documentId,
        data,
      })

      if (response?.data?.updateSite) emits('update', response?.data?.updateSite)
    } else {
      const response = await create({
        data,
      })
      if (response?.data?.createSite) emits('create', response?.data?.createSite)
    }

    notification('success', props.rowData?.documentId ? 'Site updated successfully' : 'Site created successfully')
  } catch (error) {
    notification('error', (error as Error).message)
  }
}

function onBusinessHoursSave(value: Record<string, string>) {
  businessHoursJSON.value = value
  formRef.value?.setFieldValue('businessHours', cloneDeep(value))
}

function onBlockoutDatesSave(value: BlockoutDate[]) {
  blockoutDatesJSON.value = value
  formRef.value?.setFieldValue('blockoutDates', cloneDeep(value))
  console.log('blockoutDatesJSON.value', blockoutDatesJSON.value)
}
</script>

<template>
  <DynamicForm
    ref="formRef"
    :data="props.rowData ?? {}"
    :fields="formFields"
    :fields-config="formConfigs"
    :loading="loadingCreate || loadingUpdate"
    is-modal
    label-width="150px"
    model-name="Site"
    @close="emits('close')"
    @on-submit="onSubmit"
  >
    <template #field-businessHours="{ formData, fieldName, fieldsRefs }">
      <BusinessHours :ref="(el: any) => (fieldsRefs[fieldName] = el)" :model-value="formData[fieldName]" @save="onBusinessHoursSave" />
    </template>
    <template #field-blockoutDates="{ formData, fieldName, fieldsRefs }">
      <BlockoutDates :ref="(el: any) => (fieldsRefs[fieldName] = el)" :model-value="formData[fieldName]" @save="onBlockoutDatesSave" />
    </template>
  </DynamicForm>
</template>

<style lang="scss" scoped></style>
