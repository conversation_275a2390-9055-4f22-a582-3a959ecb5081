<script lang="ts" setup>
import { computed, onMounted, ref, useTemplateRef, watch } from 'vue'
import DatePicker from 'primevue/datepicker'
import Checkbox from 'primevue/checkbox'
import Dialog from 'primevue/dialog'
import TabView from 'primevue/tabview'
import Tab<PERSON>anel from 'primevue/tabpanel'
import InputText from 'primevue/inputtext'
import Textarea from 'primevue/textarea'
import { cloneDeep, isEqual } from 'lodash-es'
import { format, isAfter, isEqual as isEqualDate, parseISO } from 'date-fns'

interface DaySchedule {
  enabled: boolean
  open: Date | null
  close: Date | null
  is24Hours: boolean
}

interface DaySchedules {
  [key: string]: DaySchedule
}

interface DateSpecificHours {
  id: string
  date: string // ISO date string (YYYY-MM-DD)
  enabled: boolean
  open: Date | null
  close: Date | null
  is24Hours: boolean
  title?: string
  reason?: string
}

export interface BusinessHoursJSON {
  // Date-specific hours
  dateSpecific?: DateSpecificHours[]

  // Weekly schedule (backward compatibility)
  [key: string]: string | DateSpecificHours[] | undefined
}

interface Props {
  modelValue?: BusinessHoursJSON | null
  buttonLabel?: string
  viewOnly?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: BusinessHoursJSON): void

  (e: 'save', value: BusinessHoursJSON): void

  (e: 'close'): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: null,
  buttonLabel: 'Set Business Hours',
  viewOnly: false,
})

const emit = defineEmits<Emits>()

const dialogVisible = ref(false)
const originalHours = ref<BusinessHoursJSON | null>(null)
const activeTab = ref(0) // 0 = Weekly Schedule, 1 = Date-Specific Hours

// Date-specific hours management
const dateSpecificHours = ref<DateSpecificHours[]>([])
const showAddDateForm = ref(false)
const newDateHours = ref<Partial<DateSpecificHours>>({
  enabled: true,
  is24Hours: false,
  title: '',
  reason: '',
})
const selectedDate = ref<Date | null>(null)

const toggleRef = useTemplateRef<any>('toggleRef')
const dialogRef = useTemplateRef<any>('dialogRef')

const daysOfWeek = [
  { key: 'monday', label: 'Monday', shortLabel: 'Mon' },
  { key: 'tuesday', label: 'Tuesday', shortLabel: 'Tue' },
  { key: 'wednesday', label: 'Wednesday', shortLabel: 'Wed' },
  { key: 'thursday', label: 'Thursday', shortLabel: 'Thu' },
  { key: 'friday', label: 'Friday', shortLabel: 'Fri' },
  { key: 'saturday', label: 'Saturday', shortLabel: 'Sat' },
  { key: 'sunday', label: 'Sunday', shortLabel: 'Sun' },
]

const daySchedules = ref<DaySchedules>({
  monday: { enabled: false, open: null, close: null, is24Hours: false },
  tuesday: { enabled: false, open: null, close: null, is24Hours: false },
  wednesday: { enabled: false, open: null, close: null, is24Hours: false },
  thursday: { enabled: false, open: null, close: null, is24Hours: false },
  friday: { enabled: false, open: null, close: null, is24Hours: false },
  saturday: { enabled: false, open: null, close: null, is24Hours: false },
  sunday: { enabled: false, open: null, close: null, is24Hours: false },
})

onMounted(() => {
  parseBusinessHours(props.modelValue)
})

const businessHoursJSON = computed((): BusinessHoursJSON => {
  const schedule: BusinessHoursJSON = {}

  // Weekly schedule
  for (const day of daysOfWeek) {
    const dayData = daySchedules.value[day.key]
    if (!dayData.enabled) {
      schedule[day.key] = 'CLOSED'
    } else if (dayData.is24Hours) {
      schedule[day.key] = '24H'
    } else if (dayData.open && dayData.close) {
      const openTime = formatTimeForStorage(dayData.open)
      const closeTime = formatTimeForStorage(dayData.close)
      schedule[day.key] = `${openTime}-${closeTime}`
    } else {
      schedule[day.key] = 'CLOSED'
    }
  }

  // Date-specific hours
  if (dateSpecificHours.value.length > 0) {
    schedule.dateSpecific = dateSpecificHours.value
  }

  return schedule
})

const hasBusinessHours = computed(() => Object.values(daySchedules.value).some((day) => day.enabled) || dateSpecificHours.value.length > 0)

const hasChanges = computed(() => {
  if (!originalHours.value) return false
  const currentHours = businessHoursJSON.value
  return !isEqual(currentHours, originalHours.value)
})

const upcomingDateSpecificHours = computed(() => {
  const today = new Date()
  return dateSpecificHours.value
    .filter((dateHour) => {
      try {
        const date = parseISO(dateHour.date)
        return isAfter(date, today) || isEqualDate(date, today)
      } catch {
        return false
      }
    })
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
    .slice(0, 3)
})

watch(
  () => props.modelValue,
  (newValue) => parseBusinessHours(newValue),
  { immediate: true, deep: true },
)

const getOpenDays = computed(() => {
  const weeklyDays = daysOfWeek
    .filter((day) => daySchedules.value[day.key].enabled)
    .slice(0, 2)
    .map((day) => {
      const dayData = daySchedules.value[day.key]
      let hours = ''
      if (dayData.is24Hours) {
        hours = '24h'
      } else if (dayData.open && dayData.close) {
        hours = `${formatTime(dayData.open)}-${formatTime(dayData.close)}`
      }
      return {
        key: day.key,
        shortLabel: day.shortLabel,
        hours,
        type: 'weekly' as const,
      }
    })

  const dateSpecific = upcomingDateSpecificHours.value.slice(0, 3 - weeklyDays.length).map((dateHour) => {
    let hours = ''
    if (dateHour.is24Hours) {
      hours = '24h'
    } else if (dateHour.open && dateHour.close) {
      hours = `${formatTime(dateHour.open)}-${formatTime(dateHour.close)}`
    }

    const date = parseISO(dateHour.date)
    const shortLabel = format(date, 'MMM dd')

    return {
      key: dateHour.id,
      shortLabel,
      hours,
      type: 'date-specific' as const,
      title: dateHour.title,
    }
  })

  return [...dateSpecific, ...weeklyDays]
})

const isDateFormValid = computed(() => {
  if (!selectedDate.value) return false

  if (newDateHours.value.enabled && !newDateHours.value.is24Hours) {
    return !!(newDateHours.value.open && newDateHours.value.close)
  }

  return true
})

function parseBusinessHours(hoursJSON: BusinessHoursJSON | null): void {
  for (const day of daysOfWeek) {
    daySchedules.value[day.key] = { enabled: false, open: null, close: null, is24Hours: false }
  }

  dateSpecificHours.value = []

  if (!hoursJSON) {
    return
  }

  try {
    // Parse weekly schedule
    for (const day of daysOfWeek) {
      const dayValue = hoursJSON[day.key]
      if (!dayValue || dayValue === 'CLOSED' || typeof dayValue !== 'string') {
        daySchedules.value[day.key] = { enabled: false, open: null, close: null, is24Hours: false }
      } else if (dayValue === '24H') {
        daySchedules.value[day.key] = { enabled: true, open: null, close: null, is24Hours: true }
      } else if (dayValue.includes('-')) {
        const [openTime, closeTime] = dayValue.split('-')
        daySchedules.value[day.key] = {
          enabled: true,
          open: parseTimeString(openTime),
          close: parseTimeString(closeTime),
          is24Hours: false,
        }
      } else {
        daySchedules.value[day.key] = { enabled: false, open: null, close: null, is24Hours: false }
      }
    }

    if (hoursJSON.dateSpecific && Array.isArray(hoursJSON.dateSpecific)) {
      dateSpecificHours.value = hoursJSON.dateSpecific.map((dateHour) => ({
        ...dateHour,
        open: dateHour.open ? new Date(dateHour.open) : null,
        close: dateHour.close ? new Date(dateHour.close) : null,
      }))
    }
  } catch (error) {
    console.error('Error parsing business hours:', error)
  }
}

function parseTimeString(timeStr: string): Date {
  const [time, period] = timeStr.split(' ')
  const [hours, minutes] = time.split(':').map(Number)
  const date = new Date()
  let adjustedHours = hours

  if (period === 'PM' && hours !== 12) {
    adjustedHours += 12
  } else if (period === 'AM' && hours === 12) {
    adjustedHours = 0
  }

  date.setHours(adjustedHours, minutes, 0, 0)
  return date
}

function formatTimeForStorage(date: Date): string {
  return date.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
  })
}

function formatTime(date: Date | null): string {
  return date ? formatTimeForStorage(date) : ''
}

function setQuickSchedule(type: string): void {
  const defaultOpen = new Date()
  defaultOpen.setHours(9, 0, 0, 0)
  const defaultClose = new Date()
  defaultClose.setHours(17, 0, 0, 0)
  const extendedClose = new Date()
  extendedClose.setHours(18, 0, 0, 0)

  switch (type) {
    case 'weekdays':
      for (const day of ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']) {
        daySchedules.value[day] = {
          enabled: true,
          open: new Date(defaultOpen),
          close: new Date(defaultClose),
          is24Hours: false,
        }
      }
      for (const day of ['saturday', 'sunday']) {
        daySchedules.value[day] = { enabled: false, open: null, close: null, is24Hours: false }
      }
      break
    case 'weekdaysWithSat':
      for (const day of ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']) {
        daySchedules.value[day] = {
          enabled: true,
          open: new Date(defaultOpen),
          close: new Date(extendedClose),
          is24Hours: false,
        }
      }
      daySchedules.value.sunday = { enabled: false, open: null, close: null, is24Hours: false }
      break
    case 'always':
      for (const day of daysOfWeek) {
        daySchedules.value[day.key] = { enabled: true, open: null, close: null, is24Hours: true }
      }
      break
  }
}

function clearAllHours(): void {
  for (const day of daysOfWeek) {
    daySchedules.value[day.key] = { enabled: false, open: null, close: null, is24Hours: false }
  }
}

function handle24Hours(dayKey: string): void {
  if (daySchedules.value[dayKey].is24Hours) {
    daySchedules.value[dayKey].open = null
    daySchedules.value[dayKey].close = null
  }
}

function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substring(2)
}

function formatDateForStorage(date: Date): string {
  return date.toISOString().split('T')[0]
}

function addDateSpecificHours(): void {
  if (!selectedDate.value) return

  if (newDateHours.value.enabled && !newDateHours.value.is24Hours) {
    if (!newDateHours.value.open || !newDateHours.value.close) {
      console.warn('Open and close times are required when not using 24-hour format')
      return
    }
  }

  const dateStr = formatDateForStorage(selectedDate.value)

  const existingIndex = dateSpecificHours.value.findIndex((h) => h.date === dateStr)

  const newHours: DateSpecificHours = {
    id: generateId(),
    date: dateStr,
    enabled: newDateHours.value.enabled ?? true,
    open: newDateHours.value.open || null,
    close: newDateHours.value.close || null,
    is24Hours: newDateHours.value.is24Hours ?? false,
    title: newDateHours.value.title || '',
    reason: newDateHours.value.reason || '',
  }

  if (existingIndex >= 0) {
    // Update existing
    dateSpecificHours.value[existingIndex] = newHours
  } else {
    // Add new
    dateSpecificHours.value.push(newHours)
  }

  resetDateForm()
}

function removeDateSpecificHours(id: string): void {
  const index = dateSpecificHours.value.findIndex((h) => h.id === id)
  if (index >= 0) {
    dateSpecificHours.value.splice(index, 1)
  }
}

function toggleDateSpecificHours(id: string): void {
  const dateHour = dateSpecificHours.value.find((h) => h.id === id)
  if (dateHour) {
    dateHour.enabled = !dateHour.enabled
  }
}

function resetDateForm(): void {
  selectedDate.value = null
  newDateHours.value = {
    enabled: true,
    is24Hours: false,
    title: '',
    reason: '',
  }
  showAddDateForm.value = false
}

function handleDateSpecific24Hours(): void {
  if (newDateHours.value.is24Hours) {
    newDateHours.value.open = null
    newDateHours.value.close = null
  }
}

function openDialog(): void {
  originalHours.value = props.modelValue ? cloneDeep(props.modelValue) : null
  dialogVisible.value = true
  activeTab.value = 0
  resetDateForm()
}

function saveAndClose(): void {
  const newValue = businessHoursJSON.value
  emit('update:modelValue', newValue)
  emit('save', newValue)
  dialogVisible.value = false
}

function cancelChanges(): void {
  parseBusinessHours(originalHours.value)
  resetDateForm()
  dialogVisible.value = false
}

defineExpose({
  focus: () => {
    if (toggleRef.value) {
      toggleRef.value.$el.focus()
    }
  },
})
</script>

<template>
  <div>
    <MainButton
      ref="toggleRef"
      :pt="{
        root: 'p-0 m-0 !max-h-[40px] truncate w-full ml-1',
        label: 'truncate w-full ',
      }"
      class="truncate p-0"
      severity="secondary"
      size="small"
      text
      @click="openDialog"
    >
      <p
        :class="{
          'placeholder-color': !hasBusinessHours,
        }"
        class="w-full truncate max-w-xs text-sm text-left"
      >
        {{
          getOpenDays
            .map((day) => {
              const prefix = day.type === 'date-specific' && day.title ? `${day.title} (${day.shortLabel})` : day.shortLabel
              return `${prefix}: ${day.hours}`
            })
            .join(', ') || props.buttonLabel
        }}
      </p>
    </MainButton>

    <!-- Business Hours Dialog -->
    <Dialog
      ref="dialogRef"
      v-model:visible="dialogVisible"
      v-focustrap
      :base-z-index="40000"
      :closable="true"
      :close-on-escape="false"
      :content-style="{ padding: '0' }"
      :draggable="false"
      :header="props.viewOnly ? 'View Business Hours' : 'Manage Business Hours'"
      :modal="true"
      class="w-full max-w-4xl"
      @after-hide="emit('close')"
    >
      <div class="p-6">
        <TabView v-model:active-index="activeTab" class="business-hours-tabs">
          <!-- Weekly Schedule Tab -->
          <TabPanel header="Weekly Schedule" value="0">
            <div class="space-y-4">
              <div v-for="day in daysOfWeek" :key="day.key" class="bg-slate-50 rounded-lg p-4">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3">
                    <Checkbox :id="`${day.key}-enabled`" v-model="daySchedules[day.key].enabled" :binary="true" :disabled="viewOnly" />
                    <label :for="`${day.key}-enabled`" class="font-medium text-slate-700 min-w-[80px]">
                      {{ day.label }}
                    </label>
                  </div>

                  <div v-if="daySchedules[day.key].enabled" class="flex items-center space-x-3 flex-1 justify-end">
                    <div class="flex items-center space-x-2">
                      <DatePicker
                        v-model="daySchedules[day.key].open"
                        :class="{ 'p-invalid': daySchedules[day.key].enabled && !daySchedules[day.key].is24Hours && !daySchedules[day.key].open }"
                        :disabled="daySchedules[day.key].is24Hours || viewOnly"
                        class="w-32"
                        hour-format="12"
                        placeholder="Open *"
                        size="small"
                        time-only
                      />
                      <span class="text-slate-500">to</span>
                      <DatePicker
                        v-model="daySchedules[day.key].close"
                        :class="{ 'p-invalid': daySchedules[day.key].enabled && !daySchedules[day.key].is24Hours && !daySchedules[day.key].close }"
                        :disabled="daySchedules[day.key].is24Hours || viewOnly"
                        class="w-32"
                        hour-format="12"
                        placeholder="Close *"
                        size="small"
                        time-only
                      />
                    </div>
                    <div class="flex items-center space-x-2">
                      <Checkbox
                        :id="`${day.key}-24h`"
                        v-model="daySchedules[day.key].is24Hours"
                        :binary="true"
                        :disabled="viewOnly"
                        size="small"
                        @change="handle24Hours(day.key)"
                      />
                      <label :for="`${day.key}-24h`" class="text-sm text-slate-600">24h</label>
                    </div>
                  </div>

                  <div v-else class="flex-1 text-right">
                    <span class="text-slate-400 italic">Closed</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Quick Actions -->
            <div v-if="!props.viewOnly" class="mt-6 pt-4 border-t border-slate-200">
              <h4 class="font-medium text-slate-600 text-sm mb-2">Quick Add:</h4>
              <div class="flex flex-wrap gap-2 mb-4">
                <MainButton :disabled="viewOnly" label="Mon-Fri 9-5" severity="secondary" size="small" variant="outlined" @click="setQuickSchedule('weekdays')" />
                <MainButton :disabled="viewOnly" label="Mon-Sat 9-6" severity="secondary" size="small" variant="outlined" @click="setQuickSchedule('weekdaysWithSat')" />
                <MainButton :disabled="viewOnly" label="24/7" severity="secondary" size="small" variant="outlined" @click="setQuickSchedule('always')" />
                <MainButton :disabled="viewOnly" label="Clear All" severity="danger" size="small" variant="outlined" @click="clearAllHours" />
              </div>
            </div>
          </TabPanel>

          <!-- Date-Specific Hours Tab -->
          <TabPanel header="Date-Specific Hours" value="1">
            <div class="space-y-4">
              <!-- Add New Date Form -->
              <div v-if="!props.viewOnly" class="bg-slate-50 rounded-lg p-4">
                <div v-if="!showAddDateForm" class="text-center">
                  <MainButton icon="pi pi-plus" label="Add Date-Specific Hours" size="small" @click="showAddDateForm = true" />
                </div>

                <div v-else class="space-y-4">
                  <div class="flex items-center justify-between">
                    <h4 class="font-medium text-slate-700">Add Date-Specific Hours</h4>
                    <MainButton icon="pi pi-times" severity="secondary" size="small" text @click="resetDateForm" />
                  </div>

                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label class="block text-sm font-medium text-slate-600 mb-1">Date</label>
                      <DatePicker v-model="selectedDate" :min-date="new Date()" class="w-full" placeholder="Select date" show-icon />
                    </div>

                    <div>
                      <label class="block text-sm font-medium text-slate-600 mb-1">Title (Optional)</label>
                      <InputText v-model="newDateHours.title" class="w-full" placeholder="e.g., Holiday Hours" />
                    </div>
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-slate-600 mb-1">Reason (Optional)</label>
                    <Textarea v-model="newDateHours.reason" class="w-full" placeholder="e.g., Christmas Day" rows="2" />
                  </div>

                  <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                      <Checkbox id="date-enabled" v-model="newDateHours.enabled" :binary="true" />
                      <label class="text-sm text-slate-600" for="date-enabled">Open</label>
                    </div>

                    <div v-if="newDateHours.enabled" class="flex items-center space-x-2">
                      <Checkbox id="date-24h" v-model="newDateHours.is24Hours" :binary="true" @change="handleDateSpecific24Hours" />
                      <label class="text-sm text-slate-600" for="date-24h">24 Hours</label>
                    </div>
                  </div>

                  <div v-if="newDateHours.enabled && !newDateHours.is24Hours" class="space-y-2">
                    <div class="flex items-center space-x-2">
                      <DatePicker
                        v-model="newDateHours.open"
                        :class="{ 'p-invalid': newDateHours.enabled && !newDateHours.is24Hours && !newDateHours.open }"
                        class="w-32"
                        hour-format="12"
                        placeholder="Open time *"
                        size="small"
                        time-only
                      />
                      <span class="text-slate-500">to</span>
                      <DatePicker
                        v-model="newDateHours.close"
                        :class="{ 'p-invalid': newDateHours.enabled && !newDateHours.is24Hours && !newDateHours.close }"
                        class="w-32"
                        hour-format="12"
                        placeholder="Close time *"
                        size="small"
                        time-only
                      />
                    </div>
                    <p v-if="newDateHours.enabled && !newDateHours.is24Hours && (!newDateHours.open || !newDateHours.close)" class="text-xs text-red-600">
                      Both open and close times are required
                    </p>
                  </div>

                  <div class="flex justify-end space-x-2">
                    <MainButton label="Cancel" severity="secondary" size="small" variant="outlined" @click="resetDateForm" />
                    <MainButton :disabled="!isDateFormValid" label="Add" size="small" @click="addDateSpecificHours" />
                  </div>
                </div>
              </div>

              <!-- Existing Date-Specific Hours -->
              <div v-if="dateSpecificHours.length === 0 && !showAddDateForm" class="text-center py-8 text-slate-500">
                <p>No date-specific hours configured.</p>
                <p class="text-sm">Use the button above to add special hours for specific dates.</p>
              </div>

              <div v-else class="space-y-2 max-h-64 overflow-y-auto">
                <div
                  v-for="dateHour in dateSpecificHours"
                  :key="dateHour.id"
                  :class="{ 'opacity-50': !dateHour.enabled }"
                  class="flex items-center justify-between p-3 bg-slate-50 rounded-lg"
                >
                  <div class="flex-1">
                    <div class="flex items-center space-x-2">
                      <span class="font-medium text-slate-800">
                        {{ format(parseISO(dateHour.date), 'MMM dd, yyyy') }}
                      </span>
                      <span v-if="dateHour.title" class="text-sm text-slate-600"> ({{ dateHour.title }}) </span>
                    </div>
                    <div class="text-sm text-slate-600">
                      {{
                        dateHour.enabled
                          ? dateHour.is24Hours
                            ? '24 Hours'
                            : dateHour.open && dateHour.close
                              ? `${formatTime(dateHour.open)} - ${formatTime(dateHour.close)}`
                              : 'No times set'
                          : 'Closed'
                      }}
                    </div>
                    <div v-if="dateHour.reason" class="text-xs text-slate-500">
                      {{ dateHour.reason }}
                    </div>
                  </div>

                  <div v-if="!props.viewOnly" class="flex items-center space-x-2">
                    <MainButton :icon="dateHour.enabled ? 'pi pi-eye-slash' : 'pi pi-eye'" severity="secondary" size="small" text @click="toggleDateSpecificHours(dateHour.id)" />
                    <MainButton icon="pi pi-trash" severity="danger" size="small" text @click="removeDateSpecificHours(dateHour.id)" />
                  </div>
                </div>
              </div>
            </div>
          </TabPanel>
        </TabView>
      </div>

      <template v-if="!props.viewOnly" #footer>
        <MainButton :disabled="viewOnly" label="Cancel" severity="secondary" size="small" variant="outlined" @click="cancelChanges" />
        <MainButton :disabled="viewOnly || hasChanges" label="Save" size="small" @click="saveAndClose" />
      </template>
    </Dialog>
  </div>
</template>
