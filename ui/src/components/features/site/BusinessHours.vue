<script lang="ts" setup>
import { computed, onMounted, ref, useTemplateRef, watch } from 'vue'
import DatePicker from 'primevue/datepicker'
import Checkbox from 'primevue/checkbox'
import Dialog from 'primevue/dialog'
import { isEqual } from 'lodash-es'

interface DaySchedule {
  enabled: boolean
  open: Date | null
  close: Date | null
  is24Hours: boolean
}

interface DaySchedules {
  [key: string]: DaySchedule
}

interface BusinessHoursJSON {
  [key: string]: string
}

interface Props {
  modelValue?: BusinessHoursJSON | null
  buttonLabel?: string
  viewOnly?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: BusinessHoursJSON): void

  (e: 'save', value: BusinessHoursJSON): void

  (e: 'close'): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: null,
  buttonLabel: 'Set Business Hours',
  viewOnly: false,
})

const emit = defineEmits<Emits>()

const dialogVisible = ref(false)
const originalHours = ref<BusinessHoursJSON | null>(null)

const toggleRef = useTemplateRef<any>('toggleRef')
const dialogRef = useTemplateRef<any>('dialogRef')

const daysOfWeek = [
  { key: 'monday', label: 'Monday', shortLabel: 'Mon' },
  { key: 'tuesday', label: 'Tuesday', shortLabel: 'Tue' },
  { key: 'wednesday', label: 'Wednesday', shortLabel: 'Wed' },
  { key: 'thursday', label: 'Thursday', shortLabel: 'Thu' },
  { key: 'friday', label: 'Friday', shortLabel: 'Fri' },
  { key: 'saturday', label: 'Saturday', shortLabel: 'Sat' },
  { key: 'sunday', label: 'Sunday', shortLabel: 'Sun' },
]

const daySchedules = ref<DaySchedules>({
  monday: { enabled: false, open: null, close: null, is24Hours: false },
  tuesday: { enabled: false, open: null, close: null, is24Hours: false },
  wednesday: { enabled: false, open: null, close: null, is24Hours: false },
  thursday: { enabled: false, open: null, close: null, is24Hours: false },
  friday: { enabled: false, open: null, close: null, is24Hours: false },
  saturday: { enabled: false, open: null, close: null, is24Hours: false },
  sunday: { enabled: false, open: null, close: null, is24Hours: false },
})

onMounted(() => {
  parseBusinessHours(props.modelValue)
})

const businessHoursJSON = computed((): BusinessHoursJSON => {
  const schedule: BusinessHoursJSON = {}

  for (const day of daysOfWeek) {
    const dayData = daySchedules.value[day.key]
    if (!dayData.enabled) {
      schedule[day.key] = 'CLOSED'
    } else if (dayData.is24Hours) {
      schedule[day.key] = '24H'
    } else if (dayData.open && dayData.close) {
      const openTime = formatTimeForStorage(dayData.open)
      const closeTime = formatTimeForStorage(dayData.close)
      schedule[day.key] = `${openTime}-${closeTime}`
    } else {
      schedule[day.key] = 'CLOSED'
    }
  }

  return schedule
})

const hasBusinessHours = computed(() => Object.values(daySchedules.value).some((day) => day.enabled))

const hasChanges = computed(() => {
  if (!originalHours.value) return false
  const currentHours = businessHoursJSON.value
  return isEqual(currentHours, originalHours.value)
})

watch(
  () => props.modelValue,
  (newValue) => parseBusinessHours(newValue),
  { immediate: true, deep: true },
)

const getOpenDays = computed(() =>
  daysOfWeek
    .filter((day) => daySchedules.value[day.key].enabled)
    .slice(0, 3)
    .map((day) => {
      const dayData = daySchedules.value[day.key]
      let hours = ''
      if (dayData.is24Hours) {
        hours = '24h'
      } else if (dayData.open && dayData.close) {
        hours = `${formatTime(dayData.open)}-${formatTime(dayData.close)}`
      }
      return {
        key: day.key,
        shortLabel: day.shortLabel,
        hours,
      }
    }),
)

function parseBusinessHours(hoursJSON: BusinessHoursJSON | null): void {
  if (!hoursJSON) {
    for (const day of daysOfWeek) {
      daySchedules.value[day.key] = { enabled: false, open: null, close: null, is24Hours: false }
    }
    return
  }

  try {
    for (const day of daysOfWeek) {
      const dayValue = hoursJSON[day.key]
      if (!dayValue || dayValue === 'CLOSED') {
        daySchedules.value[day.key] = { enabled: false, open: null, close: null, is24Hours: false }
      } else if (dayValue === '24H') {
        daySchedules.value[day.key] = { enabled: true, open: null, close: null, is24Hours: true }
      } else {
        const [openTime, closeTime] = dayValue.split('-')
        daySchedules.value[day.key] = {
          enabled: true,
          open: parseTimeString(openTime),
          close: parseTimeString(closeTime),
          is24Hours: false,
        }
      }
    }
  } catch (error) {
    console.error('Error parsing business hours:', error)
  }
}

function parseTimeString(timeStr: string): Date {
  const [time, period] = timeStr.split(' ')
  const [hours, minutes] = time.split(':').map(Number)
  const date = new Date()
  let adjustedHours = hours

  if (period === 'PM' && hours !== 12) {
    adjustedHours += 12
  } else if (period === 'AM' && hours === 12) {
    adjustedHours = 0
  }

  date.setHours(adjustedHours, minutes, 0, 0)
  return date
}

// Format Date to 12-hour string
function formatTimeForStorage(date: Date): string {
  return date.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
  })
}

function formatTime(date: Date | null): string {
  return date ? formatTimeForStorage(date) : ''
}

// Quick presets
function setQuickSchedule(type: string): void {
  const defaultOpen = new Date()
  defaultOpen.setHours(9, 0, 0, 0)
  const defaultClose = new Date()
  defaultClose.setHours(17, 0, 0, 0)
  const extendedClose = new Date()
  extendedClose.setHours(18, 0, 0, 0)

  switch (type) {
    case 'weekdays':
      for (const day of ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']) {
        daySchedules.value[day] = {
          enabled: true,
          open: new Date(defaultOpen),
          close: new Date(defaultClose),
          is24Hours: false,
        }
      }
      for (const day of ['saturday', 'sunday']) {
        daySchedules.value[day] = { enabled: false, open: null, close: null, is24Hours: false }
      }
      break
    case 'weekdaysWithSat':
      for (const day of ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']) {
        daySchedules.value[day] = {
          enabled: true,
          open: new Date(defaultOpen),
          close: new Date(extendedClose),
          is24Hours: false,
        }
      }
      daySchedules.value.sunday = { enabled: false, open: null, close: null, is24Hours: false }
      break
    case 'always':
      for (const day of daysOfWeek) {
        daySchedules.value[day.key] = { enabled: true, open: null, close: null, is24Hours: true }
      }
      break
  }
}

function clearAllHours(): void {
  for (const day of daysOfWeek) {
    daySchedules.value[day.key] = { enabled: false, open: null, close: null, is24Hours: false }
  }
}

function handle24Hours(dayKey: string): void {
  if (daySchedules.value[dayKey].is24Hours) {
    daySchedules.value[dayKey].open = null
    daySchedules.value[dayKey].close = null
  }
}

function openDialog(): void {
  originalHours.value = props.modelValue ? { ...props.modelValue } : null
  dialogVisible.value = true
}

function saveAndClose(): void {
  const newValue = businessHoursJSON.value
  emit('update:modelValue', newValue)
  emit('save', newValue)
  dialogVisible.value = false
}

function cancelChanges(): void {
  parseBusinessHours(originalHours.value)
  dialogVisible.value = false
}

defineExpose({
  focus: () => {
    if (toggleRef.value) {
      toggleRef.value.$el.focus()
    }
  },
})
</script>

<template>
  <div>
    <MainButton
      ref="toggleRef"
      :pt="{
        root: 'p-0 m-0 !max-h-[40px] truncate w-full ml-1',
        label: 'truncate w-full ',
      }"
      class="truncate p-0"
      severity="secondary"
      size="small"
      text
      @click="openDialog"
    >
      <p
        :class="{
          'placeholder-color': !hasBusinessHours,
        }"
        class="w-full truncate max-w-xs text-sm text-left"
      >
        {{ getOpenDays.map((day) => `${day.shortLabel}: ${day.hours}`).join(', ') || props.buttonLabel }}
      </p>
    </MainButton>

    <!-- Business Hours Dialog -->
    <Dialog
      ref="dialogRef"
      v-model:visible="dialogVisible"
      v-focustrap
      :base-z-index="40000"
      :closable="true"
      :close-on-escape="false"
      :content-style="{ padding: '0' }"
      :draggable="false"
      :header="props.viewOnly ? 'View Business Hours' : 'Manage Business Hours'"
      :modal="true"
      class="w-full max-w-4xl"
      @after-hide="emit('close')"
    >
      <div class="p-6">
        <div class="space-y-4">
          <div v-for="day in daysOfWeek" :key="day.key" class="bg-slate-50 rounded-lg p-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <Checkbox :id="`${day.key}-enabled`" v-model="daySchedules[day.key].enabled" :binary="true" :disabled="viewOnly" />
                <label :for="`${day.key}-enabled`" class="font-medium text-slate-700 min-w-[80px]">
                  {{ day.label }}
                </label>
              </div>

              <div v-if="daySchedules[day.key].enabled" class="flex items-center space-x-3 flex-1 justify-end">
                <div class="flex items-center space-x-2">
                  <DatePicker
                    v-model="daySchedules[day.key].open"
                    :disabled="daySchedules[day.key].is24Hours || viewOnly"
                    class="w-32"
                    hour-format="12"
                    placeholder="Open"
                    size="small"
                    time-only
                  />
                  <span class="text-slate-500">to</span>
                  <DatePicker
                    v-model="daySchedules[day.key].close"
                    :disabled="daySchedules[day.key].is24Hours || viewOnly"
                    class="w-32"
                    hour-format="12"
                    placeholder="Close"
                    size="small"
                    time-only
                  />
                </div>
                <div class="flex items-center space-x-2">
                  <Checkbox :id="`${day.key}-24h`" v-model="daySchedules[day.key].is24Hours" :binary="true" :disabled="viewOnly" size="small" @change="handle24Hours(day.key)" />
                  <label :for="`${day.key}-24h`" class="text-sm text-slate-600">24h</label>
                </div>
              </div>

              <div v-else class="flex-1 text-right">
                <span class="text-slate-400 italic">Closed</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div v-if="!props.viewOnly" class="mt-6 pt-4 border-t border-slate-200">
          <h4 class="font-medium text-slate-600 text-sm mb-2">Quick Add:</h4>
          <div class="flex flex-wrap gap-2 mb-4">
            <MainButton :disabled="viewOnly" label="Mon-Fri 9-5" severity="secondary" size="small" variant="outlined" @click="setQuickSchedule('weekdays')" />
            <MainButton :disabled="viewOnly" label="Mon-Sat 9-6" severity="secondary" size="small" variant="outlined" @click="setQuickSchedule('weekdaysWithSat')" />
            <MainButton :disabled="viewOnly" label="24/7" severity="secondary" size="small" variant="outlined" @click="setQuickSchedule('always')" />
            <MainButton :disabled="viewOnly" label="Clear All" severity="danger" size="small" variant="outlined" @click="clearAllHours" />
          </div>
        </div>
      </div>

      <template v-if="!props.viewOnly" #footer>
        <MainButton :disabled="viewOnly" label="Cancel" severity="secondary" size="small" variant="outlined" @click="cancelChanges" />
        <MainButton :disabled="viewOnly || hasChanges" label="Save" size="small" @click="saveAndClose" />
      </template>
    </Dialog>
  </div>
</template>
