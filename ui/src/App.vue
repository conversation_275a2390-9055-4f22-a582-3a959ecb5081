<script lang="ts" setup>
import { RouterView, useRouter } from 'vue-router'
import { defineAsyncComponent, onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '@/stores/auth.store'
import { authEvents } from '@/services/api'
import Toast from 'primevue/toast'
import { useToast } from 'primevue'
import { setToastInstance } from '@/utils/notifications.ts'

const NavBar = defineAsyncComponent(() => import('@/components/layout/NavBar.vue'))

const router = useRouter()
const authStore = useAuthStore()
const toast = useToast()
setToastInstance(toast)

const handleLogout = () => {
  console.log('Auto logout triggered due to token expiration')
  authStore.logout()
  router.push('/')
}

onMounted(() => {
  authEvents.addEventListener('logout', handleLogout)
})

onUnmounted(() => {
  authEvents.removeEventListener('logout', handleLogout)
})
</script>

<template>
  <div class="min-h-screen">
    <div v-if="!authStore.isLoading && authStore.isAuthenticated" class="sticky top-0 z-50 bg-white/95 backdrop-blur-sm shadow-xs border-b border-slate-200/50">
      <NavBar />
    </div>

    <Toast />

    <main>
      <router-view v-slot="{ Component, route }">
        <component :is="Component" :key="route.name" />
      </router-view>
    </main>
  </div>
</template>

<style scoped>
html {
  scroll-behavior: smooth;
}

.sticky {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}
</style>
