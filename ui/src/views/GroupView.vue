<script lang="ts" setup>
import BaseLayoutContainer from '@/components/layout/BaseLayoutContainer.vue'
import { defineAsyncComponent, onBeforeMount, ref, useTemplateRef } from 'vue'
import GridComponent from '@/components/general/grid/GridComponent.vue'
import type { Client, Group, GroupFiltersInput } from '@/gql/graphql.ts'
import type { ColumnType } from '@/components/general/grid/utils/types.ts'
import PaginationComponent from '@/components/general/grid/PaginationComponent.vue'
import { notification } from '@/utils/notifications.ts'
import { BuildingOffice2Icon, UserIcon } from '@heroicons/vue/24/outline'
import HistoryCell from '@/components/general/grid/cells/HistoryCell.vue'
import type { ReturnFieldProp } from '@/utils/queryMethods.ts'
import { HISTORY_FIELDS } from '@/utils/constants/grid.ts'
import SearchComponent from '@/components/general/SearchComponent.vue'
import { useRoute, useRouter } from 'vue-router'
import Chip from 'primevue/chip'
import { cloneDeep } from 'lodash-es'

const GroupForm = defineAsyncComponent(() => import('@/components/features/group/GroupForm.vue'))
const route = useRoute()
const router = useRouter()

const gridColumns: ColumnType<Group>[] = [
  {
    key: 'actions',
    title: '',
    width: 80,
  },
  {
    key: 'name',
    title: 'Name',
  },
  {
    key: 'client',
    title: 'Client',
  },
  {
    key: 'Patients',
    title: 'Patients',
  },
  {
    key: 'history',
    title: 'History',
  },
]

const paginationFields: ReturnFieldProp = ['name', { client: ['name'] }, { Patients: ['documentId'] }, ...HISTORY_FIELDS]

// grid
const loading = ref(false)
const groups = ref<Group[]>([])
const showForm = ref(false)
const selectedRow = ref<Group>()

//pagination
const paginationRef = useTemplateRef('paginationRef')

const searchFilters = ref<GroupFiltersInput>({})

const client = ref<Partial<Client>>()

function onPageChange(data: Group[]) {
  groups.value = data
}

onBeforeMount(() => {
  const clientId = route.query.clientId as string
  const clientName = route.query.clientName as string

  if (clientId && clientName) {
    client.value = { documentId: clientId, name: clientName }

    searchFilters.value = {
      ...searchFilters.value,
      Client: {
        documentId: {
          eq: clientId,
        },
      },
    }
  }
})

function clearClientFilter() {
  router.push({ name: 'Groups' })
  client.value = { documentId: '', name: '' }
  searchFilters.value = { ...searchFilters.value, Client: undefined }
}

function onCreate() {
  paginationRef.value?.refresh()
  closeForm()
}

function onUpdate(data: Group) {
  const index = groups.value.findIndex((item) => item.documentId === data.documentId)
  if (index !== -1) {
    groups.value[index] = data
  }
  closeForm()
}

function editGroup(rowData: Group) {
  const data = cloneDeep(rowData)
  const { Patients, ...cleanedData } = data
  selectedRow.value = cleanedData as Group
  showForm.value = true
}

function closeForm() {
  showForm.value = false
  selectedRow.value = undefined
}

function onSearchFiltersChange(filters: GroupFiltersInput) {
  searchFilters.value = filters
}

function openForm() {
  selectedRow.value = {
    Client: {
      documentId: client.value?.documentId || '',
      name: client.value?.name || '',
    },
  } as Group
  showForm.value = true
}
</script>

<template>
  <BaseLayoutContainer>
    <template #button-top>
      <div class="flex justify-between items-center w-full">
        <div class="flex items-center gap-3">
          <SearchComponent v-model:loading="loading" model-name="Group" @filters-change="onSearchFiltersChange" />
          <Chip v-if="client?.name" :label="client?.name" icon="pi pi-filter" removable size="small" @remove="clearClientFilter" />
        </div>
        <MainButton label="Add new" size="small" @click="openForm"></MainButton>
      </div>
    </template>
    <template #form>
      <GroupForm v-if="showForm" :row-data="selectedRow" @close="closeForm" @create="onCreate" @update="onUpdate" />
    </template>

    <template #content>
      <GridComponent :columns="gridColumns" :data="groups" :loading="loading" :selected-row-id="selectedRow?.documentId">
        <template #cell-name="{ rowData }">
          <div :title="rowData.name || ''">
            {{ rowData.name }}
          </div>
        </template>

        <template #cell-client="{ rowData }">
          <div v-if="rowData.Client" class="flex items-center gap-0.5">
            <BuildingOffice2Icon class="size-4 text-slate-600" />
            <span>{{ rowData.Client?.name }}</span>
          </div>
        </template>

        <template #cell-Patients="{ rowData }">
          <RouterLink
            :title="`View ${rowData.Patients?.length || 0} patients for ${rowData.name}`"
            :to="{
              name: 'Patients',
              query: {
                groupId: rowData.documentId,
                groupName: rowData.name,
              },
            }"
            class="flex items-center gap-0.5"
          >
            <UserIcon class="size-4" />
            <span>{{ rowData.Patients?.length || 0 }}</span>
          </RouterLink>
        </template>

        <template #cell-history="{ rowData }">
          <HistoryCell :row-data="rowData" />
        </template>

        <template #cell-actions="{ rowData }">
          <MainButton aria-label="Edit group" icon="pi pi-pencil" severity="secondary" size="small" text @click="editGroup(rowData)" />
        </template>

        <template #pagination>
          <PaginationComponent
            ref="paginationRef"
            :fields="paginationFields"
            :filters="searchFilters"
            model-name="Group"
            settings-id="group_view"
            @error="notification('error', $event)"
            @loading="loading = $event"
            @page-change="onPageChange"
          />
        </template>
      </GridComponent>
    </template>
  </BaseLayoutContainer>
</template>

<style lang="scss" scoped></style>
