<script lang="ts" setup>
import { useRouter } from 'vue-router'
import { ArrowRightIcon, BeakerIcon, BuildingOffice2Icon, MapPinIcon, UserGroupIcon, UserIcon } from '@heroicons/vue/24/outline'
import Card from 'primevue/card'
import { useAuthStore } from '@/stores/auth.store'

const navigationCards = [
  {
    title: 'Clients',
    description: 'Manage clients and their information',
    icon: BuildingOffice2Icon,
    route: 'Clients',
  },
  {
    title: 'Groups',
    description: 'Organize and manage patient groups',
    icon: UserGroupIcon,
    route: 'Groups',
  },
  {
    title: 'Patients',
    description: 'View and manage patient information',
    icon: UserIcon,
    route: 'Patients',
  },
  {
    title: 'Client Sites',
    description: 'Manage locations with business hours and blockout dates',
    icon: MapPinIcon,
    route: 'Client Sites',
  },
]

const router = useRouter()
const authStore = useAuthStore()

function navigateTo(route: string) {
  router.push({ name: route })
}
</script>

<template>
  <div class="min-h-[91.5dvh] bg-gradient-to-br from-slate-50 to-slate-100">
    <div class="relative overflow-hidden">
      <div class="absolute inset-0 bg-gradient-to-r from-emerald-600 via-emerald-500 to-teal-500"></div>

      <div class="absolute inset-0 opacity-10">
        <div class="absolute top-1/4 left-1/4 w-2 h-2 bg-white rounded-full animate-twinkle"></div>
        <div class="absolute top-1/3 right-1/3 w-1 h-1 bg-white rounded-full animate-twinkle-delayed"></div>
        <div class="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-white rounded-full animate-twinkle-slow"></div>
        <div class="absolute top-2/3 right-1/4 w-1 h-1 bg-white rounded-full animate-twinkle"></div>
      </div>

      <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="text-center">
          <div class="flex items-center justify-center mb-6">
            <BeakerIcon class="h-16 w-16 text-white animate-bounce-gentle" />
          </div>

          <h1 class="text-5xl md:text-6xl font-bold text-white mb-4 tracking-tight">Welcome to Emerald Randomizer</h1>

          <p class="text-xl text-white">
            Welcome back, <span class="font-semibold text-white">{{ authStore.getUser?.username }}</span>
          </p>
        </div>
      </div>
    </div>

    <div class="py-16 px-4 sm:px-6 lg:px-8">
      <div class="max-w-7xl mx-auto">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-slate-900 mb-4">Quick Navigation</h2>
          <p class="text-lg text-slate-600 max-w-2xl mx-auto">Choose your destination to begin managing your healthcare operations</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card
            v-for="(card, index) in navigationCards"
            :key="card.title"
            :class="[`card-visible card-${index}`]"
            :pt="{
              root: 'group relative bg-white shadow-lg hover:shadow-xl cursor-pointer overflow-hidden hover:-translate-y-1',
            }"
            @click="navigateTo(card.route)"
          >
            <template #content>
              <div class="relative p-6 flex flex-col justify-between h-full">
                <div class="flex items-center justify-center mb-4">
                  <div
                    class="w-14 h-14 white rounded-xl flex items-center justify-center group-hover:bg-white group-hover:scale-110 transition-all duration-300 border-2 border-emerald-100"
                  >
                    <component :is="card.icon" class="h-7 w-7 text-primary" />
                  </div>
                </div>

                <h3 class="text-xl font-semibold text-slate-900 text-center mb-3 transition-colors duration-300">
                  {{ card.title }}
                </h3>

                <p class="text-slate-600 text-center mb-4 text-sm leading-relaxed group-hover:text-slate-700 transition-colors duration-300">
                  {{ card.description }}
                </p>

                <div class="flex justify-center">
                  <div class="inline-flex items-center text-primary font-medium text-sm transition-colors duration-300">
                    Go to {{ card.title }}
                    <ArrowRightIcon class="ml-1 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                  </div>
                </div>
              </div>
            </template>
          </Card>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
@keyframes bounce-gentle {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

@keyframes twinkle {
  0%,
  100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.2);
  }
}

@keyframes twinkle-delayed {
  0%,
  100% {
    opacity: 0.2;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.1);
  }
}

@keyframes twinkle-slow {
  0%,
  100% {
    opacity: 0.1;
    transform: scale(1);
  }
  50% {
    opacity: 0.4;
    transform: scale(1.3);
  }
}

.animate-bounce-gentle {
  animation: bounce-gentle 3s ease-in-out infinite;
}

.animate-twinkle {
  animation: twinkle 3s ease-in-out infinite;
}

.animate-twinkle-delayed {
  animation: twinkle-delayed 4s ease-in-out infinite;
}

.animate-twinkle-slow {
  animation: twinkle-slow 5s ease-in-out infinite;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card-visible {
  animation: fadeInUp 0.6s ease-out forwards;
  animation-fill-mode: both;
  will-change: opacity, transform;
}

.card-0 {
  animation-delay: 300ms;
}

.card-1 {
  animation-delay: 450ms;
}

.card-2 {
  animation-delay: 600ms;
}

.card-3 {
  animation-delay: 750ms;
}

.card-hidden {
  opacity: 0;
  transform: translateY(20px);
  transition: none;
}
</style>
