<script lang="ts" setup>
import BaseLayoutContainer from '@/components/layout/BaseLayoutContainer.vue'
import { defineAsyncComponent, onBeforeMount, ref, useTemplateRef } from 'vue'
import GridComponent from '@/components/general/grid/GridComponent.vue'
import type { Client, Site, SiteFiltersInput } from '@/gql/graphql.ts'
import type { ColumnType } from '@/components/general/grid/utils/types.ts'
import PaginationComponent from '@/components/general/grid/PaginationComponent.vue'
import { notification } from '@/utils/notifications.ts'
import { BuildingOffice2Icon, EnvelopeIcon, MapPinIcon, PhoneIcon, TagIcon } from '@heroicons/vue/24/outline'
import HistoryCell from '@/components/general/grid/cells/HistoryCell.vue'
import type { ReturnFieldProp } from '@/utils/queryMethods.ts'
import { HISTORY_FIELDS } from '@/utils/constants/grid.ts'
import SearchComponent from '@/components/general/SearchComponent.vue'
import { getFormattedAddress } from '@/utils/helpers.ts'
import BusinessHours from '@/components/features/site/BusinessHours.vue'
import BlockoutDates from '@/components/features/site/BlockoutDates.vue'
import { useRoute, useRouter } from 'vue-router'
import Chip from 'primevue/chip'

const SiteForm = defineAsyncComponent(() => import('@/components/features/site/SiteForm.vue'))
const route = useRoute()
const router = useRouter()

const gridColumns: ColumnType<Site>[] = [
  {
    key: 'actions',
    title: '',
    width: 80,
  },
  {
    key: 'name',
    title: 'Site',
  },
  {
    key: 'Client',
    title: 'Client',
  },
  {
    key: 'businessHours',
    title: 'Business Hours',
  },
  {
    key: 'blockoutDates',
    title: 'Blockout Dates',
  },
  {
    key: 'history',
    title: 'History',
  },
]

const paginationFields: ReturnFieldProp = ['name', 'contact', 'phone', 'Address', 'businessHours', 'blockoutDates', { Client: ['name'] }, ...HISTORY_FIELDS]
// grid
const loading = ref(false)
const sites = ref<Site[]>([])
const showForm = ref(false)
const selectedRow = ref<Site>()

const paginationRef = useTemplateRef('paginationRef')

const searchFilters = ref<SiteFiltersInput>({})

const client = ref<Partial<Client>>()

function onPageChange(data: Site[]) {
  sites.value = data
}

onBeforeMount(() => {
  const clientId = route.query.clientId as string
  const clientName = route.query.clientName as string

  if (clientId && clientName) {
    client.value = { documentId: clientId, name: clientName }

    searchFilters.value = {
      ...searchFilters.value,
      Client: {
        documentId: {
          eq: clientId,
        },
      },
    }
  }
})

function clearClientFilter() {
  router.push({ name: 'Sites' })
  client.value = { documentId: '', name: '' }
  searchFilters.value = { ...searchFilters.value, Client: undefined }
}

function onCreate() {
  paginationRef.value?.refresh()
  closeForm()
}

function onUpdate(data: Site) {
  const index = sites.value.findIndex((item) => item.documentId === data.documentId)
  if (index !== -1) {
    sites.value[index] = data
  }
  closeForm()
}

function editSite(rowData: Site) {
  selectedRow.value = rowData
  showForm.value = true
}

function closeForm() {
  showForm.value = false
  selectedRow.value = undefined
}

function onSearchFiltersChange(filters: SiteFiltersInput) {
  searchFilters.value = filters
}

function openForm() {
  selectedRow.value = {
    Client: {
      documentId: client.value?.documentId || '',
      name: client.value?.name || '',
    },
  } as Site
  showForm.value = true
}
</script>

<template>
  <BaseLayoutContainer>
    <template #button-top>
      <div class="flex justify-between items-center w-full">
        <div class="flex items-center gap-3">
          <SearchComponent v-model:loading="loading" model-name="Site" @filters-change="onSearchFiltersChange" />
          <Chip v-if="client?.name" :label="client?.name" icon="pi pi-filter" removable size="small" @remove="clearClientFilter" />
        </div>
        <MainButton label="Add new" size="small" @click="openForm"></MainButton>
      </div>
    </template>
    <template #form>
      <SiteForm v-if="showForm" :row-data="selectedRow" @close="closeForm" @create="onCreate" @update="onUpdate" />
    </template>

    <template #content>
      <GridComponent :columns="gridColumns" :data="sites" :estimated-row-height="100" :loading="loading" :selected-row-id="selectedRow?.documentId">
        <template #cell-name="{ rowData }">
          <div :title="rowData.name" class="flex items-center gap-1">
            <TagIcon class="size-4 text-slate-600" />
            {{ rowData.name }}
          </div>
          <div class="flex flex-col gap-1 text-sm">
            <div :title="rowData.contact" class="flex items-center gap-1">
              <EnvelopeIcon class="size-4 text-slate-600" />
              <a :href="'mailto:' + rowData.contact" class="text-slate-600 hover:text-blue-500 w-full truncate">
                {{ rowData.contact }}
              </a>
            </div>
            <div :title="rowData?.phone ?? ''" class="flex items-center gap-0.5">
              <PhoneIcon v-if="rowData.phone" class="size-4 text-slate-600" />
              <a v-if="rowData.phone" :href="'tel:' + rowData.phone" class="text-slate-600 hover:text-blue-500 w-full truncate">
                {{ rowData.phone }}
              </a>
            </div>
            <div v-if="rowData.Address?.address" :title="getFormattedAddress(rowData.Address?.address)" class="flex items-center gap-0.5">
              <MapPinIcon class="size-4 text-slate-600 flex-shrink-0" />
              <a
                :href="`https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(getFormattedAddress(rowData.Address?.address))}`"
                aria-label="View on Google Maps"
                class="text-slate-600 hover:text-blue-500 truncate"
                target="_blank"
              >
                {{ getFormattedAddress(rowData.Address?.address) }}
              </a>
            </div>
          </div>
        </template>

        <template #cell-Client="{ rowData }">
          <div v-if="rowData.Client" class="flex items-center gap-0.5">
            <BuildingOffice2Icon class="size-4 text-slate-600" />
            <div class="w-full truncate">{{ rowData.Client?.name }}</div>
          </div>
        </template>

        <template #cell-businessHours="{ rowData }">
          <BusinessHours :model-value="rowData.businessHours" view-only @click="selectedRow = rowData" @close="selectedRow = undefined" />
        </template>

        <template #cell-blockoutDates="{ rowData }">
          <BlockoutDates :model-value="rowData.blockoutDates" view-only @click="selectedRow = rowData" @close="selectedRow = undefined" />
        </template>

        <template #cell-history="{ rowData }">
          <HistoryCell :row-data="rowData" />
        </template>

        <template #cell-actions="{ rowData }">
          <MainButton aria-label="Edit site" icon="pi pi-pencil" severity="secondary" size="small" text @click="editSite(rowData)" />
        </template>

        <template #pagination>
          <PaginationComponent
            ref="paginationRef"
            :fields="paginationFields"
            :filters="searchFilters"
            model-name="Site"
            settings-id="site_view"
            @error="notification('error', $event)"
            @loading="loading = $event"
            @page-change="onPageChange"
          />
        </template>
      </GridComponent>
    </template>
  </BaseLayoutContainer>
</template>

<style lang="scss" scoped></style>
