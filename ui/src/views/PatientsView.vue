<script lang="ts" setup>
import BaseLayoutContainer from '@/components/layout/BaseLayoutContainer.vue'
import { defineAsyncComponent, onBeforeMount, ref, useTemplateRef } from 'vue'
import GridComponent from '@/components/general/grid/GridComponent.vue'
import type { Group, Patient, PatientFiltersInput } from '@/gql/graphql.ts'
import type { ColumnType } from '@/components/general/grid/utils/types.ts'
import PaginationComponent from '@/components/general/grid/PaginationComponent.vue'
import { notification } from '@/utils/notifications.ts'
import { EnvelopeIcon, IdentificationIcon, LockClosedIcon, PhoneIcon, UserGroupIcon, UserIcon } from '@heroicons/vue/24/outline'
import HistoryCell from '@/components/general/grid/cells/HistoryCell.vue'
import type { ReturnFieldProp } from '@/utils/queryMethods.ts'
import { HISTORY_FIELDS } from '@/utils/constants/grid.ts'
import SearchComponent from '@/components/general/SearchComponent.vue'
import { formatDate } from '@/utils/dateMethods.ts'
import { useRoute, useRouter } from 'vue-router'
import Chip from 'primevue/chip'
import { formatPatientName } from '@/utils/formatters.ts'
import CallSessionsDialog from '@/components/features/patient/CallSessionsDialog.vue'

const PatientForm = defineAsyncComponent(() => import('@/components/features/patient/PatientForm.vue'))
const route = useRoute()
const router = useRouter()

const gridColumns: ColumnType<Patient>[] = [
  { key: 'actions', title: '', width: 80 },
  { key: 'fullName', title: 'Name' },
  { key: 'dob', title: 'Date of Birth' },
  { key: 'identifiers', title: 'Identifiers' },
  { key: 'Group', title: 'Group' },
  { key: 'demographics', title: 'Demographics' },
  { key: 'history', title: 'History' },
]

const paginationFields: ReturnFieldProp = [
  'firstName',
  'lastName',
  'namePrefix',
  'nameSuffix',
  'pid',
  'dob',
  'ssn',
  'phone',
  'contact',
  { Group: ['name'] },
  { Race: ['value'] },
  { Sex: ['value'] },
  ...HISTORY_FIELDS,
]

const loading = ref(false)
const patients = ref<Patient[]>([])
const showForm = ref(false)
const selectedRow = ref<Patient>()
const paginationRef = useTemplateRef('paginationRef')
const searchFilters = ref<PatientFiltersInput>({})

// Call Sessions Dialog
const showCallSessionsDialog = ref(false)
const selectedPatientForCallSessions = ref<Patient>()

const group = ref<Partial<Group>>({
  documentId: '',
  name: '',
})

function onPageChange(data: Patient[]) {
  patients.value = data
}

onBeforeMount(() => {
  const groupId = route.query.groupId as string
  const groupName = route.query.groupName as string

  if (groupId && groupName) {
    group.value = { documentId: groupId, name: groupName }

    searchFilters.value = {
      ...searchFilters.value,
      Group: {
        documentId: {
          eq: groupId,
        },
      },
    }
  }
})

function clearGroupFilter() {
  router.push({ name: 'Patients' })
  group.value = { documentId: '', name: '' }
  searchFilters.value = { ...searchFilters.value, Group: undefined }
}

function onCreate() {
  paginationRef.value?.refresh()
  closeForm()
}

function onUpdate(d: Patient) {
  const i = patients.value.findIndex((p) => p.documentId === d.documentId)
  if (i !== -1) patients.value[i] = d
  closeForm()
}

function editPatient(row: Patient) {
  selectedRow.value = row
  showForm.value = true
}

function closeForm() {
  showForm.value = false
  selectedRow.value = undefined
}

function onSearchFiltersChange(f: PatientFiltersInput) {
  searchFilters.value = f
}

function openForm() {
  selectedRow.value = {
    Group: {
      documentId: group.value.documentId || '',
      name: group.value.name || '',
    },
  } as Patient
  showForm.value = true
}

function openCallSessionsDialog(patient: Patient) {
  selectedPatientForCallSessions.value = patient
  selectedRow.value = patient
  showCallSessionsDialog.value = true
}
</script>

<template>
  <BaseLayoutContainer>
    <template #button-top>
      <div class="flex justify-between items-center w-full">
        <div class="flex items-center gap-3">
          <SearchComponent v-model:loading="loading" model-name="Patient" @filters-change="onSearchFiltersChange" />
          <Chip v-if="group.name" :label="group.name" icon="pi pi-filter" removable size="small" @remove="clearGroupFilter" />
        </div>
        <MainButton label="Add new" size="small" @click="openForm" />
      </div>
    </template>

    <template #form>
      <PatientForm v-if="showForm" :row-data="selectedRow" @close="closeForm" @create="onCreate" @update="onUpdate" />
    </template>

    <template #content>
      <GridComponent :columns="gridColumns" :data="patients" :loading="loading" :selected-row-id="selectedRow?.documentId">
        <template #cell-fullName="{ rowData }">
          <div class="flex flex-col gap-1">
            <div :title="formatPatientName(rowData)" class="truncate flex items-center gap-1">
              <UserIcon class="size-4 text-slate-600" />
              <div class="truncate">
                {{ formatPatientName(rowData) }}
              </div>
            </div>

            <div class="flex flex-col gap-1 text-sm">
              <div v-if="rowData.contact" :title="rowData.contact" class="flex items-center gap-1">
                <EnvelopeIcon class="size-4 text-slate-600" />
                <a :href="'mailto:' + rowData.contact" class="text-slate-600 hover:text-blue-500 w-full truncate">
                  {{ rowData.contact }}
                </a>
              </div>
              <div v-if="rowData.phone" :title="rowData?.phone ?? ''" class="flex items-center gap-0.5">
                <PhoneIcon class="size-4 text-slate-600" />
                <a v-if="rowData.phone" :href="'tel:' + rowData.phone" class="text-slate-600 hover:text-blue-500 w-full truncate">
                  {{ rowData.phone }}
                </a>
              </div>

              <MainButton
                :pt="{
                  root: '!w-fit !p-0 !items-start !justify-start !px-1',
                }"
                severity="secondary"
                size="small"
                variant="text"
                @click="openCallSessionsDialog(rowData)"
              >
                <div class="truncate">View Call Sessions</div>
              </MainButton>
            </div>
          </div>
        </template>

        <!-- DATE OF BIRTH -->
        <template #cell-dob="{ rowData }">
          <div class="flex items-center gap-1 text-sm">
            <div class="truncate">{{ formatDate(rowData.dob) || 'N/A' }}</div>
          </div>
        </template>

        <!-- IDENTIFIERS -->
        <template #cell-identifiers="{ rowData }">
          <div class="flex flex-col gap-1 text-sm">
            <div class="flex items-center gap-1">
              <IdentificationIcon aria-hidden="true" class="size-4" />
              <span class="font-medium">Patient ID:</span>
              <div class="truncate">{{ rowData.pid || 'N/A' }}</div>
            </div>
            <div class="flex items-center gap-1">
              <LockClosedIcon aria-hidden="true" class="size-4" />
              <span class="font-medium">SSN:</span>
              <div class="truncate">{{ rowData.ssn || 'N/A' }}</div>
            </div>
          </div>
        </template>

        <!-- GROUP -->
        <template #cell-Group="{ rowData }">
          <RouterLink
            v-if="rowData.Group?.name && rowData.Group?.Client"
            :title="`View groups for ${rowData.Group.Client.name}`"
            :to="{
              name: 'Groups',
              query: {
                ClientId: rowData.Group.Client.documentId,
                ClientName: rowData.Group.Client.name,
              },
            }"
            class="flex items-center gap-1 text-sm"
          >
            <UserGroupIcon aria-hidden="true" class="size-4" />
            <div class="truncate">{{ rowData.Group.name }}</div>
          </RouterLink>
          <div v-else class="flex items-center gap-1 text-sm">
            <UserGroupIcon aria-hidden="true" class="size-4" />
            <div class="truncate">{{ rowData.Group?.name || 'N/A' }}</div>
          </div>
        </template>

        <!-- DEMOGRAPHICS -->
        <template #cell-demographics="{ rowData }">
          <div class="flex flex-col gap-1 text-sm">
            <div class="truncate"><span class="font-medium">Race:</span> {{ rowData.Race?.value || 'N/A' }}</div>
            <div class="truncate"><span class="font-medium">Sex:</span> {{ rowData.Sex?.value || 'N/A' }}</div>
          </div>
        </template>

        <!-- HISTORY -->
        <template #cell-history="{ rowData }">
          <HistoryCell :row-data="rowData" />
        </template>
        <!-- ACTIONS -->
        <template #cell-actions="{ rowData }">
          <MainButton aria-label="Edit patient" icon="pi pi-pencil" severity="secondary" size="small" text @click="editPatient(rowData)" />
        </template>

        <!-- PAGINATION -->
        <template #pagination>
          <PaginationComponent
            ref="paginationRef"
            :fields="paginationFields"
            :filters="searchFilters"
            model-name="Patient"
            settings-id="patient_view"
            @error="notification('error', $event)"
            @loading="loading = $event"
            @page-change="onPageChange"
          />
        </template>
      </GridComponent>
    </template>
  </BaseLayoutContainer>

  <CallSessionsDialog v-model:visible="showCallSessionsDialog" :patient="selectedPatientForCallSessions" @close="selectedRow = undefined" />
</template>

<style lang="scss" scoped></style>
