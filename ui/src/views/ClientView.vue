<script lang="ts" setup>
import BaseLayoutContainer from '@/components/layout/BaseLayoutContainer.vue'
import { defineAsyncComponent, ref, useTemplateRef } from 'vue'
import GridComponent from '@/components/general/grid/GridComponent.vue'
import type { Client, ClientFiltersInput } from '@/gql/graphql.ts'
import type { ColumnType } from '@/components/general/grid/utils/types.ts'
import PaginationComponent from '@/components/general/grid/PaginationComponent.vue'
import { notification } from '@/utils/notifications.ts'
import { EnvelopeIcon, MapPinIcon, PhoneIcon, UserGroupIcon } from '@heroicons/vue/24/outline'
import HistoryCell from '@/components/general/grid/cells/HistoryCell.vue'
import type { ReturnFieldProp } from '@/utils/queryMethods.ts'
import { HISTORY_FIELDS } from '@/utils/constants/grid.ts'
import SearchComponent from '@/components/general/SearchComponent.vue'
import { cloneDeep } from 'lodash-es'

const ClientForm = defineAsyncComponent(() => import('@/components/features/client/ClientForm.vue'))

const gridColumns: ColumnType<Client>[] = [
  {
    key: 'actions',
    title: '',
    width: 80,
  },
  {
    key: 'name',
    title: 'Name',
  },
  {
    key: 'contact',
    title: 'Contact',
  },
  {
    key: 'Sites',
    title: 'Sites',
  },
  {
    key: 'Groups',
    title: 'Groups',
  },
  {
    key: 'history',
    title: 'History',
  },
]

const paginationFields: ReturnFieldProp = ['name', 'contact', 'phone', { Sites: ['documentId'] }, { Groups: ['documentId'] }, ...HISTORY_FIELDS]

// grid
const loading = ref(true)
const facilities = ref<Client[]>([])
const showForm = ref(false)
const selectedRow = ref<Client>()

//pagination
const paginationRef = useTemplateRef('paginationRef')

// search
const searchFilters = ref<ClientFiltersInput>({})

function onPageChange(data: Client[]) {
  facilities.value = data
}

function onCreate() {
  paginationRef.value?.refresh()
  closeForm()
}

function onUpdate(data: Client) {
  const index = facilities.value.findIndex((item) => item.documentId === data.documentId)
  if (index !== -1) {
    facilities.value[index] = data
  }
  closeForm()
}

function editClient(rowData: Client) {
  const data = cloneDeep(rowData)
  const { Sites, Groups, ...cleanedData } = data
  selectedRow.value = cleanedData as Client
  showForm.value = true
}

function closeForm() {
  showForm.value = false
  selectedRow.value = undefined
}

function onSearchFiltersChange(filters: ClientFiltersInput) {
  searchFilters.value = filters
}
</script>

<template>
  <BaseLayoutContainer>
    <template #button-top>
      <div class="flex justify-between items-center w-full">
        <SearchComponent :loading="loading" model-name="Client" @filters-change="onSearchFiltersChange" />

        <MainButton label="Add new" size="small" @click="showForm = true"></MainButton>
      </div>
    </template>
    <template #form>
      <ClientForm v-if="showForm" :row-data="selectedRow" @close="closeForm" @create="onCreate" @update="onUpdate" />
    </template>

    <template #content>
      <GridComponent :columns="gridColumns" :data="facilities" :loading="loading" :selected-row-id="selectedRow?.documentId">
        <template #cell-name="{ rowData }: { rowData: Client }">
          <div :title="rowData.name">
            {{ rowData.name }}
          </div>
        </template>

        <template #cell-contact="{ rowData }: { rowData: Client }">
          <div class="flex flex-col gap-1">
            <div :title="rowData.contact" class="truncate flex items-center gap-1">
              <EnvelopeIcon class="size-4 text-slate-600" />
              <a :href="'mailto:' + rowData.contact" class="text-slate-600 hover:text-blue-500 truncate">
                {{ rowData.contact }}
              </a>
            </div>
            <div :title="rowData?.phone ?? ''" class="truncate flex items-center gap-0.5">
              <PhoneIcon v-if="rowData.phone" class="size-4 text-slate-600" />
              <a v-if="rowData.phone" :href="'tel:' + rowData.phone" class="text-slate-600 hover:text-blue-500">
                {{ rowData.phone }}
              </a>
            </div>
          </div>
        </template>

        <template #cell-Sites="{ rowData }: { rowData: Client }">
          <RouterLink
            :title="`View ${rowData.Sites.length} sites for ${rowData.name}`"
            :to="{
              name: 'Client Sites',
              query: {
                clientId: rowData.documentId,
                clientName: rowData.name,
              },
            }"
            class="flex items-center gap-0.5"
          >
            <MapPinIcon class="size-4" />
            <span>{{ rowData.Sites.length }}</span>
          </RouterLink>
        </template>

        <template #cell-Groups="{ rowData }: { rowData: Client }">
          <RouterLink
            :title="`View ${rowData.Groups.length} groups for ${rowData.name}`"
            :to="{
              name: 'Groups',
              query: {
                clientId: rowData.documentId,
                clientName: rowData.name,
              },
            }"
            class="flex items-center gap-0.5"
          >
            <UserGroupIcon class="size-4" />
            <span>{{ rowData.Groups.length }}</span>
          </RouterLink>
        </template>

        <template #cell-history="{ rowData }: { rowData: Client }">
          <HistoryCell :row-data="rowData" />
        </template>

        <template #cell-actions="{ rowData }: { rowData: Client }">
          <MainButton aria-label="Edit Client" icon="pi pi-pencil" severity="secondary" size="small" text @click="editClient(rowData)" />
        </template>

        <template #pagination>
          <PaginationComponent
            ref="paginationRef"
            :fields="paginationFields"
            :filters="searchFilters"
            model-name="Client"
            settings-id="Client_view"
            @error="notification('error', $event)"
            @loading="loading = $event"
            @page-change="onPageChange"
          />
        </template>
      </GridComponent>
    </template>
  </BaseLayoutContainer>
</template>

<style lang="scss" scoped></style>
