<script lang="ts" setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth.store'
import gql from 'graphql-tag'
import { useLazyQuery } from '@vue/apollo-composable'
import type { Query, QueryViewSettingsArgs } from '@/gql/graphql.ts'
import { notification } from '@/utils/notifications.ts'
import { useViewSettingsStore } from '@/stores/viewSettings.store.ts'
import Card from 'primevue/card'
import InputText from 'primevue/inputtext'
import Password from 'primevue/password'
import Message from 'primevue/message'
import FloatLabel from 'primevue/floatlabel'

const FETCH_VIEW_SETTINGS = gql`
  query getViewSettings {
    viewSettings {
      settings
      name
    }
  }
`

const router = useRouter()
const authStore = useAuthStore()
const { setInitialViewSettings } = useViewSettingsStore()

const identifier = ref('')
const password = ref('')
const errorMessage = ref('')
const isSubmitting = ref(false)

const { load: loadViewSettings } = useLazyQuery<Query, QueryViewSettingsArgs>(
  FETCH_VIEW_SETTINGS,
  {},
  {
    fetchPolicy: 'network-only',
  },
)

async function handleLogin() {
  if (!identifier.value || !password.value) {
    errorMessage.value = 'Please enter both email/username and password'
    return
  }

  isSubmitting.value = true

  try {
    errorMessage.value = ''
    await authStore.login({
      identifier: identifier.value,
      password: password.value,
    })
    authStore.loading = true
    console.log('Login successful')
    await getViewSettings()
    await router.push('/')
  } catch (error) {
    errorMessage.value = authStore.getError || (error as Error).message || 'Login failed. Please try again.'
    notification('error', errorMessage.value)
  } finally {
    isSubmitting.value = false
    authStore.loading = false
  }
}

async function getViewSettings() {
  try {
    const response = await loadViewSettings()

    if (!response) return
    if (response.viewSettings) {
      const settings = response.viewSettings?.[0]?.settings
      setInitialViewSettings(settings)
    }
  } catch (error) {
    console.error('Error fetching view settings:', error)
    notification('error', error as string)
  }
}
</script>

<template>
  <div class="min-h-screen flex items-center justify-center p-4">
    <div class="w-full max-w-md">
      <div class="text-center mb-8">
        <div class="mb-4">
          <div class="justify-center inline-flex">
            <img alt="logo" class="object-contain h-16 select-none bg-transparent" src="@/assets/image.png" style="mix-blend-mode: multiply" />
          </div>
        </div>
        <h1 class="text-3xl font-bold text-gray-900 mb-2 tracking-tight">Welcome Back</h1>
        <p class="text-gray-600 text-base">Sign in to your account to continue</p>
      </div>

      <Card class="shadow-2xl border-0 overflow-hidden backdrop-blur-sm bg-white/95 hover:shadow-3xl transition-all duration-300">
        <template #content>
          <form class="space-y-6 p-2" @submit.prevent="handleLogin">
            <Message v-if="errorMessage" :closable="false" class="mb-4 rounded-lg" severity="error">
              {{ errorMessage }}
            </Message>

            <div class="space-y-8">
              <div class="relative group">
                <FloatLabel class="w-full" variant="on">
                  <InputText
                    id="identifier"
                    v-model="identifier"
                    :class="{
                      'border-red-300 focus:border-red-500 focus:ring-red-100': errorMessage && !identifier,
                      'pl-4 pr-4': true,
                    }"
                    class="w-full h-14 text-lg rounded-lg border-2 border-gray-200 focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all duration-200 hover:border-gray-300"
                    required
                    size="small"
                    type="email"
                  />
                  <label class="text-gray-600 font-medium transition-all duration-200 group-hover:text-blue-600" for="identifier"> Email or Username </label>
                </FloatLabel>
              </div>

              <div class="relative group">
                <FloatLabel class="w-full" variant="on">
                  <Password
                    id="password"
                    v-model="password"
                    :class="{
                      'border-red-300 focus:border-red-500 focus:ring-red-100': errorMessage && !password,
                    }"
                    :feedback="false"
                    class="w-full"
                    input-class="w-full h-14 text-lg rounded-lg border-2 border-gray-200 focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all duration-200 hover:border-gray-300 pl-4 pr-12"
                    required
                    size="small"
                    toggle-mask
                  />
                  <label class="text-gray-600 font-medium transition-all duration-200 group-hover:text-blue-600" for="password"> Password </label>
                </FloatLabel>
              </div>
            </div>

            <MainButton
              :disabled="isSubmitting"
              :label="isSubmitting ? 'Signing in...' : 'Sign In'"
              :loading="isSubmitting"
              class="w-full h-14 text-lg font-semibold bg-gradient-to-r from-blue-600 to-purple-600 border-0 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-[1.02] hover:-translate-y-0.5 transition-all duration-200 focus:ring-4 focus:ring-blue-200"
              icon="pi pi-sign-in"
              icon-pos="right"
              size="small"
              type="submit"
            />
          </form>
        </template>
      </Card>
    </div>
  </div>
</template>

<style scoped>
@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
</style>
