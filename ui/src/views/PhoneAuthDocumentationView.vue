<script lang="ts" setup>
import { markRaw, ref } from 'vue'
import Accordion from 'primevue/accordion'
import AccordionPanel from 'primevue/accordionpanel'
import AccordionHeader from 'primevue/accordionheader'
import AccordionContent from 'primevue/accordioncontent'
import Badge from 'primevue/badge'
import { ClockIcon, ExclamationTriangleIcon, GlobeAltIcon, HashtagIcon, KeyIcon, PhoneIcon, PlayIcon, ShieldCheckIcon, SpeakerWaveIcon } from '@heroicons/vue/24/outline'

const phoneNumber = ref('(*************')

const faqData = markRaw([
  {
    id: '0',
    iconClass: 'bg-red-100 text-red-600',
    title: "I can't get through / Call doesn't connect",
    description: "If you're having trouble connecting:",
    tips: ["Verify you're calling from your registered phone number", 'Check your phone service connection', 'Try calling again in a few minutes'],
  },
  {
    id: '1',
    iconClass: 'bg-orange-100 text-orange-600',
    title: 'I forgot my PIN',
    description: "If you've forgotten your 4-digit PIN:",
    tips: ['Contact your healthcare provider to reset your PIN', 'Do not attempt to guess your PIN'],
  },
  {
    id: '2',
    iconClass: 'bg-yellow-100 text-yellow-600',
    title: 'The system hangs up too quickly',
    description: 'If the call ends too quickly:',
    tips: ['Be ready with your PIN before calling', "Ensure your phone's keypad tones are enabled"],
  },
])

const stepCards = markRaw([
  {
    number: 1,
    title: 'Call the Number',
    description: `Dial ${phoneNumber.value} from your registered phone number`,
    iconClass: 'bg-gradient-to-br from-blue-500 to-blue-600',
    badgeClass: 'bg-blue-100 text-blue-600',
    cardClass: 'bg-blue-50 border-blue-200 text-blue-800',
  },
  {
    number: 2,
    title: 'Select Language',
    description: 'Choose your preferred language within 6 seconds',
    iconClass: 'bg-gradient-to-br from-green-500 to-green-600',
    badgeClass: 'bg-green-100 text-green-600',
    cardClass: 'bg-green-50 border-green-200 text-green-800',
  },
  {
    number: 3,
    title: 'Enter PIN',
    description: 'Enter your 4-digit PIN followed by # within 10 seconds',
    iconClass: 'bg-gradient-to-br from-purple-500 to-purple-600',
    badgeClass: 'bg-purple-100 text-purple-600',
    cardClass: 'bg-purple-50 border-purple-200 text-purple-800',
  },
])

const infoCards = markRaw([
  {
    iconClass: 'bg-amber-100 text-amber-600',
    title: 'Time Limits',
    details: [
      { label: 'Language selection:', value: '6 seconds' },
      { label: 'PIN entry:', value: '10 seconds' },
    ],
  },
  {
    iconClass: 'bg-red-100 text-red-600',
    title: 'Maximum Attempts',
    description: 'You have 3 attempts to enter your PIN correctly. After 3 failed attempts, the call will end automatically.',
  },
  {
    iconClass: 'bg-blue-100 text-blue-600',
    title: 'Clear Audio',
    description: "Ensure you're in a quiet environment with good phone reception for the best experience.",
  },
])

const loadedPanels = ref(new Set())

const loadPanel = (panelId: string) => {
  loadedPanels.value.add(panelId)
}

const isPanelLoaded = (panelId: string) => loadedPanels.value.has(panelId)
</script>

<template>
  <div class="min-h-screen bg-slate-50">
    <div class="relative overflow-hidden">
      <div class="absolute inset-0 bg-gradient-to-r from-emerald-600 via-emerald-500 to-teal-500 gradient-bg"></div>

      <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="text-center">
          <div class="flex items-center justify-center mb-6">
            <PhoneIcon class="h-16 w-16 text-white animate-bounce-gentle" />
          </div>

          <h1 class="text-5xl md:text-6xl font-bold text-white mb-4 tracking-tight">Test Schedule Phone Line</h1>

          <p class="text-xl text-white max-w-3xl mx-auto">
            Call our automated phone system to get information about your upcoming test schedules. Follow these simple steps to verify your identity and receive your schedule
            information.
          </p>
        </div>
      </div>
    </div>

    <div class="py-20 px-4 sm:px-6 lg:px-8 relative">
      <div class="absolute inset-0 overflow-hidden pointer-events-none">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-emerald-100 rounded-full opacity-20 bg-decoration"></div>
        <div class="absolute top-96 -left-40 w-60 h-60 bg-teal-100 rounded-full opacity-20 bg-decoration"></div>
      </div>

      <div class="max-w-6xl mx-auto relative">
        <div class="text-center mb-16">
          <div class="inline-flex items-center gap-2 bg-emerald-100 text-emerald-700 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <PlayIcon class="h-4 w-4" />
            Get Started
          </div>
          <h2 class="text-3xl font-bold text-slate-900 mb-4">Three Simple Steps</h2>
          <p class="text-lg text-slate-600 max-w-2xl mx-auto">Access your test schedule information quickly and securely</p>
        </div>

        <div class="relative">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-12 md:gap-8 mb-20">
            <div v-for="step in stepCards" :key="step.number" class="group flex flex-col h-full">
              <div class="bg-white rounded-2xl p-8 shadow-lg border border-slate-100 flex-1 flex flex-col step-card">
                <div :class="['w-16 h-16 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300', step.iconClass]">
                  <PhoneIcon v-if="step.number === 1" class="h-8 w-8 text-white" />
                  <GlobeAltIcon v-else-if="step.number === 2" class="h-8 w-8 text-white" />
                  <KeyIcon v-else class="h-8 w-8 text-white" />
                </div>
                <div class="flex items-center gap-2 mb-3">
                  <span :class="['w-6 h-6 rounded-full flex items-center justify-center text-sm font-bold', step.badgeClass]">
                    {{ step.number }}
                  </span>
                  <h3 class="text-xl font-bold text-slate-900">{{ step.title }}</h3>
                </div>
                <p class="text-slate-600 mb-4 flex-1">{{ step.description }}</p>

                <div v-if="step.number === 1" :class="['border rounded-lg p-3 mt-auto', step.cardClass]">
                  <p class="font-medium">{{ phoneNumber }}</p>
                </div>
                <div v-else-if="step.number === 2" class="flex gap-2 mt-auto">
                  <div :class="['flex-1 border rounded-lg p-2 text-center', step.cardClass]">
                    <Badge class="mb-1" severity="success" value="1" />
                    <p class="text-sm font-medium">English</p>
                  </div>
                  <div :class="['flex-1 border rounded-lg p-2 text-center', step.cardClass]">
                    <Badge class="mb-1" severity="success" value="2" />
                    <p class="text-sm font-medium">Español</p>
                  </div>
                </div>
                <div v-else :class="['border rounded-lg p-3 flex items-center gap-2 mt-auto', step.cardClass]">
                  <HashtagIcon class="h-4 w-4" />
                  <p class="text-sm font-medium">Don't forget the #</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-gradient-to-r from-emerald-600 via-emerald-500 to-teal-500 rounded-2xl p-8 mb-20 text-white gradient-bg">
          <div class="flex items-start gap-4">
            <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center flex-shrink-0">
              <ShieldCheckIcon class="h-6 w-6 text-white" />
            </div>
            <div>
              <h3 class="text-xl font-bold mb-2">Important Security Notice</h3>
              <p class="text-white/90">
                You must call from the phone number registered in your patient account. This ensures your personal health information remains secure and protected.
              </p>
            </div>
          </div>
        </div>

        <div class="mb-20">
          <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-slate-900 mb-4">What You Need to Know</h2>
            <p class="text-lg text-slate-600">Important details for a smooth experience</p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div v-for="(card, index) in infoCards" :key="index" class="bg-white rounded-xl p-6 shadow-sm border border-slate-100">
              <div :class="['w-12 h-12 rounded-xl flex items-center justify-center mb-4', card.iconClass]">
                <ClockIcon v-if="index === 0" class="h-6 w-6" />
                <ExclamationTriangleIcon v-else-if="index === 1" class="h-6 w-6" />
                <SpeakerWaveIcon v-else class="h-6 w-6" />
              </div>
              <h4 class="font-bold text-slate-900 mb-2">{{ card.title }}</h4>

              <div v-if="card.details" class="space-y-2 text-sm text-slate-600">
                <div v-for="detail in card.details" :key="detail.label" class="flex justify-between">
                  <span>{{ detail.label }}</span>
                  <span class="font-medium">{{ detail.value }}</span>
                </div>
              </div>
              <p v-else class="text-sm text-slate-600">{{ card.description }}</p>
            </div>
          </div>
        </div>

        <div class="mb-20">
          <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-slate-900 mb-4">Frequently Asked Questions</h2>
            <p class="text-lg text-slate-600">Quick solutions to common issues</p>
          </div>

          <div class="bg-white rounded-2xl overflow-hidden accordion-container shadow-sm border border-slate-100 border-b-0">
            <Accordion>
              <AccordionPanel v-for="(faq, index) in faqData" :key="faq.id" :value="faq.id" class="accordion-panel">
                <AccordionHeader class="hover:bg-slate-50 accordion-header" @click="loadPanel(faq.id)">
                  <div class="flex items-center gap-3 py-2">
                    <div :class="['w-8 h-8 rounded-lg flex items-center justify-center', faq.iconClass]">
                      <ExclamationTriangleIcon v-if="faq.id === '0'" class="h-4 w-4" />
                      <KeyIcon v-else-if="faq.id === '1'" class="h-4 w-4" />
                      <ClockIcon v-else class="h-4 w-4" />
                    </div>
                    <span class="font-medium">{{ faq.title }}</span>
                  </div>
                </AccordionHeader>
                <AccordionContent
                  :pt="{
                    '!border-none': index === 2,
                  }"
                  class="accordion-content"
                >
                  <div v-if="isPanelLoaded(faq.id)" class="px-4 pb-6">
                    <div class="bg-slate-50 rounded-lg p-4 ml-11">
                      <p class="text-slate-700 mb-3">{{ faq.description }}</p>
                      <ul class="space-y-2 text-sm text-slate-600">
                        <li v-for="tip in faq.tips" :key="tip" class="flex items-start gap-2">
                          <div class="w-1.5 h-1.5 bg-slate-400 rounded-full mt-2 flex-shrink-0"></div>
                          <span>{{ tip }}</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionPanel>
            </Accordion>
          </div>
        </div>

        <div class="text-center">
          <div class="bg-gradient-to-r from-emerald-600 via-emerald-500 to-teal-500 rounded-2xl p-12 text-white gradient-bg">
            <div class="w-20 h-20 bg-white/10 rounded-2xl flex items-center justify-center mx-auto mb-6">
              <PhoneIcon class="h-10 w-10 text-white" />
            </div>
            <h2 class="text-2xl font-bold mb-4">Need Additional Help?</h2>
            <p class="text-white/90 mb-8 max-w-2xl mx-auto">
              If you continue to experience issues with the test schedule phone system, please contact your healthcare provider directly.
            </p>
            <div class="inline-flex items-center gap-4 bg-white/10 backdrop-blur rounded-xl p-6">
              <div class="text-left">
                <p class="text-white/80 text-sm">Test Schedule Phone Line</p>
                <p class="text-white font-mono text-2xl font-bold">{{ phoneNumber }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
@keyframes bounce-gentle {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

.animate-bounce-gentle {
  animation: bounce-gentle 2s infinite;
}

.accordion-container {
  contain: layout style paint;
}

.accordion-panel {
  will-change: auto;
  transform: translateZ(0);
}

.accordion-header {
  backface-visibility: hidden;
  transform: translateZ(0);
}

.accordion-content {
  transform: translateZ(0);
  backface-visibility: hidden;
  contain: layout style paint;
  transition: height 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  overflow: hidden;
}

:deep(.p-accordion-content) {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  transform: translateZ(0);
}

:deep(.p-accordion-panel) {
  transform: translateZ(0);
}

:deep(.p-accordion-header) {
  transform: translateZ(0);
}

:deep(.p-accordion-content-enter-active),
:deep(.p-accordion-content-leave-active) {
  transform: translateZ(0);
  will-change: height, opacity;
  transition:
    height 0.15s ease-out,
    opacity 0.15s ease-out !important;
}

:deep(.p-accordion-content-enter-from),
:deep(.p-accordion-content-leave-to) {
  opacity: 0;
  transform: translateZ(0);
}

.step-card {
  will-change: transform;
  transform: translateZ(0);
}

.bg-decoration {
  will-change: transform;
  transform: translateZ(0);
}

.gradient-bg {
  background-attachment: fixed;
  will-change: transform;
  transform: translateZ(0);
}

.group:hover .step-card {
  will-change: transform;
}

* {
  transition-property: transform, opacity, color, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-transform {
  transform: translateZ(0);
}
</style>
