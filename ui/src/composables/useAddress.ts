import { type Address, type Mutation, type MutationCreateAddressArgs, type MutationUpdateAddressArgs } from '@/gql/graphql'
import { Create, Update } from '@/utils/queryMethods'
import { useMutation } from '@vue/apollo-composable'

interface UseAddress {
  saveAddress: (address: Address) => Promise<Address | null>
}

/**
 * Composable function for creating or updating an address.
 * This can be used with the Address component or a dynamic form component.
 *
 * @property {Function} save - Saves the provided address entity. Creates a new address if no ID is present; otherwise, updates the existing address.
 *
 * @returns {UseAddress} An object containing the `save` function.
 */
function useAddress(): UseAddress {
  const { mutate: create } = useMutation<Mutation, MutationCreateAddressArgs>(Create('Address'))
  const { mutate: update } = useMutation<Mutation, MutationUpdateAddressArgs>(Update('Address'))

  async function saveAddress(address: Address): Promise<Address | null> {
    const addressValue = address?.address || {}
    const response = address?.documentId
      ? await update({
          documentId: address.documentId,
          data: { address: addressValue },
        })
      : await create({ data: { address: addressValue } })
    if (address?.documentId) {
      return response?.data?.updateAddress || null
    } else {
      return response?.data?.createAddress || null
    }
  }

  return {
    saveAddress,
  }
}

export default useAddress
