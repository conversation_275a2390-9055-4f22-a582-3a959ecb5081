export enum FieldType {
  Boolean = 'boolean',
  Number = 'number',
  Float = 'float',
  Decimal = 'decimal',
  Integer = 'integer',
  BigInteger = 'biginteger',
  Date = 'date',
  DateTime = 'datetime',
  Text = 'text',
  String = 'string',
  Email = 'email',
  Enumeration = 'enumeration',
  Relation = 'relation',
}

export enum FieldRelationType {
  OneToMany = 'oneToMany',
  ManyToOne = 'manyToOne',
  ManyToMany = 'manyToMany',
  OneToOne = 'oneToOne',
  Component = 'component',
  Scalar = 'SCALAR',
}

export const NumericFieldTypes: FieldType[] = [FieldType.Number, FieldType.Float, FieldType.Decimal, FieldType.Integer, FieldType.BigInteger]

export const DateFieldTypes: FieldType[] = [FieldType.Date, FieldType.DateTime]

export const TextFieldTypes: FieldType[] = [FieldType.Text, FieldType.String, FieldType.Email]

export const ScalarFieldTypes: FieldType[] = [...NumericFieldTypes, ...DateFieldTypes, ...TextFieldTypes, FieldType.Enumeration, FieldType.Boolean]

export const OneRelationFieldTypes: FieldRelationType[] = [FieldRelationType.ManyToOne, FieldRelationType.OneToOne]
export const ManyRelationFieldTypes: FieldRelationType[] = [FieldRelationType.OneToMany, FieldRelationType.ManyToMany]
