// src/utils/toastService.ts
import type { ToastServiceMethods } from 'primevue/toastservice'
import type { ToastMessageOptions } from 'primevue/toast'
import { upperFirst } from 'lodash-es'

let toast: ToastServiceMethods | null = null

export function setToastInstance(instance: ToastServiceMethods) {
  toast = instance
}

export function notification(type: ToastMessageOptions['severity'], detail: any): void {
  if (!toast) {
    console.warn('Toast instance not set. Call setToastInstance() in a component first.')
    return
  }
  toast.add({
    detail,
    life: 4000,
    summary: upperFirst(type),
    severity: type,
  })
}
