import { BroadcastChannel as BChannel } from 'broadcast-channel/types/broadcast-channel'

export declare interface PersistElement {
  key?: string
  storage: Storage
  stateVariables?: string[]
}

export declare interface PersistOptions {
  active: boolean
  persistOptions?: PersistElement[]
}

export declare interface SharedOptions {
  active: boolean
  stateVariables?: string[]
}

export declare interface LocalObject {
  sendFlag: boolean
  broadcast: BChannel
  storeVersion: number
}
