import 'pinia'
import type { PiniaPluginContext } from 'pinia'
import type { LocalObject, PersistElement, PersistOptions, SharedOptions } from './types'
import { BroadcastChannel as BChannel } from 'broadcast-channel'
import { debounce } from 'lodash-es'

type Store = PiniaPluginContext['store']
type PartialState = Partial<Store['$state']>

declare module 'pinia' {
  export interface DefineStoreOptionsBase<S, Store> {
    persist?: PersistOptions
    share?: SharedOptions
  }

  export interface PiniaCustomProperties {
    _asyncStorage?: Promise<string | null>
  }
}

export function piniaPersistShared(context: PiniaPluginContext): void {
  const { store, options } = context
  let promiseResolve: (value: string | PromiseLike<string>) => void
  const storeLocalVariables: LocalObject = {
    sendFlag: false,
    broadcast: new BChannel('shared-' + store.$id),
    storeVersion: 0,
  }

  if (options.persist?.active) {
    const defaultStorage: PersistElement[] = [
      {
        key: store.$id,
        storage: localStorage,
      },
    ]
    const debounceMethod = debounce(() => {
      shareStore(store, storeLocalVariables)
    }, 1000)

    const persistElements = options.persist?.persistOptions?.length ? options.persist.persistOptions : defaultStorage

    persistElements.forEach(async (persistElement: PersistElement) => {
      const storage = persistElement.storage || localStorage
      const storeKey = persistElement.key || store.$id
      const storageResult = storage.getItem(storeKey) || ''
      if (Object.prototype.toString.call(storageResult) === '[object Promise]') {
        store._asyncStorage = new Promise<string>((resolve) => {
          promiseResolve = resolve
        })
        let result: string | null = ''
        try {
          result = (await storageResult) as string
        } catch (err) {
          result = '{}'
        }
        !!result && store.$patch(JSON.parse(result as string))
        promiseResolve('')
      } else {
        !!storageResult && store.$patch(JSON.parse(storageResult as string))
      }
    })

    store.$subscribe(() => {
      if (options.share?.active && !storeLocalVariables.sendFlag) {
        debounceMethod()
      } else {
        storeLocalVariables.sendFlag = false
      }

      persistElements.forEach(async (persistElement: PersistElement) => {
        const storage = persistElement.storage || localStorage
        const storeKey = persistElement.key || store.$id
        await changeStorage(persistElement, store, storage, storeKey)
      })
    })
  }

  options.share?.active && listenForShareData(store, options.share.stateVariables, storeLocalVariables)
}

const changeStorage = async (persistElement: PersistElement, store: Store, storage: Storage, storeKey: string) => {
  if (persistElement?.stateVariables?.length) {
    const availableKeys = persistElement.stateVariables.filter((key) => Object.prototype.hasOwnProperty.call(store.$state, key))
    if (!availableKeys.length) return
    const partialState = availableKeys.reduce((stateObject, key) => {
      stateObject[key] = store.$state[key]

      return stateObject
    }, {} as PartialState)

    await storage.setItem(storeKey, JSON.stringify(partialState))
  } else {
    await storage.setItem(storeKey, JSON.stringify(store.$state))
  }
}

const shareStore = (store: Store, storeLocalVariables: LocalObject) => {
  storeLocalVariables.broadcast.postMessage({
    store: JSON.parse(JSON.stringify(store.$state)),
    storeId: store.$id,
    storeVersion: Date.now(),
  })
}

export const listenForShareData = (store: Store, selectedStateVariables: string[] = [], storeLocalVariables: LocalObject): void => {
  if (!store?.$id) return

  storeLocalVariables.broadcast.onmessage = (event: any) => {
    if (!event?.store) return
    if (event.storeId !== store.$id) return
    const isNewerVersionStore = event.storeVersion - storeLocalVariables.storeVersion > 0
    if (!isNewerVersionStore) return

    let state: PartialState = {}

    if (selectedStateVariables?.length) {
      state = selectedStateVariables.reduce((stateObject, key) => {
        if (Object.prototype.hasOwnProperty.call(event.store, key)) {
          stateObject[key] = event.store[key]
        }

        return stateObject
      }, {} as PartialState)
    } else {
      state = { ...event.store }
    }

    storeLocalVariables.sendFlag = true
    storeLocalVariables.storeVersion = event.storeVersion
    store.$patch(state)
  }
}
