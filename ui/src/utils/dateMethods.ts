import { type DateLike, useDateFormat, type UseDateFormatOptions } from '@vueuse/core'
import type { MaybeRefOrGetter } from 'vue'

/**
 A wrapper around useDateFormat to format a date.
 @param {DateLike} date - The date to format.
 @param {string} formatStr - The format string. Defaults to 'YYYY/MM/DD HH:mm:ss'.
 @param {UseDateFormatOptions} options - The options to pass to useDateFormat.
 @returns {string} The formatted date.
 */
export function formatDate(date: MaybeRefOrGetter<DateLike>, formatStr: MaybeRefOrGetter<string> = 'YYYY/MM/DD'): string {
  if (!date) return ''
  return useDateFormat(date, formatStr).value
}
