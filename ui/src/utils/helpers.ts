import { isEqual, isObject, transform } from 'lodash-es'
import type { AddressJsonType } from '@/utils/types.ts'

export function buildBaseBackendUrl(): string {
  const BACKEND_URL = import.meta.env.VITE_BACKEND_URL
  if (!BACKEND_URL) {
    throw new Error('Backend URL is not defined')
  }
  const BACKEND_PORT = import.meta.env.VITE_BACKEND_PORT !== '' ? `:${import.meta.env.VITE_BACKEND_PORT}` : ''
  return `${BACKEND_URL}${BACKEND_PORT}`
}

export function buildBackendUrl(path: string, queryParams = {}): string {
  const BACKEND_URL = buildBaseBackendUrl()
  const baseUrl = `${BACKEND_URL}/api${path}`
  const queryString = new URLSearchParams(queryParams).toString()
  return queryString ? `${baseUrl}?${queryString}` : baseUrl
}

export function findDifferences<T>(object: T, base: T): Partial<T> {
  return transform(
    object as any,
    (result, value, key) => {
      const baseValue = base[key as keyof T]

      // Check if value is a File
      if (value instanceof File || baseValue instanceof File) {
        if (!(value instanceof File && baseValue instanceof File) || value.name !== baseValue.name || value.size !== baseValue.size || value.type !== baseValue.type) {
          ;(result as any)[key] = value
        }
      }
      // Check if values are not equal
      else if (!isEqual(value, baseValue)) {
        // Check if value is an array
        if (Array.isArray(value) && Array.isArray(baseValue)) {
          const diff = value.filter((v) => !isEqual(v, baseValue))
          if (diff.length > 0) (result as any)[key] = diff
        }
        // If value is an object, recursively find differences
        else if (!Array.isArray(value)) {
          ;(result as any)[key] = isObject(value) && isObject(baseValue) ? findDifferences(value as any, baseValue as any) : value
        }
      }
    },
    {} as Partial<T>,
  )
}

export function getFormattedAddress(address: Partial<AddressJsonType>): string {
  const displayOrder = ['Street Address 1', 'Address 2', 'City', 'State', 'Postal Code', 'Country']
  switch (address?.Country ?? '') {
    case 'Canada':
      return displayOrder
        .filter((field) => !!address?.[field as keyof AddressJsonType])
        .map((field) => address?.[field as keyof AddressJsonType])
        .join(', ')
        .replace('Canada', 'CA')
    case 'United States':
      return displayOrder
        .filter((field) => !!address?.[field as keyof AddressJsonType])
        .map((field) => address?.[field as keyof AddressJsonType])
        .join(', ')
        .replace('United States', 'US')
    case 'United Kingdom':
      return displayOrder
        .filter((field) => !!address?.[field as keyof AddressJsonType])
        .map((field) => address?.[field as keyof AddressJsonType])
        .join(', ')
        .replace('United Kingdom', 'UK')
    default:
      return displayOrder
        .filter((field) => !!address?.[field as keyof AddressJsonType])
        .map((field) => address?.[field as keyof AddressJsonType])
        .join(', ')
  }
}
