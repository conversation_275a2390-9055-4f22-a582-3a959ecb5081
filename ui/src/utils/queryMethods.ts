import { useModelStore } from '@/stores/model.store.ts'
import { type Model } from '@/utils/types'
import * as gqlBuilder from 'gql-query-builder'
import type { DocumentNode } from 'graphql/language'
import gql from 'graphql-tag'
import { upperFirst } from 'lodash-es'
import { PaginationFields } from '@/utils/constants/enums.ts'
import type { ModelName } from '@/components/general/form/types.ts'
import { FieldRelationType } from '@/utils/constants/fieldTypes.ts'

interface ReturnFieldOpts {
  [p: string]: ReturnFieldProp
}

export type ReturnFieldProp = Array<string | ReturnFieldOpts>

// the recursive return‐fields shape: a list of either
//  • simple field names (string)
//  • nested objects mapping a relation/component name → sub-fields
//  • a meta entry with pagination info
export type ReturnFields = Array<
  | string
  | ReturnFieldNested // { relationName: ReturnFields }
  | ReturnFieldMeta // { meta: string[] }
>

// a nested relation or component pointer
export interface ReturnFieldNested {
  [fieldName: string]: ReturnFields
}

// the single “meta” entry we append for pagination
export interface ReturnFieldMeta {
  meta: string[]
}

/**
 * For a given model, build the two top‐level blocks:
 *   { nodes: [ …fields… ] }
 *   { pageInfo: [ page, pageSize, pageCount, total ] }
 */
export function buildConnectionReturnFields(model: Model, returnFields?: ReturnFieldProp) {
  return {
    nodes: buildReturnFields(model, 1, returnFields),
    pageInfo: [PaginationFields.Page, PaginationFields.PageSize, PaginationFields.PageCount, PaginationFields.Total],
  }
}

/**
 * Recursively builds a flat list of fields:
 *  • scalars ⇒ string
 *  • relations ⇒ { relationName: [sub-fields] }
 *  • components ⇒ { compName: ['documentId','value'] }
 */
export function buildReturnFields(model: Model, level = 1, returnFields?: ReturnFieldProp): Array<string | Record<string, any>> {
  const modelStore = useModelStore()

  // pick which fields we include
  const modelFields = model.fields.filter((f) => {
    if (f.name === 'documentId') return false
    if (!returnFields) {
      // by default, omit pure relations at top level
      return f.type !== 'relation'
    } else {
      return returnFields.some((r) => (typeof r === 'string' ? r === f.name : Object.prototype.hasOwnProperty.call(r, f.name)))
    }
  })

  // apply special default‐fields logic for UsersPermissions if you still need it…
  // (omitted here for brevity)

  const out: Array<string | Record<string, any>> = []

  for (const field of modelFields) {
    if (field.type === 'relation') {
      const subModel = modelStore.getModel(field.target!)
      if (!subModel) continue
      // see if user passed nested opts:  [{ comments: ['text','author'] }, …]
      const nestedOpts = (returnFields as ReturnFieldOpts[] | undefined)?.find((r) => typeof r !== 'string' && Object.keys(r)[0] === field.name)
      out.push({
        [field.name]: buildReturnFields(subModel, level + 1, nestedOpts?.[field.name] as ReturnFieldProp | undefined),
      })
    } else if (field.relationType === FieldRelationType.Component) {
      // Strapi components: always documentId+value
      out.push({ [field.name]: ['documentId', 'value'] })
    } else {
      out.push(field.name)
    }
  }

  // always include documentId
  return ['documentId', ...out]
}

/**
 * Constructs a GraphQL query for fetching multiple records of a model.
 *
 * @param {string} modelName - The name of the model.
 * @param {ReturnFieldProp} [returnFields] - Optional return fields to include. If not provided all scalars will be returned
 * @returns {DocumentNode} The constructed GraphQL query.
 * @throws Will throw an error if the model is not found.
 */
export function GetMany(modelName: ModelName, returnFields?: ReturnFieldProp): DocumentNode {
  const modelStore = useModelStore()
  const model = modelStore.getModel(modelName)
  if (!model) throw new Error(`Model ${modelName} not found`)
  const operation = `${model.pluralName}_connection`

  const options = {
    operation,
    variables: {
      filters: { type: `${modelName}FiltersInput` },
      pagination: { type: 'PaginationArg' },
      sort: { type: '[String]' },
    },
    fields: [buildConnectionReturnFields(model, returnFields)],
  }
  return gql`
    ${gqlBuilder.query(options).query}
  `
}

/**
 * Constructs a GraphQL query for fetching a single record of a model.
 *
 * @param {string} modelName - The name of the model.
 * @param {ReturnFieldProp} [returnFields] - Optional return fields to include. If not provided all scalars will be returned
 * @returns {DocumentNode} The constructed GraphQL query.
 * @throws Will throw an error if the model is not found.
 */
export function GetOne(modelName: ModelName, returnFields?: ReturnFieldProp): DocumentNode {
  const modelStore = useModelStore()
  const model = modelStore.getModel(modelName)
  if (!model) throw new Error(`Model ${modelName} not found`)
  const options = {
    operation: model.singularName,
    variables: {
      documentId: { type: 'ID!' },
    },
    fields: buildReturnFields(model, 1, returnFields),
  }

  return gql`
    ${gqlBuilder.query(options).query}
  `
}

/**
 * Constructs a GraphQL mutation for updating a record of a model.
 *
 * @param {string} modelName - The name of the model.
 * @param {ReturnFieldProp} [returnFields] - Optional return fields to include. If not provided all scalars will be returned
 * @returns {DocumentNode} The constructed GraphQL mutation.
 * @throws Will throw an error if the model is not found.
 */
export function Update(modelName: ModelName, returnFields?: ReturnFieldProp): DocumentNode {
  const modelStore = useModelStore()
  const model = modelStore.getModel(modelName)
  if (!model) throw new Error(`Model ${modelName} not found`)
  const options = {
    operation: `update${upperFirst(model.singularName)}`,
    variables: {
      documentId: { type: 'ID!' },
      data: { type: `${modelName}Input!` },
    },
    fields: buildReturnFields(model, 1, returnFields),
  }
  return gql`
    ${gqlBuilder.mutation(options).query}
  `
}

/**
 * Constructs a GraphQL mutation for creating a record of a model.
 *
 * @param {string} modelName - The name of the model.
 * @param {ReturnFieldProp} [returnFields] - Optional return fields to include. If not provided all scalars will be returned
 * @returns {DocumentNode} The constructed GraphQL mutation.
 * @throws Will throw an error if the model is not found.
 */
export function Create(modelName: ModelName, returnFields?: ReturnFieldProp): DocumentNode {
  const modelStore = useModelStore()
  const model = modelStore.getModel(modelName)
  if (!model) throw new Error(`Model ${modelName} not found`)
  const options = {
    operation: `create${upperFirst(model.singularName)}`,
    variables: {
      data: { type: `${modelName}Input!` },
    },
    fields: buildReturnFields(model, 1, returnFields),
  }
  return gql`
    ${gqlBuilder.mutation(options).query}
  `
}

/**
 * Constructs a GraphQL mutation for deleting a record of a model.
 *
 * @param {string} modelName - The name of the model.
 * @param {ReturnFieldProp} [returnFields] - Optional return fields to include. If not provided all scalars will be returned
 * @returns {DocumentNode} The constructed GraphQL mutation.
 * @throws Will throw an error if the model is not found.
 */
export function Delete(modelName: ModelName, returnFields?: ReturnFieldProp): DocumentNode {
  const modelStore = useModelStore()
  const model = modelStore.getModel(modelName)
  if (!model) throw new Error(`Model ${modelName} not found`)
  const options = {
    operation: `delete${upperFirst(model.singularName)}`,
    variables: {
      documentId: { type: 'ID!' },
    },
    fields: buildReturnFields(model, 1, returnFields),
  }
  return gql`
    ${gqlBuilder.mutation(options).query}
  `
}
