import gql from 'graphql-tag'

export const CREATE_VIEW_SETTINGS = gql`
  mutation createViewSetting($data: CustomViewSettingsInput!, $documentId: ID) {
    createViewSetting(data: $data, documentId: $documentId) {
      settings
      name
    }
  }
`

export const UPDATE_VIEW_SETTINGS = gql`
  mutation updateViewSetting($data: CustomViewSettingsInput!, $documentId: ID) {
    updateViewSetting(data: $data, documentId: $documentId) {
      settings
      name
    }
  }
`
