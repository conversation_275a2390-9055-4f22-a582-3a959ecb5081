import type { Maybe, UsersPermissionsUser } from '@/gql/graphql.ts'
import type { ReturnFieldProp } from '@/utils/queryMethods.ts'
import type { ModelName } from '@/components/general/form/types.ts'
import { FieldRelationType, type FieldType } from '@/utils/constants/fieldTypes.ts'

export interface GeneratedModelSchema {
  [key: string]: Model
}

export interface Model {
  name: string
  caption: string
  globalId: string
  singularName: string
  pluralName: string
  fields: FieldModel[]
}

export interface FieldModel {
  name: string
  type: FieldType
  required: boolean
  relationType: FieldRelationType
  unique: boolean
  enumValues?: string[]
  target?: string
  filters?: FieldFilter[]
}

export interface FieldFilter {
  name: string
  label: string
  type: string
}

interface RemoteSelectSections {
  name: string
  filters: any
}

export interface RemoteSelectProps {
  modelValue: any // The current value of the select input
  modelName: ModelName // The name of the model to query
  labelField?: string // The field to use for the option labels
  labelFunction?: (item: any) => string // A function to generate the option labels
  filters?: Maybe<any> // Filters to apply to the query
  multiple?: boolean // Whether multiple selections are allowed
  returnFields?: ReturnFieldProp // The fields to return from the query
  uniqueByKey?: string // A key to ensure uniqueness of options
  placeholder?: Maybe<string> // Placeholder text for the select input
  clearable?: boolean // Whether the select input is clearable
  valueKey?: string // The key to use for the option values
  disabled?: boolean // Whether the select input is disabled
  fitInputWidth?: boolean // Whether the input should fit the width of its content
  suffixIcon?: string // The icon to display in the input suffix
  sections?: RemoteSelectSections[] // custom sections to display above all section
  showSetFavorites?: boolean // Whether to show the set favorites button
  skipEntityStateFilter?: boolean
  sorter?: string // The field to sort the options by
  placement?: string // placement for select
  optionDisabled?: (option: any) => boolean // Function to determine if an option is disabled
}

export interface JSONObject {
  [key: string]: any
}

export interface BaseHistoryData {
  createdAt?: Date | string
  updatedAt?: Date | string
  CreatedByUser?: UsersPermissionsUser | null
  UpdatedByUser?: UsersPermissionsUser | null
}

export interface AddressJsonType {
  Country: string
  'Street Address 1': string
  'Address 2': string
  City: string
  State: string
  'Postal Code': string
}
