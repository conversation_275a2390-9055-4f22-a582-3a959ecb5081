{"name": "ui", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/", "predev": "graphql-codegen", "prebuild": "graphql-codegen"}, "dependencies": {"@apollo/client": "^3.13.8", "@heroicons/vue": "^2.2.0", "@primeuix/themes": "^1.1.1", "@tailwindcss/vite": "^4.1.5", "@vue/apollo-composable": "^4.2.2", "@vueuse/core": "^13.2.0", "apollo-upload-client": "^18.0.1", "axios": "^1.9.0", "broadcast-channel": "^7.1.0", "date-fns": "^4.1.0", "element-plus": "^2.9.10", "gql-query-builder": "^3.8.0", "localforage": "^1.10.0", "lodash-es": "^4.17.21", "pinia": "^3.0.1", "primeicons": "^7.0.0", "primevue": "^4.3.4", "sass": "^1.89.0", "streamsaver": "^2.0.6", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.5", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@graphql-codegen/cli": "^5.0.6", "@graphql-codegen/client-preset": "^4.8.1", "@graphql-codegen/introspection": "^4.0.3", "@tsconfig/node22": "^22.0.1", "@types/apollo-upload-client": "^18.0.0", "@types/lodash-es": "^4.17.12", "@types/node": "^22.15.19", "@types/streamsaver": "^2.0.5", "@vitejs/plugin-vue": "^5.2.3", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "eslint": "^9.22.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-vue": "~10.0.0", "jiti": "^2.4.2", "npm-run-all2": "^7.0.2", "prettier": "3.5.3", "typescript": "~5.8.0", "unplugin-element-plus": "^0.10.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}