import type { CodegenConfig } from '@graphql-codegen/cli'
import 'dotenv/config'

const schemaPath = process.env.VITE_SCHEMA_PATH
const config: CodegenConfig = {
  overwrite: true,
  schema: schemaPath,
  documents: ['src/**/*.{js,jsx,ts,tsx}'],
  generates: {
    'src/gql/': {
      preset: 'client',
      presetConfig: {
        gqlTagName: 'gql',
      },
      plugins: [],
      config: {
        useTypeImports: true,
      },
    },
  },
  ignoreNoDocuments: true,
}

export default config
